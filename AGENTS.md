# Agent Guidelines for Paibox App

## Build/Lint/Test Commands

- Build: `npm run build:ios:sim` (iOS Simulator)
- Start dev server: `npm start`
- Lint: `npm run lint` (fix errors)
- Type check: `npm run types:check`
- Test: `npm test` (all tests)
- Single test: `npm test -- [test-file-path]`
- E2E tests: `npm run test:maestro`

## Code Style Guidelines

### Formatting

- Prettier config: `prettier.config.mjs`
- Semi-colons: false
- Single quotes: true
- Trailing commas: none

### Imports

- Sort with `eslint-plugin-simple-import-sort`
- Groups: Node modules → React → Internal → Relative
- Remove unused imports automatically

### Types

- TypeScript strict mode
- Avoid `any` type
- Consistent type imports

### Naming

- PascalCase: components, types
- camelCase: functions, variables
- kebab-case: filenames

### Error Handling

- Use ErrorBoundary component
- Log errors to crash reporting
- User-friendly error messages

### Conventions

- Use Nativewind for styling
- Zustand for state management
- Follow Ignite boilerplate patterns
- Write tests in `__tests__` directories

Generated by [opencode](https://opencode.ai)
