{"name": "paibox-app", "version": "0.0.1", "private": true, "main": "expo-router/entry", "scripts": {"compile": "tsc --noEmit -p . --pretty", "lint": "eslint . --fix --ignore-pattern .gitignore --ignore-pattern ui/paibox-ui --cache", "lint:check": "eslint . --ignore-pattern .gitignore --ignore-pattern ui/paibox-ui --cache", "patch": "patch-package", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test -e MAESTRO_APP_ID=com.paiboxappignite -e IS_DEV=true .maestro/flows", "test:maestro:ci": "maestro test -e MAESTRO_APP_ID=com.paiboxappignite -e IS_DEV=false .maestro/flows", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "patch-package", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local", "start": "expo start --dev-client", "start:local": "cross-env EXPO_PUBLIC_NODE_ENV=localhost expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean", "types:check": "tsc --project tsconfig.json --pretty --noEmit --skipLib<PERSON><PERSON>ck", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "version": "npm run changelog && git add -A", "update:api:local": "openapi-typescript http://localhost:8080/api-docs.json -o ./src/services/api/schema.d.ts && eslint ./src/services/api/schema.d.ts --fix", "update:api": "openapi-typescript http://ae5c04cd9431c415bb543e735afcf4ae-858245834.us-east-1.elb.amazonaws.com/api-docs.json -o ./src/services/api/schema.d.ts && eslint ./src/services/api/schema.d.ts --fix", "update:dict-types": "node scripts/gen-dict-types.mjs && eslint ./src/types/dict.ts --fix"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "^14.1.0", "@expo/webpack-config": "^19.0.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-community/datetimepicker": "8.4.2", "@react-native-community/slider": "4.5.7", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/elements": "^2.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@roninoss/icons": "^0.0.4", "@shopify/flash-list": "1.8.3", "@shopify/react-native-skia": "^2.0.3", "@wuba/react-native-echarts": "^2.0.3", "ahooks": "^3.9.0", "apisauce": "3.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "echarts": "^5.6.0", "expo": "^53.0.12", "expo-application": "~6.1.4", "expo-blur": "^14.1.5", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.9", "expo-constants": "^17.1.6", "expo-contacts": "~14.2.5", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.2", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-image": "^2.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.5", "expo-live-photo": "~0.1.4", "expo-localization": "~16.1.5", "expo-navigation-bar": "~4.2.6", "expo-notifications": "~0.31.3", "expo-router": "~5.1.1", "expo-sms": "~13.1.4", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "^14.2.0", "i18next": "^25.2.1", "intl-pluralrules": "^2.0.1", "mobx-state-tree": "^7.0.2", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.59.0", "react-i18next": "^15.5.3", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-awesome-gallery": "^0.4.3", "react-native-chart-kit": "^6.12.0", "react-native-css-interop": "^0.1.22", "react-native-drawer-layout": "^4.1.11", "react-native-gesture-handler": "~2.26.0", "react-native-gifted-charts": "^1.4.61", "react-native-gifted-chat": "^2.8.1", "react-native-heroicons": "^4.0.0", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-controller": "^1.17.5", "react-native-mmkv": "3.2.0", "react-native-pager-view": "6.8.1", "react-native-reanimated": "3.18.0", "react-native-safe-area-context": "5.5.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.12.0", "react-native-swiper-flatlist": "^3.2.5", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.15.0", "react-photo-view": "^1.2.7", "tailwind-merge": "^3.3.1", "toastify-react-native": "^7.2.0", "victory-native": "^41.17.4", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/format": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@commitlint/types": "^19.8.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.1", "@testing-library/react-native": "^13.2.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^29.5.14", "@types/react": "~19.0.14", "@types/react-native-vector-icons": "^6.4.18", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "babel-jest": "^29.7.0", "conventional-changelog-cli": "^5.0.0", "cross-env": "^7.0.3", "devmoji": "^2.3.0", "eslint": "^9.30.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-reactotron": "^0.1.7", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "http-proxy-middleware": "^3.0.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-expo": "~53.0.5", "lint-staged": "^16.1.2", "nativewind": "^4.1.23", "openapi-fetch": "^0.14.0", "openapi-typescript": "^7.8.0", "patch-package": "^8.0.0", "postinstall-prepare": "2.0.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "react-refresh": "^0.17.0", "react-test-renderer": "19.0.0", "reactotron-core-client": "^2.9.7", "reactotron-react-js": "^3.3.16", "reactotron-react-native": "^5.1.14", "reactotron-react-native-mmkv": "^0.2.8", "sort-package-json": "^3.3.1", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}}