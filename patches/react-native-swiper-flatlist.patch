diff --git a/src/components/SwiperFlatList/SwiperFlatList.tsx b/src/components/SwiperFlatList/SwiperFlatList.tsx
index 9f6aaf08bda7469116d703e28f5cb46e46305e7b..ea1ffa64ea9edc15c70058d7e02062538d7fc351 100644
--- a/src/components/SwiperFlatList/SwiperFlatList.tsx
+++ b/src/components/SwiperFlatList/SwiperFlatList.tsx
@@ -79,7 +79,7 @@ export const SwiperFlatList = React.forwardRef(
     const _initialNumToRender = renderAll ? size : 1;
     const [currentIndexes, setCurrentIndexes] = React.useState({ index, prevIndex: index });
     const [ignoreOnMomentumScrollEnd, setIgnoreOnMomentumScrollEnd] = React.useState(false);
-    const flatListElement = React.useRef<RNFlatList<unknown>>(null);
+    const flatListElement = React.useRef<RNFlatList<unknown>>(null!);
     const [scrollEnabled, setScrollEnabled] = React.useState(!disableGesture);
 
     React.useEffect(() => {
