diff --git a/components/ToastManager.tsx b/components/ToastManager.tsx
index 8149099faaf8302e478f830b85c28f9b26ddde6e..7c8bd0fd4e441770e6526c0fcf01246daedb5094 100644
--- a/components/ToastManager.tsx
+++ b/components/ToastManager.tsx
@@ -15,8 +15,10 @@ import BaseToast from "./BaseToast";
 import styles from "./styles";
 
 class ToastManagerComponent extends Component<ToastManagerProps, ToastState> {
-  timerId: NodeJS.Timeout | null = null;
+  timerId: NodeJS.Timeout | number | null = null;
   animationRef: Animated.CompositeAnimation | null = null;
+  // FIXME
+  // @ts-expect-error
   static toastRef: RefObject<ToastRef> = createRef();
   static defaultProps = defaultProps;
 
