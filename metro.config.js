/* eslint-env node */
// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config')
const { withNativeWind } = require('nativewind/metro')
const { createProxyMiddleware } = require('http-proxy-middleware')

/** @type {import('expo/metro-config').MetroConfig} */
// FIXME
// @ts-expect-error
const config = getDefaultConfig(__dirname)
// FIXME
// @ts-expect-error
config.transformer.getTransformOptions = async () => ({
  transform: {
    // Inline requires are very useful for deferring loading of large dependencies/components.
    // For example, we use it in app.tsx to conditionally load Reactotron.
    // However, this comes with some gotchas.
    // Read more here: https://reactnative.dev/docs/optimizing-javascript-loading
    // And here: https://github.com/expo/expo/issues/27279#issuecomment-1971610698
    inlineRequires: true
  }
})
config.server.enhanceMiddleware = middleware => {
  return (req, res, next) => {
    if (req.url.startsWith('/api')) {
      return createProxyMiddleware({
        target:
          'http://ae5c04cd9431c415bb543e735afcf4ae-858245834.us-east-1.elb.amazonaws.com', // 替换为你的后端地址
        changeOrigin: true
      })(req, res, next)
    }
    return middleware(req, res, next)
  }
}

// This helps support certain popular third-party libraries
// such as Firebase that use the extension cjs.
// FIXME
// @ts-expect-error
config.resolver.sourceExts.push('cjs')

// FIXME
// @ts-expect-error
module.exports = withNativeWind(config, { input: './src/global.css' })
