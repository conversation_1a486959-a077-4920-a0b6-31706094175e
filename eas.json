{"cli": {"version": ">= 3.15.1", "appVersionSource": "remote"}, "build": {"development": {"extends": "production", "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug", "simulator": true}}, "development:device": {"extends": "development", "distribution": "internal", "ios": {"buildConfiguration": "Debug", "simulator": false}}, "preview": {"extends": "production", "distribution": "internal", "ios": {"simulator": true}, "android": {"buildType": "apk"}}, "preview:device": {"extends": "preview", "ios": {"simulator": false}}, "production": {}}, "submit": {"production": {}}}