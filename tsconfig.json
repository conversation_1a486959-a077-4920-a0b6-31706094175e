{
  "compilerOptions": {
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "jsx": "react-native",
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "moduleDetection": "force",
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUncheckedIndexedAccess": true,
    "sourceMap": true,
    "target": "esnext",
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "assets/*": ["./assets/*"]
    },
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": [
      "jest"
      // "nativewind/types",
      // "expo/types"
    ]
  },
  "extends": "expo/tsconfig.base",
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "test/**/*.ts",
    "test/**/*.tsx",
    "types/**/*.ts",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
    "nativewind-env.d.ts",
    "commitlint.config.js"
  ],
  "exclude": [
    "node_modules",
    "test/**/*",
    "**/node_modules/**",
    "dist",
    "build",
    ".expo",
    "coverage",
    "**/node_modules",
    "node_modules/**",
    "**/node_modules/**/*"
  ]
}
