name: CI/CD Pipeline

on:
  push:
    branches: ["develop"]
    paths-ignore:
      - "README.md"
  pull_request:
    branches: [develop]
    paths-ignore:
      - "README.md"

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: paibox-app
  IMAGE_TAG: test-${{ github.run_number }}
  EKS_CLUSTER: paibox-cluster

jobs:
  all-cli-checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm bundle:web

      - name: Run linter
        run: pnpm lint:check

      - name: Check types
        run: pnpm types:check
        # run: |
        #   sed -i 's/"typedRoutes": true/"typedRoutes": false/' app.json
        #   pnpm types:check
        #   sed -i 's/"typedRoutes": false/"typedRoutes": true/' app.json

      - name: Run tests
        run: pnpm test

  build-and-deploy:
    needs: all-cli-checks
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          # Generate timestamp in format YYYYMMDDHHMM
          TIMESTAMP=$(date +%Y%m%d%H%M)
          IMAGE_TAG="test-${TIMESTAMP}"

          echo "Building and pushing image with tag: $IMAGE_TAG"
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

          # Store the image tag for deployment step
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV

      - name: Install kubectl
        uses: azure/setup-kubectl@v3

      - name: Configure EKS credentials
        run: |
          aws eks --region $AWS_REGION update-kubeconfig --name $EKS_CLUSTER

      - name: Deploy to EKS
        run: |
          # Ensure namespace exists
          kubectl create namespace paibox --dry-run=client -o yaml | kubectl apply -f -

          # Export environment variables for envsubst
          export ECR_REGISTRY=${{ steps.login-ecr.outputs.registry }}
          export ECR_REPOSITORY=${{ env.ECR_REPOSITORY }}
          export IMAGE_TAG=${{ env.IMAGE_TAG }}

          # Apply base configurations with variable substitution
          envsubst < k8s/test/deployment.yaml | kubectl apply -f -
          kubectl apply -f k8s/test/service.yaml

          # Force restart deployment to ensure new image is used
          kubectl rollout restart deployment/paibox-app-test -n paibox

          # Debug information
          echo "Checking deployment status..."
          kubectl get pods -n paibox
          kubectl describe deployment paibox-app-test -n paibox

          # Wait for deployment to be ready
          echo "Waiting for deployment to be ready..."
          kubectl rollout status deployment/paibox-app-test -n paibox --timeout=900s

          # Print LoadBalancer address
          echo "LoadBalancer address:"
          kubectl get service paibox-app-test-service -n paibox -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'
          echo -e "\nService details:"
          kubectl get service paibox-app-test-service -n paibox

          # Verify image is correct
          echo -e "\nVerifying deployed image:"
          kubectl get deployment paibox-app-test -n paibox -o jsonpath='{.spec.template.spec.containers[0].image}'
          echo -e "\nExpected image: $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
