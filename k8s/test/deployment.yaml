apiVersion: apps/v1
kind: Deployment
metadata:
  name: paibox-app-test
  namespace: paibox
spec:
  replicas: 2
  progressDeadlineSeconds: 600
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: paibox-app-test
  template:
    metadata:
      labels:
        app: paibox-app-test
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: paibox-app-test
        image: ${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        env:
        - name: NODE_ENV
          value: "development"
        # TODO: Update API_URL to use the correct ELB
        - name: API_URL
          value: "http://ae5c04cd9431c415bb543e735afcf4ae-858245834.us-east-1.elb.amazonaws.com"
        - name: AWS_REGION
          value: "us-east-1" 
      volumes:
      - name: cloudwatch-config
        configMap:
          name: cloudwatch-config