apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: paibox-admin-hpa
  namespace: paibox
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: paibox-admin
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80