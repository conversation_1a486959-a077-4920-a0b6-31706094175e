# Cursor prompts

- Page generate

```
Generate corresponding React Native code based on the following HTML

## Requirements
- Use NativeWind for styling, use @/utils/classname for style combinations, use style for shadows and gradient background, and use classes for everything else
- Reference theme colors from @/theme/colors
- Use theme colors as tailwind colors, eg: text-primary, text-gray, text-dark, border-border
- Extract [xxx,xxx,xxx] into a separate components
- Using existed components ['xxx', 'xxx'] in @/components
- Use English for comments
- Use FontAwesome6 for icons
- Use native View and Text from `react-native`
- Don't use grid as rn don't support it

```html

```
```