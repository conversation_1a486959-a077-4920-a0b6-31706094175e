import type { Config } from 'tailwindcss'

// import type { PluginAPI } from 'tailwindcss/types/config'
import { Borders, Colors, Shadows } from './src/theme/colors'

// const LIGHT_GRAY = '#f3f4f6'
// const DARK = '#1f2937'
// const WARNING_BG = '#fef3c7'
// const WARNING_COLOR = '#f59e0b'
// const GRAY = '#6b7280'

export default {
  content: ['./src/**/*.{tsx,ts}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        ...Colors
      },
      boxShadow: {
        ...Shadows
      },
      borderRadius: {
        ...Borders
      }
    },
    plugins: [
      // function ({ addComponents }: PluginAPI) {
      //   addComponents({
      //     '.action-item': {
      //       [`@apply flex items-center px-0 py-[15px] border-b cursor-pointer transition-colors duration-200 no-underline`]:
      //         {},
      //       'border-bottom-width': '1px',
      //       'border-bottom-color': LIGHT_GRAY,
      //       'color': DARK
      //     },
      //     '.action-icon': {
      //       '@apply w-[40px] h-[40px] rounded-full flex items-center justify-center mr-[15px] text-[18px]':
      //         {}
      //     },
      //     '.action-icon.warning': {
      //       background: WARNING_BG,
      //       color: WARNING_COLOR
      //     },
      //     '.action-content': {
      //       '@apply flex-1': {}
      //     },
      //     '.action-arrow': {
      //       color: GRAY
      //     },
      //     '.action-title': {
      //       '@apply font-semibold mb-[2px]': {}
      //     },
      //     '.action-subtitle': {
      //       '@apply text-[14px]': {},
      //       'color': GRAY
      //     }
      //   })
      // }
    ]
  }
} satisfies Config
