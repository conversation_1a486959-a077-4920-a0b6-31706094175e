{
  "expo": {
    "name": "paibox-app",
    "slug": "paibox",
    "scheme": "paibox-app",
    "version": "1.0.0",
    "orientation": "portrait",
    "userInterfaceStyle": "automatic",
    "icon": "./assets/images/app-icon-all.png",
    "updates": {
      "fallbackToCacheTimeout": 0
    },
    "newArchEnabled": true,
    "jsEngine": "hermes",
    "assetBundlePatterns": [
      "**/*"
    ],
    "android": {
      "icon": "./assets/images/app-icon-android-legacy.png",
      "package": "com.paiboxappignite",
      "adaptiveIcon": {
        "foregroundImage": "./assets/images/app-icon-android-adaptive-foreground.png",
        "backgroundImage": "./assets/images/app-icon-android-adaptive-background.png"
      },
      "allowBackup": false,
      "edgeToEdgeEnabled": true
    },
    "ios": {
      "icon": "./assets/images/app-icon-ios.png",
      "supportsTablet": true,
      "bundleIdentifier": "com.paiboxappignite",
      "infoPlist": {
        "ITSAppUsesNonExemptEncryption": false
      }
    },
    "web": {
      "favicon": "./assets/images/app-icon-web-favicon.png",
      "bundler": "metro"
    },
    "plugins": [
      "expo-localization",
      "expo-font",
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/app-icon-android-adaptive-foreground.png",
          "imageWidth": 300,
          "resizeMode": "contain",
          "backgroundColor": "#191015"
        }
      ],
      [
        "expo-image-picker",
        {
          "photosPermission": "The app accesses your photos to let you share them with your friends."
        }
      ],
      [
        "expo-contacts",
        {
          "contactsPermission": "Allow $(PRODUCT_NAME) to access your contacts."
        }
      ],
      "expo-router",
      [
        "expo-notifications",
        {
          "color": "#ffffff",
          "defaultChannel": "default",
          // FIXME: add notification icon and sound
          // "icon": "./local/assets/notification_icon.png",
          // "sounds": [
          //   "./local/assets/notification_sound.wav",
          //   "./local/assets/notification_sound_other.wav"
          // ],
          "enableBackgroundRemoteNotifications": false
        }
      ],
      [
        "expo-camera",
        {
          "cameraPermission": "Allow $(PRODUCT_NAME) to access your camera",
          "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone",
          "recordAudioAndroid": true
        }
      ],
      [
        "expo-web-browser",
        {
          "experimentalLauncherActivity": true
        }
      ]
    ],
    "experiments": {
      "tsconfigPaths": true,
      "typedRoutes": true
    },
    "extra": {
      "router": {
        "origin": false
      },
      "eas": {
        "projectId": "444a1455-f594-4983-86ce-057e4259debb"
      }
    },
    "owner": "paibox"
  }
}