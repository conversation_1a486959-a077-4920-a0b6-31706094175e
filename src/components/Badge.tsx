import React from 'react'
import type { PressableProps } from 'react-native'
import { Pressable, Text } from 'react-native'
import type { ReactNode } from 'react'

import classNames from '@/utils/classname'

export type BadgeProps = {
  number: number
  max?: number
  trigger: ReactNode
  className?: string
}

const Badge = ({
  number,
  max = 99,
  trigger,
  className,
  ...props
}: BadgeProps & PressableProps) => {
  return (
    <Pressable
      className={classNames('relative inline w-fit', className)}
      {...props}
    >
      {trigger}
      {number > 0 && (
        <Text className="absolute -right-2 -top-2 h-[18px] min-w-[18px] items-center justify-center rounded-full bg-danger px-[6px] text-[10px] leading-[18px] text-white">
          {number > max ? `${max}+` : number}
        </Text>
      )}
    </Pressable>
  )
}

export { Badge }
