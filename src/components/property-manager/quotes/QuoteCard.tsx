import { Image, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { BorderCard } from '@/components/BorderCard'
import { But<PERSON> } from '@/components/Button'
import { Star } from '@/components/Star'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors, ShadowStyles } from '@/theme/colors'
import type { DICT_ITEM_ITEM_QUOTES_STATUS } from '@/types'
import classNames from '@/utils/classname'
import { formatDate } from '@/utils/formatDate'

type Quote = components['schemas']['ItemReviewQuotesDTO']

interface QuoteCardProps {
  quote: Quote
  onApproved?: () => void
  className?: string
}

export function QuoteCard({ quote, onApproved, className }: QuoteCardProps) {
  const statusColors: Record<
    DICT_ITEM_ITEM_QUOTES_STATUS,
    { bg: string; text: string }
  > = {
    PENDING_VENDOR: {
      bg: Colors['warning-light'],
      text: Colors.warning
    },
    PENDING_OWNER: {
      bg: Colors['warning-light'],
      text: Colors.warning
    },
    PENDING_MANAGER: {
      bg: Colors['warning-light'],
      text: Colors.warning
    },
    APPROVED: {
      bg: Colors['success-light'],
      text: Colors.success
    },
    REJECTED: {
      bg: Colors['danger-light'],
      text: Colors.danger
    },
    TERMINATE: {
      bg: Colors['danger-light'],
      text: Colors.danger
    }
  }
  const acceptRequest = useRequest(
    async () => {
      const { error } = await client.POST('/api/v1/pm/quotes/approve', {
        body: [
          {
            itemId: quote.itemId!,
            vendorId: quote.vendorId!,
            projectId: quote.projectId!
          }
        ]
      })
      return !error
    },
    {
      manual: true,
      onSuccess(v) {
        if (v) {
          Toast.success(`Vendor ${quote.vendorName}'s quote approved!`)
          onApproved?.()
        }
      }
    }
  )

  const onViewDetail = () => {
    router.push(
      `/property-manager/project/item-detail?id=${quote.itemId}&defaultVendorId=${quote.vendorId}`
    )
  }

  const { text, bg } =
    statusColors[quote.quoteStatus! as DICT_ITEM_ITEM_QUOTES_STATUS]

  return (
    <View
      className={classNames(
        'mb-4 rounded-xl border border-border bg-white p-4',
        className
      )}
      style={ShadowStyles.default}
    >
      {/* Quote Header */}
      <View className="mb-3 flex-row items-start justify-between">
        <View className="flex-1 flex-row gap-3">
          <Image
            source={{ uri: quote.vendorAvatar }}
            className="h-12 w-12 rounded-full"
          />
          <View className="flex-1">
            <Text className="mb-0.5 text-[15px] font-semibold text-dark">
              {quote.vendorName}
            </Text>
            <Text className="mb-1 text-xs text-gray">
              {quote.vendorServiceType}
            </Text>
            <View className="flex-row items-center gap-1">
              <View className="flex-row gap-0.5">
                <Star value={quote.vendorRating} size={10} allowHalf />
              </View>
              <Text className="text-gray-500 text-[11px]">
                {quote.vendorRating} ({quote.vendorReviewCount} reviews)
              </Text>
            </View>
          </View>
        </View>
        <View className="items-end">
          <View
            className="mb-1 rounded-xl px-2 py-1"
            style={{ backgroundColor: bg }}
          >
            <Text className="text-[11px] font-semibold" style={{ color: text }}>
              {quote.quoteStatus}
            </Text>
          </View>
          <Text className="text-[11px] text-gray">
            {formatDate(
              quote.updatedTime || quote.createdTime || quote.submittedTime
            )}
          </Text>
        </View>
      </View>

      {/* Quote Project */}
      <View className="mb-3 flex-row items-start justify-between border-b border-border pb-3">
        <View className="flex-1">
          <Text className="mb-1 text-sm font-semibold text-dark">
            {quote.itemName}
          </Text>
          <View className="flex-row items-center gap-1">
            <FontAwesome6 name="location-dot" size={12} color={Colors.gray} />
            <Text className="text-xs text-gray">{quote.streetAddress}</Text>
          </View>
        </View>
        <View className="items-end">
          <Text className="mb-0.5 text-base font-bold text-primary">
            ${quote.submittedQuote?.toLocaleString() || 'N/A'}
          </Text>
          <Text className="text-[11px] text-gray">
            vs ${quote.itemBudget?.toLocaleString()} budget
          </Text>
        </View>
      </View>
      {quote.itemDesc && (
        <BorderCard color={Colors.primary} position="left" className="bg-light">
          <Text className="text-sm text-dark">{quote.itemDesc}</Text>
        </BorderCard>
      )}
      <View className="mt-4 flex-row gap-3 border-t border-border pt-3">
        {quote.quoteStatus === 'PENDING_MANAGER' && (
          <Button
            className="flex-1"
            variant="primary"
            size="sm"
            onPress={acceptRequest.runAsync}
            loading={acceptRequest.loading}
          >
            Accept
          </Button>
        )}
        <Button
          className="flex-1"
          variant="default"
          size="sm"
          onPress={onViewDetail}
        >
          View Details
        </Button>
      </View>
    </View>
  )
}
