import { useEffect } from 'react'
import { Text, View } from 'react-native'

import SectionTitle from '@/components/SectionTitle'
import { usePMStat } from '@/store/pmStat'
import { crossPlatformShadow } from '@/theme/shadow'

interface OverviewCardProps {
  value?: number
  label?: string
}

function OverviewCard({ value, label }: OverviewCardProps) {
  return (
    <View className="w-[48%]">
      <View
        className="rounded-lg bg-white p-4"
        style={[crossPlatformShadow('sm')]}
      >
        <Text className="mb-1 text-center text-2xl font-bold text-primary">
          {value}
        </Text>
        <Text className="text-center text-xs text-gray">{label}</Text>
      </View>
    </View>
  )
}

export function PropertyOverview() {
  const {
    totalPropertyCount,
    activeProjectCount,
    pendingQuotesCount,
    completeThisMonthProjectCount,
    refresh
  } = usePMStat()

  const overviewData = [
    {
      value: totalPropertyCount || 0,
      label: 'Total Properties'
    },
    {
      value: activeProjectCount || 0,
      label: 'Active Projects'
    },
    {
      value: pendingQuotesCount || 0,
      label: 'Pending Quotes'
    },
    {
      value: completeThisMonthProjectCount || 0,
      label: 'Completed This Month'
    }
  ]

  useEffect(() => {
    refresh()
  }, [])

  return (
    <View className="mb-6">
      <SectionTitle title="Properties Overview" />

      <View className="flex-row flex-wrap gap-3">
        {overviewData.map((item, index) => (
          <OverviewCard key={index} value={item.value} label={item.label} />
        ))}
      </View>
    </View>
  )
}
