import { forwardRef, memo, useImperativeHandle, useRef } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useInfiniteScroll } from 'ahooks'

import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'
import { formatDate } from '@/utils/formatDate'

// Activity type to icon and color mapping
const getActivityIconConfig = (
  type: string | undefined
): { icon: string; color: string } => {
  switch (type?.toLowerCase()) {
    case 'painting':
      return { icon: 'paint-roller', color: Colors.warning }
    case 'installation':
      return { icon: 'tools', color: Colors.primary }
    case 'quote':
    case 'review':
      return { icon: 'quote-right', color: Colors.info }
    case 'maintenance':
      return { icon: 'wrench', color: Colors.danger }
    case 'project':
      return { icon: 'folder', color: Colors.success }
    case 'tenant':
      return { icon: 'user', color: Colors.primary }
    case 'property':
      return { icon: 'home', color: Colors.info }
    case 'payment':
      return { icon: 'credit-card', color: Colors.success }
    default:
      return { icon: 'calendar', color: Colors.gray }
  }
}

// Format time for display
const formatActivityTime = (dateString: string | undefined): string => {
  if (!dateString) return 'Unknown time'

  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`

    return formatDate(dateString, 'MMM dd')
  } catch {
    return 'Unknown time'
  }
}

// Activity item component props interface
interface ActivityItemProps {
  icon: string
  iconColor: string
  title: string
  details: string
  time: string
  showBorder?: boolean
}

// Activity item component
const ActivityItem = memo(function ActivityItem({
  icon,
  iconColor,
  title,
  details,
  time,
  showBorder = true
}: ActivityItemProps) {
  return (
    <View
      className={classNames(
        'flex-row items-start py-3',
        showBorder ? 'border-b border-b-border' : ''
      )}
    >
      <View
        className={classNames(
          'mr-3 h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-gray-100'
        )}
      >
        <FontAwesome6 name={icon} size={16} color={iconColor} />
      </View>
      <View className="flex-1">
        <Text className="mb-1 font-semibold text-dark">{title}</Text>
        <Text className="mb-1 text-xs leading-[1.4] text-gray">{details}</Text>
        <Text className="text-[11px] text-gray">{time}</Text>
      </View>
    </View>
  )
})

export interface RecentActivitiesRef {
  loadMore: () => Promise<void>
  canLoadMore: () => boolean
}

export const RecentActivities = memo(
  forwardRef<RecentActivitiesRef>((props, ref) => {
    const loadingRef = useRef(false)

    const request = useInfiniteScroll(
      async (prev?: {
        list?: components['schemas']['ActivityDTO'][]
        page?: number
        lastPageSize?: number
      }) => {
        console.log(
          '🔄 RecentActivities API Request - Page:',
          (prev?.page || 0) + 1,
          'Timestamp:',
          new Date().toISOString()
        )

        const { data } = await client.POST('/api/v1/pm/activity', {
          body: {
            pageNum: (prev?.page || 0) + 1,
            pageSize: 10,
            sortBy: 'createdTime',
            isAsc: '0'
          }
        })
        const currentPageData = data?.data || []

        return {
          list: [...(prev?.list || []), ...currentPageData],
          page: (prev?.page || 0) + 1,
          lastPageSize: currentPageData.length
        }
      },
      {
        manual: false,
        isNoMore: prev => {
          if (!prev || !prev.list || prev.list.length === 0) {
            return false
          }
          const pageSize = 10
          const hasNoMore = (prev.lastPageSize || 0) < pageSize
          return hasNoMore
        }
      }
    )

    useImperativeHandle(
      ref,
      () => ({
        loadMore: async () => {
          if (loadingRef.current || request.loading || request.noMore) {
            return
          }
          loadingRef.current = true
          try {
            await request.loadMoreAsync()
          } finally {
            loadingRef.current = false
          }
        },
        canLoadMore: () =>
          !request.loading && !request.noMore && !loadingRef.current
      }),
      [request.loadMoreAsync, request.loading, request.noMore]
    )

    return (
      <View className="mb-6">
        <SectionTitle
          title="Recent Activities"
          // href="/property-manager/(tabs)/home"
          // linkText="View All"
        />
        <View>
          {request.data?.list?.map((activity, index) => {
            const iconConfig = getActivityIconConfig(activity.activityType)
            return (
              <ActivityItem
                key={`${activity.title}-${index}`}
                showBorder={index !== (request.data?.list ?? []).length - 1}
                icon={iconConfig.icon}
                iconColor={iconConfig.color}
                title={activity.title || 'Activity'}
                details={activity.description || 'No description available'}
                time={formatActivityTime(activity.createdTime)}
              />
            )
          })}
          {request.loading && (
            <Text className="py-2 text-center text-sm text-gray">
              Loading more...
            </Text>
          )}
          {request.noMore &&
            request.data?.list &&
            request.data.list.length > 0 && (
              <Text className="py-2 text-center text-sm text-gray">
                No more activities
              </Text>
            )}
          {!request.loading &&
            (!request.data?.list || request.data.list.length === 0) && (
              <Text className="py-4 text-center text-gray">
                No recent activities
              </Text>
            )}
        </View>
      </View>
    )
  })
)
