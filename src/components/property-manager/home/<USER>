import { Image, Text, View } from 'react-native'
import { format } from 'date-fns'

import { useAuth } from '@/store'

export function UserInfo() {
  const today = new Date()
  const formattedDate = format(today, 'EEEE, MMMM d, yyyy')
  const user = useAuth(state => state.user)

  return (
    <View className="mb-6 flex-row items-center justify-between">
      <View className="flex-1">
        <Text className="text-xl font-bold">
          Welcome back, {user?.userName}!
        </Text>
        <Text className="text-sm text-gray">{formattedDate}</Text>
      </View>
      <View className="ml-auto">
        <Image
          source={{
            uri:
              user?.avatar || 'https://randomuser.me/api/portraits/men/44.jpg'
          }}
          className="h-[50px] w-[50px] rounded-full"
          alt="Profile"
          resizeMode="cover"
        />
      </View>
    </View>
  )
}
