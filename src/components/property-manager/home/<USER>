import { Text, TouchableOpacity, View } from 'react-native'
import { useRequest } from 'ahooks'
import { router } from 'expo-router'

import { Progress } from '@/components'
import PhotoSwiper from '@/components/PhotoSwiper'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'
import { Colors, ShadowStyles } from '@/theme/colors'
import type { DICT_ITEM_PROJECT_STATUS, PropertyType } from '@/types'
import classNames from '@/utils/classname'

interface PropertyCardProps {
  property: PropertyType
}

function PropertyCard({ property }: PropertyCardProps) {
  const statusConfig: Record<
    DICT_ITEM_PROJECT_STATUS,
    {
      bg: string
      text: string
      color: string
    }
  > = {
    DRAFT: {
      bg: 'bg-primary-light',
      text: 'text-primary',
      color: Colors.primary
    },
    PENDING_QUOTES: {
      bg: 'bg-warning-light',
      text: 'text-warning',
      color: Colors.warning
    },
    IN_PROGRESS: {
      bg: 'bg-info-light',
      text: 'text-info',
      color: Colors.info
    },
    CANCELLED: {
      bg: 'bg-danger-light',
      text: 'text-danger',
      color: Colors.danger
    },
    SUBMITTED: {
      bg: 'bg-tenant-light',
      text: 'text-tenant',
      color: Colors.tenant
    },
    COMPLETED: {
      bg: 'bg-success-light',
      text: 'text-success',
      color: Colors.success
    }
  }

  const config =
    statusConfig[property.status as DICT_ITEM_PROJECT_STATUS] ||
    statusConfig.DRAFT
  const completedPercent =
    property.completedPercent && property.completedPercent !== '-'
      ? parseInt(property.completedPercent.replace(/%$/, ''))
      : 0
  return (
    <TouchableOpacity
      onPress={() =>
        router.push(`/property-manager/property/${property.propertyId}`)
      }
      className="mb-4 overflow-hidden rounded-lg bg-white"
      style={ShadowStyles.sm}
    >
      <View className="relative h-[120px]">
        <PhotoSwiper
          photos={property.photos?.map(i => i.fileUrl!) || []}
          imageHeight={120}
          imageClassName="h-full w-full"
        />
        <View
          className={classNames(
            'absolute right-2.5 top-2.5 rounded px-2 py-1',
            config.bg
          )}
        >
          <Text className={classNames('text-xs font-medium', config.text)}>
            {property.status}
          </Text>
        </View>
      </View>

      <View className="p-3">
        <Text className="mb-1 text-base font-semibold text-dark">
          {property.propertyName}
        </Text>
        <Text className="mb-2 text-xs text-gray">
          {property.streetAddress},{property.completedPercent}
        </Text>

        <View className="mt-2">
          <Progress percentage={completedPercent} color={config.color} />
          <View className="flex flex-row justify-end">
            <Text className="mt-1 text-xs text-gray">
              {`${completedPercent}% Complete`}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}

export function RecentProperties() {
  const request = useRequest(async () => {
    const { data } = await client.POST('/api/v1/pm/property/list', {
      body: {
        pageNum: 1,
        pageSize: 3,
        sortBy: 'updatedTime',
        isAsc: '0'
      }
    })
    return data?.data?.list ?? []
  })

  return (
    <View className="mb-6">
      <SectionTitle
        title="Recent Properties"
        href="/property-manager/properties"
        linkText="View All"
      />
      {request.data?.map(property => (
        <PropertyCard key={property.propertyId} property={property} />
      ))}
    </View>
  )
}
