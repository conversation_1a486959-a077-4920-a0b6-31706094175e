import { Fragment } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { router } from 'expo-router'

import SectionTitle from '@/components/SectionTitle'
import { Colors } from '@/theme/colors'

interface QuickActionItemProps {
  icon: string
  text: string
  colors: [string, string]
  onPress: () => void
}

function QuickActionItem({
  icon,
  text,
  colors,
  onPress
}: QuickActionItemProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="flex flex-1 flex-col items-center"
    >
      <View className="relative mb-3 h-14 w-14 items-center justify-center overflow-hidden rounded-2xl shadow-md">
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ position: 'absolute', width: '100%', height: '100%' }}
        />
        <View className="absolute left-0 top-0 h-[30%] w-full rounded-[50%_50%_100%_100%_/_25%_25%_50%_50%] bg-white/20" />
        <FontAwesome6 name={icon} size={24} color={Colors.white} />
      </View>
      <Text className="text-center text-xs font-medium text-dark">{text}</Text>
    </TouchableOpacity>
  )
}

export function QuickActions() {
  const actions = [
    {
      icon: 'circle-plus',
      text: 'Add Property',
      colors: [Colors.primary, '#3b5fe2'] as [string, string],
      onPress: () => router.push('/property-manager/property/create')
    },
    {
      icon: 'wrench',
      text: 'Add Project',
      colors: [Colors.success, '#1f9d57'] as [string, string],
      onPress: () => router.push('/property-manager/project/create')
    },
    {
      icon: 'clipboard-list',
      text: 'Pending Quotes',
      colors: [Colors.info, '#0097a7'] as [string, string],
      onPress: () =>
        router.push('/property-manager/projects?projectStatus=PENDING_QUOTES')
    },
    {
      icon: 'file',
      text: 'Draft',
      colors: [Colors.warning, '#ff7e1a'] as [string, string],
      onPress: () => router.push('/property-manager/draft/list')
    }
  ]

  return (
    <Fragment>
      <SectionTitle title="Quick Actions" />
      <View className="mb-6 flex flex-row gap-3">
        {actions.map((action, index) => (
          <QuickActionItem key={index} {...action} />
        ))}
      </View>
    </Fragment>
  )
}
