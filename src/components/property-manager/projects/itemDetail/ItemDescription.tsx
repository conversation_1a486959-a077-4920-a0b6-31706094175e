import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import type { FC } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface ItemDescriptionProps {
  description: string
  isExpanded: boolean
  onToggleExpand: () => void
}

export const ItemDescription: FC<ItemDescriptionProps> = ({
  description,
  isExpanded,
  onToggleExpand
}) => {
  return (
    <View
      className="mx-4 mb-4 rounded-lg bg-white p-4"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 3
      }}
    >
      <Text className="mb-4 text-lg font-semibold text-dark">Description</Text>
      <View
        className={classNames(
          'relative',
          isExpanded ? '' : 'h-16 overflow-hidden'
        )}
      >
        <Text className="mb-3 text-sm text-gray">{description}</Text>
        {!isExpanded && (
          <LinearGradient
            colors={['rgba(255,255,255,0)', 'rgba(255,255,255,1)']}
            className="absolute bottom-0 left-0 right-0 h-8"
          />
        )}
      </View>
      <View className="mt-2 items-center">
        <Pressable onPress={onToggleExpand}>
          <View className="flex-row items-center">
            <Text className="mr-1 text-sm text-primary">
              {isExpanded ? 'Show Less' : 'Read More'}
            </Text>
            <FontAwesome6
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={12}
              color={Colors.primary}
            />
          </View>
        </Pressable>
      </View>
    </View>
  )
}

export default ItemDescription
