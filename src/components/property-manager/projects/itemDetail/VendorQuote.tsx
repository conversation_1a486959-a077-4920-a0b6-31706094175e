import React, { useState } from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRouter } from 'expo-router'
import type { FC } from 'react'
import { Toast } from 'toastify-react-native'

import { But<PERSON> } from '@/components/Button'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import type { DICT_ITEM_ITEM_QUOTES_STATUS } from '@/types/dict'
import classNames from '@/utils/classname'

import { StatusSteps } from './StatusSteps'
import type { StatusBadge } from './types'

interface VendorQuoteProps {
  quote: components['schemas']['ItemQuotes']
  isExpanded: boolean
  onToggle: () => void
  projectId?: number
  itemId?: number
  onQuoteApproved?: () => void
}

export const VendorQuote: FC<VendorQuoteProps> = ({
  quote,
  isExpanded,
  onToggle,
  projectId,
  itemId,
  onQuoteApproved
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [approvingQuote, setApprovingQuote] = useState(false)
  const [decliningQuote, setDecliningQuote] = useState(false)
  const router = useRouter()

  // Helper function to map quote status to display status
  const getQuoteDisplayStatus = (
    quoteStatus?: string
  ): DICT_ITEM_ITEM_QUOTES_STATUS => {
    // Cast to the correct type and provide fallback
    return (quoteStatus as DICT_ITEM_ITEM_QUOTES_STATUS) || 'PENDING_VENDOR'
  }

  // Handle quote approval/decline
  const handleQuoteAction = async (
    approveStatus: string,
    approveAmount?: number
  ) => {
    if (!projectId || !itemId || !quote.vendorId) {
      Toast.error('Missing required information')
      return
    }

    // Set the appropriate loading state
    const isApproving = approveStatus === 'APPROVED'
    if (isApproving) {
      setApprovingQuote(true)
    } else {
      setDecliningQuote(true)
    }
    setIsSubmitting(true)

    try {
      // Prepare approval data
      const approvalData: components['schemas']['PMItemQuotesApproveDTO'][] = [
        {
          projectId,
          itemId,
          vendorId: quote.vendorId,
          approveAmount: approveAmount || quote.submittedQuote || 0,
          approveStatus,
          description:
            approveStatus === 'APPROVED' ? 'Quote approved' : 'Quote declined'
        }
      ]

      // Call API
      const response = await client.POST('/api/v1/pm/quotes/approve', {
        body: approvalData
      })
      if (response.data?.code === 200) {
        Toast.success(`Quote ${approveStatus.toLowerCase()} successfully!`)
        onQuoteApproved?.()
      } else {
        console.log('res22', response)
        Toast.error(
          response.error?.message ||
            `Failed to ${approveStatus.toLowerCase()} quote`
        )
      }
    } catch (error) {
      console.error('Quote action error:', error)
      Toast.error(
        `Failed to ${approveStatus.toLowerCase()} quote. Please try again.`
      )
    } finally {
      setIsSubmitting(false)
      setApprovingQuote(false)
      setDecliningQuote(false)
    }
  }
  const getStatusBadge = (
    status: DICT_ITEM_ITEM_QUOTES_STATUS
  ): StatusBadge => {
    switch (status) {
      case 'APPROVED':
        return {
          text: status,
          bg: Colors['success-light'],
          color: Colors.success,
          borderColor: Colors.success
        }
      case 'PENDING_MANAGER':
        return {
          text: status,
          bg: Colors['warning-light'],
          color: Colors.warning,
          borderColor: Colors.warning
        }
      case 'PENDING_OWNER':
        return {
          text: status,
          bg: '#f3e8ff',
          color: '#9c27b0',
          borderColor: '#9c27b0'
        }
      case 'REJECTED':
        return {
          text: status,
          bg: Colors['danger-light'],
          color: Colors.danger,
          borderColor: Colors.danger
        }
      case 'PENDING_VENDOR':
      default:
        return {
          text: status,
          bg: Colors.light,
          color: Colors.gray,
          borderColor: Colors.gray
        }
    }
  }

  const displayStatus = getQuoteDisplayStatus(quote.quoteStatus)
  const statusBadge = getStatusBadge(displayStatus)
  // Note: budget is not available in ItemQuotes, we'll need to get it from parent component
  const budget = 0 // TODO: Pass budget from parent component
  const difference = (quote.submittedQuote || 0) - budget
  const percentDiff =
    budget > 0 ? ((difference / budget) * 100).toFixed(1) : '0'

  return (
    <View
      className="overflow-hidden rounded-lg bg-white"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 3,
        borderLeftWidth: 4,
        borderLeftColor: statusBadge.borderColor
      }}
    >
      {/* Vendor Header */}
      <Pressable onPress={onToggle}>
        <View className="flex-row items-center justify-between border-b border-border p-3">
          <View className="flex-row items-center gap-3">
            <Image
              source={{
                uri:
                  quote.vendorAvatar ||
                  'https://randomuser.me/api/portraits/men/32.jpg'
              }}
              className="h-12 w-12 rounded-full"
            />
            <View>
              <Text className="font-semibold text-dark">
                {quote.vendorBusinessName ||
                  quote.vendorName ||
                  'Unknown Vendor'}
              </Text>
              <Text className="text-xs text-gray">Contractor</Text>
            </View>
          </View>
          <View className="items-end gap-1">
            <View
              className="rounded-full px-2 py-1"
              style={{ backgroundColor: statusBadge.bg }}
            >
              <Text
                className="text-xs font-medium"
                style={{ color: statusBadge.color }}
              >
                {statusBadge.text}
              </Text>
            </View>
            <Text className="text-base font-semibold text-dark">
              ${quote.submittedQuote || 0}
            </Text>
            <FontAwesome6
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={12}
              color={Colors.gray}
            />
          </View>
        </View>
      </Pressable>

      {/* Vendor Details */}
      {isExpanded && (
        <View className="p-4">
          {/* Budget Comparison */}
          <View className="mb-3 flex-row justify-between">
            <View>
              <Text className="text-sm text-gray">PM Budget</Text>
              <Text className="font-semibold">${budget}</Text>
            </View>
            <View className="items-end">
              <Text className="text-sm text-gray">Vendor Quote</Text>
              <Text
                className={classNames(
                  'font-semibold',
                  difference > 0 ? 'text-danger' : 'text-success'
                )}
              >
                ${quote.submittedQuote || 0}
              </Text>
            </View>
          </View>

          {/* Comparison Line */}
          <View className="relative mb-2 h-1 rounded bg-light">
            <View
              className="absolute -top-1 h-3 w-3 rounded-full"
              style={{ backgroundColor: Colors.dark, left: 0 }}
            />
            <View
              className="absolute -top-1 h-3 w-3 rounded-full"
              style={{
                backgroundColor:
                  difference > 0 ? Colors.danger : Colors.warning,
                left: `${Math.min(((quote.submittedQuote || 0) / (budget * 1.5)) * 100, 90)}%`
              }}
            />
          </View>

          <View className="mb-3 items-end">
            <Text
              className={classNames(
                'text-sm font-medium',
                difference > 0 ? 'text-danger' : 'text-success'
              )}
            >
              {difference > 0 ? '+' : ''}
              {percentDiff}% (${Math.abs(difference)})
            </Text>
          </View>

          {/* Auto Approval Status */}
          {quote.quoteStatus === 'APPROVED' && (
            <View className="mb-4 flex-row items-center">
              <FontAwesome6
                name="check-circle"
                size={16}
                color={Colors.success}
              />
              <Text className="ml-2 text-sm text-gray">
                Auto-approved (within 10% of budget)
              </Text>
            </View>
          )}

          {/* Owner Approval Section */}
          {quote.quoteStatus === 'PENDING_OWNER' && (
            <View
              className="mb-4 rounded-lg border p-3"
              style={{
                backgroundColor: '#f3e8ff',
                borderColor: '#9c27b0'
              }}
            >
              <View className="flex-row items-start">
                <FontAwesome6
                  name="user-tie"
                  size={16}
                  color="#9c27b0"
                  style={{ marginTop: 2 }}
                />
                <View className="ml-3 flex-1">
                  <Text className="font-medium" style={{ color: '#9c27b0' }}>
                    Owner Approval Request
                  </Text>
                  <Text className="mt-1 text-sm text-gray">
                    Request sent to Property Owner on{' '}
                    {quote.submittedTime
                      ? new Date(quote.submittedTime).toLocaleDateString()
                      : 'Unknown'}
                    .{'\n'}Awaiting response.
                  </Text>
                  <View className="mt-2 flex-row items-center">
                    <View className="h-2 flex-1 rounded-full bg-gray-200">
                      <View
                        className="h-2 rounded-full"
                        style={{
                          backgroundColor: '#9c27b0',
                          width: '45%'
                        }}
                      />
                    </View>
                    <Text className="ml-2 text-xs text-gray">Pending</Text>
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* Status Steps */}
          <StatusSteps
            progress={quote.quoteStatus === 'APPROVED' ? 75 : 25}
            currentStep={quote.quoteStatus === 'APPROVED' ? 'work' : 'approval'}
            status={displayStatus}
          />

          {/* Action Buttons */}
          <View className="mt-4 flex-row gap-2">
            <Button
              variant="primary"
              size="sm"
              leftIcon="comment"
              className="flex-1"
            >
              Message
            </Button>
            <Button
              variant="outline"
              size="sm"
              leftIcon="circle-info"
              className="flex-1"
              onPress={() => {
                // Navigate to quote detail page
                router.push(
                  `/property-manager/project/quote-detail?quoteId=${quote.quoteId}&vendorId=${quote.vendorId}`
                )
              }}
            >
              View Details
            </Button>
          </View>

          {/* Additional Actions for Pending Quotes */}
          {displayStatus === 'PENDING_MANAGER' && (
            <View className="mt-2 flex-row gap-2">
              <Button
                variant="success"
                size="sm"
                leftIcon="check"
                className="flex-1"
                onPress={() => handleQuoteAction('APPROVED')}
                disabled={isSubmitting}
                loading={approvingQuote}
              >
                {approvingQuote ? 'Approving...' : 'Approve'}
              </Button>
              <Button
                variant="danger"
                size="sm"
                leftIcon="xmark"
                className="flex-1"
                onPress={() => handleQuoteAction('DECLINED')}
                loading={decliningQuote}
                disabled={isSubmitting}
              >
                {decliningQuote ? 'Declining...' : 'Decline'}
              </Button>
            </View>
          )}

          {/* Owner Reminder Button */}
          {displayStatus === 'PENDING_OWNER' && (
            <View className="mt-2">
              <Button variant="outline" size="sm" leftIcon="bell">
                Send Reminder to Owner
              </Button>
            </View>
          )}
        </View>
      )}
    </View>
  )
}

export default VendorQuote
