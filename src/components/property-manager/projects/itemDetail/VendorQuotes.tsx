import React from 'react'
import { Text, View } from 'react-native'
import type { FC } from 'react'

import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'

import { VendorQuote } from './VendorQuote'

interface VendorQuotesProps {
  vendorQuotesData: components['schemas']['ItemQuotes'][]
  expandedVendors: string[]
  onToggleVendor: (vendorId: string) => void
  isLoading?: boolean
  error?: Error
  projectId?: number
  itemId?: number
  onQuoteApproved?: () => void
}

export const VendorQuotes: FC<VendorQuotesProps> = ({
  vendorQuotesData,
  expandedVendors,
  onToggleVendor,
  isLoading = false,
  error,
  projectId,
  itemId,
  onQuoteApproved
}) => {
  return (
    <View
      className="mx-4 mb-4 rounded-lg bg-white p-4"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 3
      }}
    >
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-lg font-semibold text-dark">Vendor Quotes</Text>
        <View
          className="h-6 w-6 items-center justify-center rounded-full"
          style={{ backgroundColor: Colors.primary }}
        >
          <Text className="text-xs font-bold text-white">
            {vendorQuotesData.length}
          </Text>
        </View>
      </View>

      {/* Vendor List */}
      <View className="gap-4">
        {isLoading ? (
          <View className="py-8">
            <Text className="text-center text-gray">
              Loading vendor quotes...
            </Text>
          </View>
        ) : error ? (
          <View className="py-8">
            <Text className="mb-2 text-center text-red-500">
              Failed to load vendor quotes
            </Text>
            <Text className="text-center text-sm text-gray">
              {error.message || 'An unexpected error occurred'}
            </Text>
          </View>
        ) : vendorQuotesData.length === 0 ? (
          <View className="py-8">
            <Text className="text-center text-gray">
              No vendor quotes available
            </Text>
          </View>
        ) : (
          vendorQuotesData.map(quote => {
            // Use vendorId as the primary key for consistency
            const quoteKey =
              quote.vendorId?.toString() ||
              quote.quoteId?.toString() ||
              Math.random().toString()
            return (
              <VendorQuote
                key={quoteKey}
                quote={quote}
                isExpanded={expandedVendors.includes(quoteKey)}
                onToggle={() => onToggleVendor(quoteKey)}
                projectId={projectId}
                itemId={itemId}
                onQuoteApproved={onQuoteApproved}
              />
            )
          })
        )}
      </View>
    </View>
  )
}

export default VendorQuotes
