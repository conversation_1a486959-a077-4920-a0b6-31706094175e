import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { FC } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface StatusStepsProps {
  progress: number
  currentStep: string
  status: string
}

export const StatusSteps: FC<StatusStepsProps> = ({
  progress,
  // currentStep,
  status
}) => {
  const steps = [
    { id: 'quote', label: 'Quote Received', icon: 'file-invoice' },
    {
      id: 'approval',
      label: status === 'owner-approval' ? 'Owner Approval' : 'Quote Approved',
      icon: status === 'owner-approval' ? 'user-tie' : 'check'
    },
    { id: 'work', label: 'Work Started', icon: 'screwdriver-wrench' },
    { id: 'complete', label: 'Work Completed', icon: 'flag-checkered' }
  ]

  const getStepStatus = (stepIndex: number) => {
    const progressSteps = Math.floor(progress / 25)
    if (stepIndex < progressSteps) return 'completed'
    if (stepIndex === progressSteps) return 'current'
    return 'pending'
  }

  return (
    <View className="my-6">
      {/* Progress Bar */}
      <View className="relative">
        <View
          className="absolute left-5 right-5 top-5 h-0.5 bg-border"
          style={{ zIndex: 1 }}
        />
        <View
          className="absolute left-5 top-5 h-0.5"
          style={{
            backgroundColor: Colors.success,
            width: `${Math.max(0, progress - 12.5)}%`,
            zIndex: 2
          }}
        />

        {/* Steps */}
        <View className="flex-row justify-between" style={{ zIndex: 1 }}>
          {steps.map((step, index) => {
            const stepStatus = getStepStatus(index)
            return (
              <View
                key={step.id}
                className="items-center"
                style={{ width: '25%' }}
              >
                <View
                  className={classNames(
                    'mb-2 h-10 w-10 items-center justify-center rounded-full border-2',
                    stepStatus === 'completed'
                      ? 'border-success bg-success'
                      : stepStatus === 'current'
                        ? 'border-primary bg-primary'
                        : 'border-border bg-white'
                  )}
                  style={{
                    zIndex: 3,
                    ...(stepStatus === 'current' && {
                      shadowColor: Colors.primary,
                      shadowOffset: { width: 0, height: 0 },
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 5
                    })
                  }}
                >
                  <FontAwesome6
                    name={step.icon}
                    size={14}
                    color={
                      stepStatus === 'completed' || stepStatus === 'current'
                        ? 'white'
                        : Colors.gray
                    }
                  />
                </View>
                <Text
                  className={classNames(
                    'max-w-20 text-center text-xs',
                    stepStatus === 'completed' || stepStatus === 'current'
                      ? 'font-medium text-dark'
                      : 'text-gray'
                  )}
                >
                  {step.label}
                </Text>
              </View>
            )
          })}
        </View>
      </View>
    </View>
  )
}

export default StatusSteps
