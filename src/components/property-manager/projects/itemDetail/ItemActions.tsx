import React from 'react'
import { View } from 'react-native'
import type { FC } from 'react'

import { Button } from '@/components/Button'

interface ItemActionsProps {
  onEdit?: () => void
  onAddVendor?: () => void
  onDelete?: () => void
}

export const ItemActions: FC<ItemActionsProps> = ({
  onEdit,
  onAddVendor,
  onDelete
}) => {
  return (
    <View className="mx-4 mb-8 gap-2">
      <View className="flex-col gap-2">
        <Button
          variant="primary"
          className="flex-1"
          leftIcon="pencil"
          onPress={onEdit}
        >
          Edit Item
        </Button>
        <Button
          variant="outline"
          className="flex-1"
          leftIcon="user-plus"
          onPress={onAddVendor}
        >
          Add Vendor
        </Button>
      </View>
      <Button variant="danger-outline" leftIcon="trash" onPress={onDelete}>
        Delete
      </Button>
    </View>
  )
}

export default ItemActions
