// Type definitions for project item detail components

export interface ItemData {
  id: string
  title: string
  category: string
  status: string
  budget: string
  description: string
  priority: string
  startDate: string
  estimatedCompletion: string
  bestQuote: string
}

export interface VendorData {
  id: string
  name: string
  specialty: string
  avatar: string
  status: 'approved' | 'needs-approval' | 'owner-approval'
  quote: number
  budget: number
  progress: number
  currentStep: string
  autoApproved: boolean
  ownerApproval?: {
    sentTo: string
    sentDate: string
    status: string
  }
}

export interface StatusBadge {
  text: string
  bg: string
  color: string
  borderColor: string
}

export default StatusBadge
