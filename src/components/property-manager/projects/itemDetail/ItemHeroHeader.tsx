import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import type { FC } from 'react'

import PhotoSwiper from '@/components/PhotoSwiper'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import type { ItemData } from './types'

interface ItemHeroHeaderProps {
  item: ItemData
  isExpanded: boolean
  onToggleExpand: () => void
  photos?: string[]
}

export const ItemHeroHeader: FC<ItemHeroHeaderProps> = ({
  item,
  isExpanded,
  onToggleExpand,
  photos = []
}) => {
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in progress':
        return Colors.primary
      case 'completed':
        return Colors.success
      case 'pending quote':
        return Colors.warning
      default:
        return Colors.gray
    }
  }

  return (
    <View
      className={classNames(
        'relative mx-4 mb-4 mt-4 overflow-hidden rounded-lg',
        isExpanded ? 'h-52' : 'h-40'
      )}
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 5
      }}
    >
      <PhotoSwiper photos={photos} imageClassName="h-52" imageHeight={208} />
      <LinearGradient
        colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0.7)']}
        className="absolute inset-0"
      />
      <View className="absolute inset-0 justify-end p-5">
        {/* Badges */}
        <View className="mb-2 flex-row gap-2">
          <View
            className="rounded px-2 py-1"
            style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
          >
            <Text className="text-xs font-medium text-white">
              {item.category}
            </Text>
          </View>
          {item.priority && (
            <View
              className="rounded px-2 py-1"
              style={{ backgroundColor: 'rgba(234,67,53,0.2)' }}
            >
              <Text className="text-xs font-medium text-white">
                {item.priority} Priority
              </Text>
            </View>
          )}
        </View>

        {/* Title */}
        <Text
          className="mb-2 text-2xl font-semibold text-white"
          style={{
            textShadowColor: 'rgba(0,0,0,0.3)',
            textShadowOffset: { width: 0, height: 1 },
            textShadowRadius: 2
          }}
        >
          {item.title}
        </Text>

        {/* Status */}
        <View className="mb-1 flex-row items-center gap-2">
          <View
            className="h-2.5 w-2.5 rounded-full"
            style={{ backgroundColor: getStatusColor(item.status) }}
          />
          <Text className="text-sm text-white">{item.status}</Text>
        </View>

        {/* Budget */}
        <Text className="text-sm text-white opacity-90">
          Budget: {item.budget}
        </Text>
      </View>

      {/* Expand Button */}
      <Pressable
        onPress={onToggleExpand}
        className="absolute bottom-2 right-2 h-6 w-6 items-center justify-center rounded-full"
        style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
      >
        <FontAwesome6
          name={isExpanded ? 'chevron-up' : 'chevron-down'}
          size={12}
          color="white"
        />
      </Pressable>
    </View>
  )
}

export default ItemHeroHeader
