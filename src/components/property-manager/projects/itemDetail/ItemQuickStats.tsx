import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { FC } from 'react'

import { Colors } from '@/theme/colors'

import type { ItemData } from './types'

interface ItemQuickStatsProps {
  item: ItemData
}

export const ItemQuickStats: FC<ItemQuickStatsProps> = ({ item }) => {
  return (
    <View
      className="mx-4 mb-4 rounded-lg bg-white p-4"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 3
      }}
    >
      <Text className="mb-4 text-lg font-semibold text-dark">Quick Stats</Text>
      <View className="flex-row flex-wrap gap-4">
        {/* Your Budget */}
        <View
          className="min-w-36 flex-1 rounded-lg p-4"
          style={{ backgroundColor: Colors['info-light'] }}
        >
          <View className="mb-2 flex-row items-center">
            <FontAwesome6
              name="money-bill-wave"
              size={16}
              color={Colors.info}
            />
            <Text className="ml-2 text-xs font-medium text-gray">
              Your Budget
            </Text>
          </View>
          <Text className="text-xl font-bold" style={{ color: Colors.info }}>
            {item.budget}
          </Text>
        </View>

        {/* Best Quote */}
        <View
          className="min-w-36 flex-1 rounded-lg p-4"
          style={{ backgroundColor: Colors['success-light'] }}
        >
          <View className="mb-2 flex-row items-center">
            <FontAwesome6 name="tag" size={16} color={Colors.success} />
            <Text className="ml-2 text-xs font-medium text-gray">
              Best Quote
            </Text>
          </View>
          <Text className="text-xl font-bold" style={{ color: Colors.success }}>
            {item.bestQuote}
          </Text>
        </View>

        {/* Start Date */}
        <View
          className="min-w-36 flex-1 rounded-lg p-4"
          style={{ backgroundColor: Colors['warning-light'] }}
        >
          <View className="mb-2 flex-row items-center">
            <FontAwesome6
              name="calendar-day"
              size={16}
              color={Colors.warning}
            />
            <Text className="ml-2 text-xs font-medium text-gray">
              Start Date
            </Text>
          </View>
          <Text className="text-xl font-bold" style={{ color: Colors.warning }}>
            {item.startDate}
          </Text>
        </View>

        {/* Est. Completion */}
        <View
          className="min-w-36 flex-1 rounded-lg p-4"
          style={{ backgroundColor: Colors['primary-light'] }}
        >
          <View className="mb-2 flex-row items-center">
            <FontAwesome6
              name="flag-checkered"
              size={16}
              color={Colors.primary}
            />
            <Text className="ml-2 text-xs font-medium text-gray">
              Est. Completion
            </Text>
          </View>
          <Text className="text-xl font-bold" style={{ color: Colors.primary }}>
            {item.estimatedCompletion}
          </Text>
        </View>
      </View>
    </View>
  )
}

export default ItemQuickStats
