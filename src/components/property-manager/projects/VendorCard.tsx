import React, { useEffect, useState } from 'react'
import { Animated, Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Button } from '@/components/Button'
import { Star } from '@/components/Star'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface VendorData {
  createdBy?: number
  createdTime?: string
  updatedBy?: number
  updatedTime?: string
  isDeleted?: boolean
  vendorId?: number
  isProfileOpened?: boolean
  isTopRated?: boolean
  title?: string
  experienceYear?: number
  aboutMe?: string
  serviceTypes?: string
  businessName?: string
  serviceArea?: string
  licenseNumber?: string
  specialties?: string
  experienceLevel?: string
  licenseType?: string
  licenseExpiryDate?: string
  insuranceProvider?: string
  insurancePolicyNumber?: string
  insuranceCoverage?: string
  insuranceExpiryDate?: string
  receiveNewJobNotice?: boolean
  receiveBidUpdateNotice?: boolean
  receiveProjectStartNotice?: boolean
  receivePushNotice?: boolean
  receiveEmailNotice?: boolean
  receiveSmsNotice?: boolean
  userName?: string
  email?: string
  phoneNumber?: string
  birthday?: string
  sex?: string
  avatar?: string
}

interface VendorCardProps {
  vendor: VendorData
  // Selection mode props
  isSelected?: boolean
  onSelect?: () => void
  // Action buttons mode props
  onViewProfile?: () => void
  onAddVendor?: () => void
  isAdding?: boolean
  // Display mode
  mode?: 'selection' | 'actions'
}

export const VendorCard: React.FC<VendorCardProps> = ({
  vendor,
  isSelected = false,
  onSelect,
  onViewProfile,
  onAddVendor,
  isAdding = false,
  mode = 'selection'
}) => {
  const [animatedValue] = useState(new Animated.Value(0))

  // Trigger animation when selection changes (only in selection mode)
  useEffect(() => {
    if (mode === 'selection' && isSelected) {
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8
      }).start()
    } else {
      animatedValue.setValue(0)
    }
  }, [isSelected, animatedValue, mode])

  // Generate data based on vendor info
  const rating = 3
  const reviewCount = 88
  const completedProjects = 99

  const CardWrapper = mode === 'selection' ? Pressable : View

  return (
    <CardWrapper
      onPress={mode === 'selection' ? onSelect : undefined}
      className={classNames(
        'relative mb-4 rounded-xl bg-white p-4',
        mode === 'selection' && isSelected
          ? 'border-2 border-primary'
          : 'border border-border'
      )}
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: mode === 'selection' && isSelected ? 0.15 : 0.08,
        shadowRadius: 8,
        elevation: 3,
        backgroundColor:
          mode === 'selection' && isSelected ? Colors['primary-light'] : 'white'
      }}
    >
      {/* Vendor Header */}
      <View className="mb-3 flex-row items-start gap-3">
        <Image
          source={{
            uri: vendor.avatar
          }}
          className="h-12 w-12 rounded-full"
          style={{ backgroundColor: Colors.gray }}
        />
        <View className="flex-1">
          <Text className="mb-0.5 text-base font-semibold text-dark">
            {vendor.businessName || vendor.userName || 'Unknown Vendor'}
          </Text>
          <Text className="mb-1.5 text-sm text-gray">
            {vendor.serviceTypes || 'General Contractor'}
          </Text>

          {/* Rating display for both modes */}
          <View className="flex-row items-center gap-1">
            <Star
              value={rating}
              size={12}
              gap={1}
              allowHalf={true}
              disabled={true}
            />
            <Text className="text-xs text-gray">
              {rating} ({reviewCount} reviews)
            </Text>
          </View>
        </View>
        <View className="items-end">
          <View
            className={classNames(
              'rounded-full px-2 py-1',
              vendor.isProfileOpened ? 'bg-success-light' : 'bg-warning-light'
            )}
          >
            <Text
              className={classNames(
                'text-xs font-semibold',
                vendor.isProfileOpened ? 'text-success' : 'text-warning'
              )}
            >
              {vendor.isProfileOpened ? 'Available' : 'Busy'}
            </Text>
          </View>
        </View>
      </View>

      {/* Vendor Details - Same for both modes */}
      <View
        className="mb-4 flex-col gap-1.5 rounded-lg p-3"
        style={{ backgroundColor: Colors.light }}
      >
        <View className="flex-row items-center gap-2">
          <FontAwesome6
            name="screwdriver-wrench"
            size={14}
            color={Colors.primary}
          />
          <Text className="text-xs text-gray">
            {vendor.experienceYear
              ? `${vendor.experienceYear}+ years experience`
              : vendor.experienceLevel || 'Experience level not specified'}
          </Text>
        </View>
        <View className="flex-row items-center gap-2">
          <FontAwesome6 name="check-circle" size={14} color={Colors.primary} />
          <Text className="text-xs text-gray">
            {completedProjects} completed projects
          </Text>
        </View>
      </View>

      {/* Action Buttons (only in actions mode) */}
      {mode === 'actions' && (
        <View className="flex-row gap-2">
          <Button
            variant="outline"
            size="sm"
            leftIcon="user"
            className="flex-1"
            onPress={onViewProfile}
          >
            View Profile
          </Button>
          <Button
            variant="primary"
            size="sm"
            leftIcon="plus"
            className="flex-1"
            onPress={onAddVendor}
            disabled={isAdding}
          >
            {isAdding ? 'Adding...' : 'Add to Item'}
          </Button>
        </View>
      )}

      {/* Selection Indicator with Animation (only in selection mode) */}
      {mode === 'selection' && isSelected && (
        <Animated.View
          style={{
            position: 'absolute',
            top: 16,
            right: 16,
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 20,
            backgroundColor: Colors.primary,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 6,
            transform: [
              {
                scale: animatedValue.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 1.2, 1]
                })
              },
              {
                rotate: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['-180deg', '0deg']
                })
              }
            ],
            opacity: animatedValue,
            shadowColor: Colors.primary,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 5,
            zIndex: 10
          }}
        >
          <FontAwesome6 name="check" size={12} color="white" />
          <Text
            style={{
              fontSize: 12,
              fontWeight: '600',
              color: 'white'
            }}
          >
            Selected
          </Text>
        </Animated.View>
      )}
    </CardWrapper>
  )
}
