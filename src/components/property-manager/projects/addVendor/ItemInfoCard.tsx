import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

interface ItemInfoCardProps {
  title: string
  budget: string
  property: string
}

export const ItemInfoCard: React.FC<ItemInfoCardProps> = ({
  title,
  budget,
  property
}) => {
  return (
    <View
      className="mb-5 rounded-xl bg-white p-4"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3
      }}
    >
      <View className="mb-2 flex-row items-center gap-3">
        <FontAwesome6 name="hammer" size={20} color={Colors.primary} />
        <View className="flex-1">
          <Text className="mb-0.5 text-base font-semibold text-dark">
            {title}
          </Text>
          <Text className="text-sm text-gray">{property}</Text>
        </View>
      </View>
      <Text className="text-sm font-medium text-dark">
        Budget: <Text className="text-primary">{budget}</Text>
      </Text>
    </View>
  )
}
