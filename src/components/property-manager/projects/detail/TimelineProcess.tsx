import React from 'react'
import { Text, View } from 'react-native'

import { Progress } from '@/components/Progress'

interface TimelineProcessProps {
  startDate: string
  endDate: string
  daysLeft: string
  progress: number
  todayPosition?: number
}

export function TimelineProcess({
  startDate,
  endDate,
  daysLeft,
  progress,
  todayPosition
}: TimelineProcessProps) {
  return (
    <View className="mt-5">
      <View className="mb-4 flex-row items-center justify-between">
        <View className="w-full flex-row items-center justify-between">
          <Text className="text-xs text-gray">{startDate}</Text>
          <View className="flex flex-row items-center">
            <Text className="text-xs text-gray">{endDate}</Text>
            <Text className="ml-2 text-xs font-semibold text-primary">
              {daysLeft}
            </Text>
          </View>
        </View>
      </View>
      <View className="relative h-1.5 rounded-full bg-light">
        <Progress percentage={progress} />
        <View className="absolute -left-1 -top-1 h-3 w-3 rounded-full border-2 border-white bg-success" />
        <View
          className="absolute h-3 w-3 rounded-full border-2 border-white bg-primary"
          style={{
            top: -4,
            left: `${todayPosition ?? progress}%`,
            transform: [{ translateX: -6 }]
          }}
        >
          <Text className="absolute -left-1 -top-5 text-xs font-semibold text-primary">
            Today
          </Text>
        </View>
        {/* <View
          className="absolute h-3 w-3 rounded-full border-2 bg-white"
          style={{
            borderColor: Colors['gray-800'],
            top: -4,
            right: 0,
            transform: [{ translateX: 3 }]
          }}
        /> */}
      </View>
      {/* <View className="mt-1 flex-row justify-end">
        <Text className="text-xs font-semibold text-primary">{daysLeft}</Text>
      </View> */}
    </View>
  )
}
