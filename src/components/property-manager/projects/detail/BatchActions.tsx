import React from 'react'
import { Text, View } from 'react-native'

import { Button } from '@/components/Button'

interface BatchActionsProps {
  selectedCount: number
  onAssignVendor: () => void
  onCancel: () => void
}

export const BatchActions: React.FC<BatchActionsProps> = ({
  selectedCount,
  onAssignVendor,
  onCancel
}) => {
  return (
    <View
      className="mb-3 flex-row items-center justify-between rounded-lg p-3"
      style={{
        backgroundColor: '#e3f2fd', // Light blue background
        borderColor: '#2196f3', // Blue border
        borderWidth: 2
      }}
    >
      {/* Selected Count */}
      <View>
        <Text
          className="font-semibold"
          style={{ color: '#1976d2' }} // Blue text color
        >
          {selectedCount} items{'\n'}selected
        </Text>
      </View>

      {/* Batch Actions */}
      <View className="flex-row gap-2">
        <Button
          variant="primary"
          size="sm"
          leftIcon="user-plus"
          onPress={onAssignVendor}
          disabled={selectedCount === 0}
        >
          Assign{'\n'}Vendor
        </Button>
        <Button variant="outline" size="sm" leftIcon="xmark" onPress={onCancel}>
          Cancel
        </Button>
      </View>
    </View>
  )
}

export default BatchActions
