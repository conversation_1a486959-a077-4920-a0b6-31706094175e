import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { ComponentProps, ReactNode } from 'react'

import { Progress } from '@/components'
import { Colors } from '@/theme/colors'

import Card from './Card'

interface ProgressStatCardProps {
  icon: ComponentProps<typeof FontAwesome6>['name']
  value: string
  label: string
  progress: number
  total?: string
  iconBgColor?: string
  iconColor?: string
  progressColor?: string
}

function ProgressStatCard({
  icon,
  value,
  label,
  progress,
  total,
  iconBgColor = 'bg-primary-light',
  iconColor = Colors.primary,
  progressColor
}: ProgressStatCardProps) {
  return (
    <View className="flex-row items-center rounded-lg bg-light p-3">
      <View
        className={`mr-3 h-8 w-8 items-center justify-center rounded-full ${iconBgColor}`}
      >
        <FontAwesome6 name={icon} size={16} color={iconColor} solid />
      </View>
      <View className="flex-1">
        <Text className="mb-1 text-sm font-bold">
          {value}
          {total && <Text className="text-gray">/{total}</Text>}
        </Text>
        <Text className="mb-1 text-xs text-gray">{label}</Text>
        <Progress percentage={progress} color={progressColor} radius={5} />
      </View>
    </View>
  )
}

interface ProjectProgressProps {
  progress: number
  stats: ProgressStatCardProps[]
  children?: ReactNode
}

export function ProjectProgress({
  progress,
  stats,
  children
}: ProjectProgressProps) {
  return (
    <Card>
      <View className="mb-3 flex-row items-center justify-between">
        <Text className="text-2xl font-bold">Progress</Text>
        <View className="items-end">
          <Text className="text-xl font-bold text-primary">{progress}%</Text>
          <Text className="text-xs text-gray">Complete</Text>
        </View>
      </View>
      <Progress percentage={progress} radius={5} className="mb-4 h-2.5" />
      <View className="gap-3">
        {stats.map((stat, index) => (
          <ProgressStatCard key={index} {...stat} />
        ))}
      </View>
      {children}
    </Card>
  )
}
