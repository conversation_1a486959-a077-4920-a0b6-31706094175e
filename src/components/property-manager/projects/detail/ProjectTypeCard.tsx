import type { ColorValue } from 'react-native'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'

import { Colors } from '@/theme/colors'

interface ProjectTypeCardProps {
  type: 'rehab' | 'workorder'
  title: string
  subtitle: string
  onSelect: () => void
}

const ProjectTypeConfig = {
  rehab: {
    icon: 'hammer',
    gradient: ['#4F46E5', '#7C3AED'] as [ColorValue, ColorValue]
  },
  workorder: {
    icon: 'clipboard-list',
    gradient: ['#059669', '#10B981'] as [ColorValue, ColorValue]
  }
}

export function ProjectTypeCard({
  type,
  title,
  subtitle,
  onSelect
}: ProjectTypeCardProps) {
  const config = ProjectTypeConfig[type]

  return (
    <Pressable
      onPress={onSelect}
      className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm"
    >
      <LinearGradient
        colors={config.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="p-4"
      >
        <View className="flex-row items-center space-x-3">
          <View className="rounded-full bg-white/20 p-3">
            <FontAwesome6 name={config.icon} size={24} color={Colors.white} />
          </View>
          <View className="flex-1">
            <Text className="text-xl font-semibold text-white">{title}</Text>
            <Text className="text-sm text-white/80">{subtitle}</Text>
          </View>
          <FontAwesome6 name="chevron-right" size={16} color={Colors.white} />
        </View>
      </LinearGradient>
    </Pressable>
  )
}
