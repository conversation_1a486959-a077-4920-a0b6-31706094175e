import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { ComponentProps } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import Card from './Card'

interface ActivityItem {
  icon: ComponentProps<typeof FontAwesome6>['name']
  title: string
  subtitle: string
  badgeText: string
  badgeType: 'warning' | 'outline'
}

const badgeStyles = {
  warning: 'bg-warning-light text-warning',
  outline: 'bg-gray-200 text-gray-700'
}

function ActivityListItem({ item }: { item: ActivityItem }) {
  return (
    <View className="flex-row items-center border-b border-gray-100 py-3">
      <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-primary-light">
        <FontAwesome6 name={item.icon} size={20} color={Colors.primary} />
      </View>
      <View className="flex-1">
        <Text className="mb-1 text-sm font-semibold">{item.title}</Text>
        <Text className="text-gray-500 text-xs">{item.subtitle}</Text>
      </View>
      <View
        className={classNames(
          'rounded-full px-2 py-1',
          badgeStyles[item.badgeType].split(' ')[0]
        )}
      >
        <Text
          className={classNames(
            'text-xs font-medium',
            badgeStyles[item.badgeType].split(' ')[1]
          )}
        >
          {item.badgeText}
        </Text>
      </View>
    </View>
  )
}

interface UpcomingActivitiesProps {
  activities: ActivityItem[]
}

export function UpcomingActivities({ activities }: UpcomingActivitiesProps) {
  return (
    <Card>
      <View className="mb-2 flex-row items-center justify-between">
        <Text className="text-md font-bold">Upcoming Activities</Text>
        <TouchableOpacity>
          <Text className="text-primary-500 text-sm">View All</Text>
        </TouchableOpacity>
      </View>
      <View>
        {activities.map((activity, index) => (
          <ActivityListItem key={index} item={activity} />
        ))}
      </View>
    </Card>
  )
}
