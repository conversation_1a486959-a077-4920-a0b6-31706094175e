import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'

import { ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

interface ProjectTabsProps {
  activeTab: 'overview' | 'items'
  onTabChange: (tab: 'overview' | 'items') => void
}

export function ProjectTabs({ activeTab, onTabChange }: ProjectTabsProps) {
  const tabs = [
    { id: 'overview', title: 'Overview' },
    { id: 'items', title: 'Items' }
  ]

  return (
    <View
      className="mb-4 flex-row rounded-default bg-white"
      style={ShadowStyles.sm}
    >
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.id}
          className={classNames(
            'flex-1 items-center border-b-2 py-3',
            activeTab === tab.id ? 'border-primary' : 'border-transparent'
          )}
          onPress={() => onTabChange(tab.id as 'overview' | 'items')}
        >
          <Text
            className={classNames(
              'text-sm',
              activeTab === tab.id ? 'text-primary' : 'text-dark'
            )}
          >
            {tab.title}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )
}
