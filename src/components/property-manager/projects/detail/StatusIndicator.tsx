import React from 'react'
import { Text, View } from 'react-native'

import { Colors } from '@/theme/colors'

interface StatusIndicatorProps {
  status: string
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'In Progress':
        return Colors.info
      case 'Pending Quote':
        return Colors.warning
      case 'Completed':
        return Colors.success
      case 'Not Started':
        return Colors.gray
      default:
        return Colors.gray
    }
  }

  return (
    <View className="flex-row items-center">
      <View
        className="mr-2 h-2 w-2 rounded-full"
        style={{ backgroundColor: getStatusColor() }}
      />
      <Text className="text-xs font-medium" style={{ color: getStatusColor() }}>
        {status}
      </Text>
    </View>
  )
}

export default StatusIndicator
