import React, { useEffect, useRef, useState } from 'react'
import { Text, View } from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { Button } from '@/components/Button'

// Import split components
import { AdvancedFilters } from './AdvancedFilters'
import { BatchActions } from './BatchActions'
import { EmptyState } from './EmptyState'
import { ItemCard } from './ItemCard'
import { SearchAndFilters } from './SearchAndFilters'
import { VendorExpandCard } from './VendorExpandCard'

// Type definition for project data
interface ProjectData {
  projectId?: number
  projectName?: string
  items?: Array<{
    itemId?: number
    itemName?: string
    itemDesc?: string
    areaType?: string
    areaName?: string
    status?: string
    priority?: string
    budget?: number
    expectedCompletionDate?: string
    photos?: Array<{
      fileUrl?: string
      fileName?: string
    }>
    vendorName?: string
    vendorBusinessName?: string
    totalVendors?: number
    approvedVendors?: number
    rejectedVendors?: number
    pendingVendors?: number
    allocations?: Array<{
      vendorId?: number
      vendorName?: string
      vendorBusinessName?: string
      finalAmount?: number
      allocationStatus?: string
    }>
    quotes?: Array<{
      vendorId?: number
      vendorName?: string
      vendorBusinessName?: string
      submittedQuote?: number
      quoteStatus?: string
    }>
  }>
}

// Type definition for vendor data
interface VendorData {
  id: string
  name: string
  specialty: string
  avatar: string
  status: string
  itemCount: number
  items: Array<{
    id: string
    title: string
    category: string
    price: string
  }>
}

interface ProjectItemsTabProps {
  projectData?: ProjectData
}

// Helper function to determine vendor specialty based on area type
const getVendorSpecialty = (areaType: string): string => {
  const specialtyMap: { [key: string]: string } = {
    Kitchen: 'Kitchen Specialist',
    Bathroom: 'Bathroom Specialist',
    Flooring: 'Flooring Specialist',
    Electrical: 'Electrical Contractor',
    Plumbing: 'Plumbing Specialist',
    HVAC: 'HVAC Specialist',
    Painting: 'Painting Contractor',
    Carpentry: 'Carpentry Specialist'
  }
  return specialtyMap[areaType] || 'General Contractor'
}

// Helper function to determine vendor status
const getVendorStatus = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    ASSIGNED: 'In Progress',
    COMPLETED: 'Completed',
    PENDING: 'Pending Quote',
    APPROVED: 'In Progress',
    REJECTED: 'Rejected',
    CANCELLED: 'Cancelled'
  }
  return statusMap[status.toUpperCase()] || 'Pending Quote'
}

/**
 * Project items tab with batch select, filter, and search.
 */
export const ProjectItemsTab = ({ projectData }: ProjectItemsTabProps) => {
  const router = useRouter()
  const { refresh } = useLocalSearchParams<{ refresh?: string }>()
  const [selected, setSelected] = useState<string[]>([])
  const [batchMode, setBatchMode] = useState(false)
  const [statusFilter, setStatusFilter] = useState('all')
  const [search, setSearch] = useState('')
  const [viewMode, setViewMode] = useState<'items' | 'vendor'>('items')
  const [expandedVendors, setExpandedVendors] = useState<string[]>([])
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [advancedFilters, setAdvancedFilters] = useState({
    areas: [] as string[],
    statuses: [] as string[],
    priceRange: [0, 5000] as [number, number]
  })

  // Track if refresh has been processed to prevent repeated execution
  const refreshProcessedRef = useRef<string | null>(null)

  // Reset states when returning from vendor assignment
  useEffect(() => {
    if (refresh && refresh !== refreshProcessedRef.current) {
      console.log('Resetting ProjectItemsTab state after vendor assignment')

      // Mark this refresh as processed
      refreshProcessedRef.current = refresh

      // Reset all states to initial values
      setSelected([])
      setBatchMode(false)
      setStatusFilter('all')
      setSearch('')
      setViewMode('items')
      setExpandedVendors([])
      setShowAdvancedFilters(false)
      setAdvancedFilters({
        areas: [],
        statuses: [],
        priceRange: [0, 5000]
      })

      // Show success message
      Toast.success('Vendor assignment completed successfully!')
    }
  }, [refresh])

  // Transform API data to component format
  const transformedItems =
    projectData?.items?.map(item => ({
      id: item.itemId?.toString() || '',
      title: item.itemName || 'Unknown Item',
      category: item.areaType || 'Unknown',
      priority: item.priority || '',
      budget: item.budget || 0,
      vendors: `${item.approvedVendors || 0}/${item.totalVendors || 0}`,
      status: item.status || 'Unknown',
      description: item.itemDesc || '',
      area: item.areaName || '',
      dueDate: item.expectedCompletionDate
        ? new Date(item.expectedCompletionDate).toLocaleDateString()
        : '',
      image: item.photos?.[0]?.fileUrl || '',
      vendorName: item.vendorName || item.vendorBusinessName || '',
      vendorId: item.allocations?.[0]?.vendorId?.toString() || '',
      allocations: item.allocations || [],
      quotes: item.quotes || []
    })) || [] // Return empty array if no project data

  // Transform API data to vendor format
  const transformedVendors: VendorData[] = React.useMemo(() => {
    if (!projectData?.items?.length) return []

    // Collect all vendor information
    const vendorMap = new Map()

    projectData.items.forEach(item => {
      // Get vendor information from allocations
      item.allocations?.forEach(allocation => {
        if (allocation.vendorId && allocation.vendorName) {
          const vendorId = allocation.vendorId.toString()

          if (!vendorMap.has(vendorId)) {
            vendorMap.set(vendorId, {
              id: vendorId,
              name:
                allocation.vendorName ||
                allocation.vendorBusinessName ||
                'Unknown Vendor',
              specialty: getVendorSpecialty(item.areaType || ''),
              avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 50) + 1}.jpg`,
              status: getVendorStatus(allocation.allocationStatus || ''),
              itemCount: 0,
              items: []
            })
          }

          const vendor = vendorMap.get(vendorId)
          vendor.itemCount++
          vendor.items.push({
            id: item.itemId?.toString() || '',
            title: item.itemName || 'Unknown Item',
            category: item.areaType || 'Unknown',
            price: allocation.finalAmount
              ? `$${allocation.finalAmount}`
              : 'Pending'
          })
        }
      })

      // Get vendor information from quotes (if no allocation)
      if (!item.allocations?.length && item.quotes?.length) {
        item.quotes.forEach(quote => {
          if (quote.vendorId && quote.vendorName) {
            const vendorId = quote.vendorId.toString()

            if (!vendorMap.has(vendorId)) {
              vendorMap.set(vendorId, {
                id: vendorId,
                name:
                  quote.vendorName ||
                  quote.vendorBusinessName ||
                  'Unknown Vendor',
                specialty: getVendorSpecialty(item.areaType || ''),
                avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 50) + 1}.jpg`,
                status: getVendorStatus(quote.quoteStatus || ''),
                itemCount: 0,
                items: []
              })
            }

            const vendor = vendorMap.get(vendorId)
            vendor.itemCount++
            vendor.items.push({
              id: item.itemId?.toString() || '',
              title: item.itemName || 'Unknown Item',
              category: item.areaType || 'Unknown',
              price: quote.submittedQuote
                ? `$${quote.submittedQuote}`
                : 'Pending'
            })
          }
        })
      }
    })

    return Array.from(vendorMap.values())
  }, [projectData])

  // Generate status chips from real data
  const statusChips = React.useMemo(() => {
    const statusCounts: { [key: string]: number } = {}

    transformedItems.forEach(item => {
      statusCounts[item.status] = (statusCounts[item.status] || 0) + 1
    })

    const chips = [
      { label: 'All Items', value: 'all', count: transformedItems.length }
    ]

    // Add status-specific chips only if they have items
    Object.entries(statusCounts).forEach(([status, count]) => {
      if (count > 0) {
        chips.push({ label: status, value: status, count })
      }
    })

    return chips
  }, [transformedItems])

  // Filtered items
  const filtered = transformedItems.filter(
    item =>
      (statusFilter === 'all' || item.status === statusFilter) &&
      (item.title.toLowerCase().includes(search.toLowerCase()) ||
        item.category.toLowerCase().includes(search.toLowerCase()))
  )

  // Toggle batch mode
  const toggleBatch = () => {
    setBatchMode(v => !v)
    if (batchMode) {
      setSelected([])
    }
  }

  // Select/deselect item
  const toggleSelect = (id: string) => {
    setSelected(sel =>
      sel.includes(id) ? sel.filter(i => i !== id) : [...sel, id]
    )
  }

  // Cancel batch selection
  const cancelBatch = () => {
    setBatchMode(false)
    setSelected([])
  }

  // Handle item click
  const handleItemClick = (item: any) => {
    // Prevent navigation in batch mode
    if (batchMode) {
      toggleSelect(item.id)
      return
    }

    // Navigate to item detail
    router.push(`/property-manager/project/item-detail?id=${item.id}`)
  }

  // Handle advanced filters
  const handleAdvancedFiltersChange = (filters: typeof advancedFilters) => {
    setAdvancedFilters(filters)
  }

  const applyAdvancedFilters = () => {
    setShowAdvancedFilters(false)
    Toast.success('Filters applied successfully')
  }

  const resetAdvancedFilters = () => {
    setAdvancedFilters({
      areas: [],
      statuses: [],
      priceRange: [0, 5000]
    })
    Toast.info('Filters reset')
  }

  // Handle assign vendor navigation
  const handleAssignVendor = () => {
    if (selected.length === 0) {
      Toast.error('No items selected')
      return
    }

    // Get selected item IDs
    const selectedItemIds = selected.join(',')

    router.push({
      pathname: '/property-manager/project/assign',
      params: {
        projectId: projectData?.projectId?.toString() || '0',
        selectedItemIds: selectedItemIds,
        selectedCount: selected.length.toString()
      }
    })
  }

  // Toggle vendor expansion
  const toggleVendorExpansion = (vendorId: string) => {
    setExpandedVendors(prev =>
      prev.includes(vendorId)
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    )
  }

  return (
    <View>
      {/* Header Actions */}
      <View className="mb-3 flex-row gap-2">
        <Button
          variant="primary"
          size="sm"
          leftIcon="plus"
          className="flex-1"
          onPress={() => {}}
        >
          Add Item
        </Button>
        {/* <Button
          variant="outline"
          size="sm"
          leftIcon="upload"
          className="flex-1"
          onPress={() => {}}
        >
          Import Items
        </Button> */}
      </View>

      {/* Batch actions toolbar */}
      {batchMode && selected.length > 0 && (
        <BatchActions
          selectedCount={selected.length}
          onAssignVendor={handleAssignVendor}
          onCancel={cancelBatch}
        />
      )}
      {/* Search and Filters */}
      <SearchAndFilters
        search={search}
        onSearchChange={setSearch}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        statusChips={statusChips}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        batchMode={batchMode}
        onToggleBatch={toggleBatch}
        onToggleAdvancedFilters={() => setShowAdvancedFilters(true)}
      />

      {/* Advanced Filters */}
      <AdvancedFilters
        isVisible={showAdvancedFilters}
        onClose={() => setShowAdvancedFilters(false)}
        filters={advancedFilters}
        onFiltersChange={handleAdvancedFiltersChange}
        onApplyFilters={applyAdvancedFilters}
        onResetFilters={resetAdvancedFilters}
      />

      {/* Items List */}
      {viewMode === 'items' && (
        <View className="gap-4">
          {filtered.length === 0 ? (
            <EmptyState type="items" hasData={transformedItems.length > 0} />
          ) : (
            filtered.map(item => (
              <ItemCard
                key={item.id}
                item={item}
                batchMode={batchMode}
                isSelected={selected.includes(item.id)}
                onLongPress={toggleBatch}
                onPress={() => handleItemClick(item)}
                onToggleSelect={() => toggleSelect(item.id)}
                // In View Items mode, items are NOT expandable
                isExpandable={false}
                isExpanded={false}
                onToggleExpansion={undefined}
              />
            ))
          )}
        </View>
      )}

      {/* Vendor View */}
      {viewMode === 'vendor' && (
        <View className="gap-4">
          <View className="mb-3 flex-row items-center justify-between">
            <Text className="text-lg font-bold text-gray-900">
              Vendors ({transformedVendors.length})
            </Text>
            <Button variant="primary" size="sm" leftIcon="plus">
              Add Vendor
            </Button>
          </View>

          {/* Vendor Cards */}
          {transformedVendors.length === 0 ? (
            <EmptyState type="vendors" hasData={false} />
          ) : (
            transformedVendors.map(vendor => (
              <VendorExpandCard
                key={vendor.id}
                vendor={vendor}
                isExpanded={expandedVendors.includes(vendor.id)}
                onToggleExpansion={() => toggleVendorExpansion(vendor.id)}
              />
            ))
          )}
        </View>
      )}
    </View>
  )
}

export default ProjectItemsTab
