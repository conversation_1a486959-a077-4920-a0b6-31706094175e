import React from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Button } from '@/components/Button'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import { StatusIndicator } from './StatusIndicator'

interface VendorItem {
  id: string
  title: string
  category: string
  price: string
}

interface VendorData {
  id: string
  name: string
  specialty: string
  avatar: string
  status: string
  itemCount: number
  items: VendorItem[]
}

interface VendorCardProps {
  vendor: VendorData
  isExpanded: boolean
  onToggleExpansion: () => void
}

export const VendorExpandCard: React.FC<VendorCardProps> = ({
  vendor,
  isExpanded,
  onToggleExpansion
}) => {
  return (
    <View
      className="overflow-hidden rounded-lg bg-white"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 3
      }}
    >
      {/* Vendor Header */}
      <Pressable onPress={onToggleExpansion}>
        <View className="border-b border-gray-100 p-4">
          <View className="flex-row items-center">
            <Image
              source={{ uri: vendor.avatar }}
              className="mr-3 h-10 w-10 rounded-full"
            />
            <View className="flex-1">
              <Text className="font-semibold text-gray-900">{vendor.name}</Text>
              <Text className="text-gray-500 text-xs">
                {vendor.specialty} • {vendor.itemCount} Items
              </Text>
            </View>
            <View className="flex-row items-center gap-2">
              <StatusIndicator status={vendor.status} />
              <FontAwesome6
                name={isExpanded ? 'chevron-up' : 'chevron-down'}
                size={12}
                color={Colors.gray}
              />
            </View>
          </View>
        </View>
      </Pressable>

      {/* Vendor Items List - Expandable */}
      {isExpanded && (
        <View className="bg-gray-50 p-4">
          {/* Items */}
          {vendor.items.map((item, index) => (
            <View
              key={item.id}
              className={classNames(
                'flex-row justify-between',
                index < vendor.items.length - 1 ? 'mb-2' : 'mb-3'
              )}
            >
              <View className="flex-1">
                <Text className="text-sm text-gray-700">{item.title}</Text>
                <Text className="text-gray-500 text-xs">{item.category}</Text>
              </View>
              <Text className="text-sm font-medium text-gray-900">
                {item.price}
              </Text>
            </View>
          ))}

          {/* Action Buttons */}
          <View className="flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              leftIcon="comment"
              className="flex-1"
            >
              Message
            </Button>
            {vendor.status === 'Pending Quote' ? (
              <Button
                variant="primary"
                size="sm"
                leftIcon="clock"
                className="flex-1"
              >
                Follow Up
              </Button>
            ) : vendor.status === 'Completed' ? (
              <Button
                variant="success"
                size="sm"
                leftIcon="star"
                className="flex-1"
              >
                Review
              </Button>
            ) : (
              <Button
                variant="primary"
                size="sm"
                leftIcon="eye"
                className="flex-1"
              >
                View Progress
              </Button>
            )}
          </View>
        </View>
      )}
    </View>
  )
}

export default VendorExpandCard
