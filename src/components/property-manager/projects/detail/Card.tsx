import type { ViewProps } from 'react-native'
import { type StyleProp, View, type ViewStyle } from 'react-native'
import type { FC } from 'react'

import { ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

interface CardProps {
  className?: string
  style?: StyleProp<ViewStyle>
}

const Card: FC<ViewProps & CardProps> = ({ className, style, ...props }) => {
  return (
    <View
      className={classNames(
        'mb-4 rounded-default border border-border bg-white p-4',
        className
      )}
      style={[ShadowStyles.sm, style]}
      {...props}
    />
  )
}

export default Card
