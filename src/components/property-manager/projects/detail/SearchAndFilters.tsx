import React from 'react'
import { Pressable, ScrollView, Text, TextInput, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface StatusChip {
  label: string
  value: string
  count: number
}

interface SearchAndFiltersProps {
  search: string
  onSearchChange: (text: string) => void
  statusFilter: string
  onStatusFilterChange: (status: string) => void
  statusChips: StatusChip[]
  viewMode: 'items' | 'vendor'
  onViewModeChange: (mode: 'items' | 'vendor') => void
  batchMode: boolean
  onToggleBatch: () => void
  onToggleAdvancedFilters?: () => void
}

export const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  search,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  statusChips,
  viewMode,
  onViewModeChange,
  batchMode,
  onToggleBatch,
  onToggleAdvancedFilters
}) => {
  return (
    <View className="mb-4">
      {/* Search and Filter Toggle */}
      <View className="mb-3 flex-row items-center gap-3">
        <View className="bg-gray-50 flex-1 flex-row items-center rounded-lg border border-gray-200 px-3 py-2">
          <FontAwesome6 name="magnifying-glass" size={16} color={Colors.gray} />
          <TextInput
            className="ml-2 flex-1 text-gray-900"
            placeholder="Search items..."
            value={search}
            onChangeText={onSearchChange}
            placeholderTextColor={Colors.gray}
          />
        </View>
        <Pressable
          className="bg-gray-50 rounded-lg border border-gray-200 p-2"
          onPress={onToggleBatch}
        >
          <FontAwesome6
            name="check-square"
            size={16}
            color={batchMode ? Colors.primary : Colors.gray}
          />
        </Pressable>
        <Pressable
          className="bg-gray-50 rounded-lg border border-gray-200 p-2"
          onPress={onToggleAdvancedFilters}
        >
          <FontAwesome6 name="filter" size={16} color={Colors.gray} />
        </Pressable>
      </View>

      {/* View Mode Toggle */}
      <View className="bg-gray-50 mb-3 flex-row rounded-lg border border-gray-200 p-1">
        <Pressable
          className={classNames(
            'flex-1 flex-row items-center justify-center rounded-md py-2',
            viewMode === 'items' ? 'bg-white' : ''
          )}
          onPress={() => onViewModeChange('items')}
          style={{
            shadowColor: viewMode === 'items' ? '#000' : 'transparent',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: viewMode === 'items' ? 0.1 : 0,
            shadowRadius: 2,
            elevation: viewMode === 'items' ? 2 : 0
          }}
        >
          <FontAwesome6
            name="list"
            size={14}
            color={viewMode === 'items' ? Colors.primary : Colors.gray}
            style={{ marginRight: 6 }}
          />
          <Text
            className="text-sm font-medium"
            style={{
              color: viewMode === 'items' ? Colors.primary : Colors.gray
            }}
          >
            View Items
          </Text>
        </Pressable>

        <Pressable
          className={classNames(
            'flex-1 flex-row items-center justify-center rounded-md py-2',
            viewMode === 'vendor' ? 'bg-white' : ''
          )}
          onPress={() => onViewModeChange('vendor')}
          style={{
            shadowColor: viewMode === 'vendor' ? '#000' : 'transparent',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: viewMode === 'vendor' ? 0.1 : 0,
            shadowRadius: 2,
            elevation: viewMode === 'vendor' ? 2 : 0
          }}
        >
          <FontAwesome6
            name="users"
            size={14}
            color={viewMode === 'vendor' ? Colors.primary : Colors.gray}
            style={{ marginRight: 6 }}
          />
          <Text
            className="text-sm font-medium"
            style={{
              color: viewMode === 'vendor' ? Colors.primary : Colors.gray
            }}
          >
            Group by Vendor
          </Text>
        </Pressable>
      </View>

      {/* Status Filter Chips */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="mb-4"
        contentContainerStyle={{ paddingHorizontal: 4 }}
      >
        {statusChips.map(chip => (
          <Pressable
            key={chip.value}
            className={classNames(
              'mr-2 rounded-full border px-3 py-1.5',
              statusFilter === chip.value
                ? 'border-primary bg-primary'
                : 'border-gray-200 bg-white'
            )}
            onPress={() => onStatusFilterChange(chip.value)}
          >
            <Text
              className={classNames(
                'text-xs font-medium',
                statusFilter === chip.value ? 'text-white' : 'text-gray-600'
              )}
            >
              {chip.label} ({chip.count})
            </Text>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  )
}

export default SearchAndFilters
