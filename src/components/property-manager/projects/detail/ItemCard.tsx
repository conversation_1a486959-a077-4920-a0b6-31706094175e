import React from 'react'
import { Pressable, Text, View } from 'react-native'

import { Checkbox } from '@/components/Checkbox'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import { StatusIndicator } from './StatusIndicator'

interface ItemData {
  id: string
  title: string
  category: string
  priority: string
  budget: number
  vendors: string
  status: string
  description: string
  area: string
  dueDate: string
  image: string
  vendorName: string
}

interface ItemCardProps {
  item: ItemData
  batchMode: boolean
  isSelected: boolean
  onLongPress: () => void
  onPress: () => void
  onToggleSelect: () => void
  isExpandable?: boolean
  isExpanded?: boolean
  onToggleExpansion?: () => void
}

export const ItemCard: React.FC<ItemCardProps> = ({
  item,
  batchMode,
  isSelected,
  onLongPress,
  onPress,
  onToggleSelect,
  isExpandable = false,
  isExpanded = false,
  onToggleExpansion
}) => {
  return (
    <Pressable
      className={classNames(
        'relative overflow-hidden rounded-xl bg-white',
        batchMode ? 'pl-12' : ''
      )}
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 3
      }}
      onLongPress={onLongPress}
      onPress={onPress}
    >
      {batchMode && (
        <View
          className="absolute left-3 top-3 z-10 rounded bg-white p-1"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 2
          }}
        >
          <Checkbox value={isSelected} onChange={onToggleSelect} />
        </View>
      )}

      <View className="border-b border-gray-100 p-4">
        <View className="mb-1 flex-row items-center gap-2">
          <View
            className="rounded-full px-2 py-1"
            style={{ backgroundColor: Colors['info-light'] }}
          >
            <Text
              className="text-xs font-semibold"
              style={{ color: Colors.info }}
            >
              {item.category}
            </Text>
          </View>
          {item.priority === 'High' && (
            <View
              className="rounded-full px-2 py-1"
              style={{ backgroundColor: Colors['danger-light'] }}
            >
              <Text
                className="text-xs font-semibold"
                style={{ color: Colors.danger }}
              >
                High Priority
              </Text>
            </View>
          )}
        </View>

        <Text className="mb-1 text-base font-semibold text-gray-900">
          {item.title}
        </Text>

        <Text className="text-gray-500 text-xs">
          Budget: ${item.budget.toLocaleString()} • Vendors: {item.vendors}
        </Text>
      </View>

      <View className="flex-row items-center justify-between p-4">
        <StatusIndicator status={item.status} />
        {isExpandable && (
          <Pressable
            onPress={onToggleExpansion}
            className="ml-2 rounded px-2 py-1"
            style={{ backgroundColor: Colors['light-gray'] }}
          >
            <Text className="text-xs text-gray-600">
              {isExpanded ? 'Less' : 'More'}
            </Text>
          </Pressable>
        )}
      </View>

      {/* Expandable Content */}
      {isExpandable && isExpanded && (
        <View
          className="border-t border-gray-100 p-4"
          style={{ backgroundColor: Colors['light-gray'] }}
        >
          <Text className="mb-2 text-sm text-gray-700">
            {item.description || 'No description available.'}
          </Text>
          {item.area && (
            <Text className="text-gray-500 mb-1 text-xs">
              Area: {item.area}
            </Text>
          )}
          {item.dueDate && (
            <Text className="text-gray-500 mb-1 text-xs">
              Due Date: {item.dueDate}
            </Text>
          )}
          {item.vendorName && (
            <Text className="text-gray-500 text-xs">
              Vendor: {item.vendorName}
            </Text>
          )}
        </View>
      )}
    </Pressable>
  )
}

export default ItemCard
