import React from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import Slider from '@react-native-community/slider'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'

import { Button } from '@/components/Button'
import { Checkbox } from '@/components/Checkbox'
import { useDict } from '@/store/dict'
import { Colors } from '@/theme/colors'

interface FilterOptions {
  areas: string[]
  statuses: string[]
  priceRange: [number, number]
}

interface AdvancedFiltersProps {
  isVisible: boolean
  onClose: () => void
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  onApplyFilters: () => Promise<void> | void
  onResetFilters: () => Promise<void> | void
}

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  isVisible,
  onClose,
  filters,
  onFiltersChange,
  onApplyFilters,
  onResetFilters
}) => {
  if (!isVisible) return null

  const { getDictItems } = useDict()

  // Fetch area options from dictionary
  const areaOptionsRequest = useRequest(() => getDictItems('PROPERTY_AREA'))

  // Fetch status options from dictionary
  const statusOptionsRequest = useRequest(() => getDictItems('ITEM_STATUS'))

  // Loading states for buttons
  const [isApplying, setIsApplying] = React.useState(false)
  const [isResetting, setIsResetting] = React.useState(false)

  // Add "All Areas" and "All Items" options
  const availableAreas = [
    { code: 'all', label: 'All Areas' },
    ...(areaOptionsRequest.data || [])
  ]

  const availableStatuses = [
    { code: 'all', label: 'All Items' },
    ...(statusOptionsRequest.data || [])
  ]

  const toggleArea = (areaCode: string) => {
    const newAreas = filters.areas.includes(areaCode)
      ? filters.areas.filter(a => a !== areaCode)
      : [...filters.areas, areaCode]

    onFiltersChange({
      ...filters,
      areas: newAreas
    })
  }

  const toggleStatus = (statusCode: string) => {
    const newStatuses = filters.statuses.includes(statusCode)
      ? filters.statuses.filter(s => s !== statusCode)
      : [...filters.statuses, statusCode]

    onFiltersChange({
      ...filters,
      statuses: newStatuses
    })
  }

  const handlePriceRangeChange = (value: number) => {
    onFiltersChange({
      ...filters,
      priceRange: [filters.priceRange[0], value]
    })
  }

  // Handle apply filters with API request
  const handleApplyFilters = async () => {
    setIsApplying(true)
    try {
      // Call the parent's apply filters function
      await onApplyFilters()
      // Close the modal after successful application
      onClose()
    } catch (error) {
      console.error('Failed to apply filters:', error)
    } finally {
      setIsApplying(false)
    }
  }

  // Handle reset filters with API request
  const handleResetFilters = async () => {
    setIsResetting(true)
    try {
      // Call the parent's reset filters function
      await onResetFilters()
    } catch (error) {
      console.error('Failed to reset filters:', error)
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="absolute inset-0 z-50 bg-black/50">
        <View className="flex-1 justify-end">
          <View className="max-h-[80%] rounded-t-3xl bg-white">
            {/* Header */}
            <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
              <Text className="text-lg font-semibold text-gray-900">
                Advanced Filters
              </Text>
              <Pressable onPress={onClose} className="p-2">
                <FontAwesome6 name="xmark" size={20} color={Colors.gray} />
              </Pressable>
            </View>

            <ScrollView className="flex-1 px-6 py-4">
              {/* Areas Filter */}
              <View className="mb-6">
                <Text className="mb-3 text-base font-medium text-gray-900">
                  Areas
                </Text>
                <View className="flex-row flex-wrap gap-3">
                  {availableAreas.map(area => (
                    <View key={area.code} className="w-[48%]">
                      <Checkbox
                        value={filters.areas.includes(area.code || '')}
                        onChange={() => toggleArea(area.code || '')}
                      >
                        {area.label}
                      </Checkbox>
                    </View>
                  ))}
                </View>
              </View>

              {/* Status Filter */}
              <View className="mb-6">
                <Text className="mb-3 text-base font-medium text-gray-900">
                  Status
                </Text>
                <View className="flex-row flex-wrap gap-3">
                  {availableStatuses.map(status => (
                    <View key={status.code} className="w-[48%]">
                      <Checkbox
                        value={filters.statuses.includes(status.code || '')}
                        onChange={() => toggleStatus(status.code || '')}
                      >
                        {status.label}
                      </Checkbox>
                    </View>
                  ))}
                </View>
              </View>

              {/* Price Range Filter */}
              <View className="mb-6">
                <Text className="mb-3 text-base font-medium text-gray-900">
                  Price Range
                </Text>
                <View>
                  <View className="flex-row justify-between">
                    <Text className="text-gray-500 text-xs">$0</Text>
                    <Text className="text-gray-500 text-xs">$10,000</Text>
                  </View>
                  <Slider
                    style={{ width: '100%', height: 40 }}
                    minimumValue={0}
                    maximumValue={10000}
                    value={filters.priceRange[1]}
                    onValueChange={handlePriceRangeChange}
                    minimumTrackTintColor={Colors.primary}
                    maximumTrackTintColor={Colors.light}
                    thumbTintColor={Colors.primary}
                    step={100}
                  />
                </View>
              </View>
            </ScrollView>

            {/* Footer Actions */}
            <View className="border-t border-gray-100 px-6 py-4">
              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onPress={handleResetFilters}
                  loading={isResetting}
                  disabled={isApplying || isResetting}
                >
                  {isResetting ? 'Resetting...' : 'Reset Filters'}
                </Button>
                <Button
                  variant="primary"
                  className="flex-1"
                  onPress={handleApplyFilters}
                  loading={isApplying}
                  disabled={isApplying || isResetting}
                >
                  {isApplying ? 'Applying...' : 'Apply Filters'}
                </Button>
              </View>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  )
}
