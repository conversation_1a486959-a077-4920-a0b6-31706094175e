import React from 'react'
import { Text, View } from 'react-native'

import Card from './Card'

interface InfoRowProps {
  label: string
  value: string | number
}

function InfoRow({ label, value }: InfoRowProps) {
  return (
    <View className="mb-2 flex-row">
      <Text className="w-32 text-sm font-bold">{label}:</Text>
      <Text className="flex-1 text-sm">{value}</Text>
    </View>
  )
}

interface ProjectDescriptionProps {
  description: string
  projectType: string
  affectedUnits: number
  priority: string
  projectManager: string
}

export function ProjectDescription({
  description,
  projectType,
  affectedUnits,
  priority,
  projectManager
}: ProjectDescriptionProps) {
  return (
    <Card>
      <Text className="text-md mb-2 font-bold">Description</Text>
      <Text className="mb-3 text-sm">{description}</Text>
      <InfoRow label="Project Type" value={projectType} />
      <InfoRow label="Affected Units" value={affectedUnits} />
      <InfoRow label="Priority" value={priority} />
      <InfoRow label="Project Manager" value={projectManager} />
    </Card>
  )
}
