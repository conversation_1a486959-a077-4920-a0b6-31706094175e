import React from 'react'
import { Text, View } from 'react-native'

import { ShadowStyles } from '@/theme/colors'

interface QuickInfoItemProps {
  value: string
  label: string
}

function QuickInfoItem({ value, label }: QuickInfoItemProps) {
  return (
    <View
      className="flex-1 items-center rounded-default bg-white px-2 py-3"
      style={ShadowStyles.sm}
    >
      <Text className="mb-1 text-base font-bold text-primary">{value}</Text>
      <Text className="text-xs text-gray">{label}</Text>
    </View>
  )
}

interface QuickInfoGridProps {
  items: QuickInfoItemProps[]
}

export function QuickInfoGrid({ items }: QuickInfoGridProps) {
  return (
    <View className="mb-4 flex-row gap-2">
      {items.map((item, index) => (
        <QuickInfoItem key={index} value={item.value} label={item.label} />
      ))}
    </View>
  )
}
