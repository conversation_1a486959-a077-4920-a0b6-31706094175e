import React from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'

import PhotoSwiper from '@/components/PhotoSwiper'
import { Colors } from '@/theme/colors'
import type { ProjectWorkType } from '@/types'

interface PropertyPhoto {
  fileId?: number
  fileUrl?: string
  fileName?: string
  category?: string
  deleted?: boolean
}

interface ProjectHeaderProps {
  property: {
    name: string
    photos: PropertyPhoto[]
    address: string
  }
  project: {
    name: string
    units: string
    type: string
    projectType?: ProjectWorkType
  }
}

export function ProjectHeader({ project, property }: ProjectHeaderProps) {
  const getBadgeStyle = () => {
    if (project.projectType === 'WORK_ORDER') {
      return {
        backgroundColor: '#FF9800',
        textColor: Colors.white
      }
    }
    return {
      backgroundColor: Colors['primary-light'],
      textColor: Colors.primary
    }
  }

  const badgeStyle = getBadgeStyle()

  const swiperPhotos =
    property.photos.length > 0
      ? property.photos
          .filter(photo => photo.fileUrl && !photo.deleted)
          .map(photo => photo.fileUrl!)
      : []

  return (
    <View className="relative mb-4 h-52 overflow-hidden rounded-lg">
      <PhotoSwiper
        photos={swiperPhotos}
        imageClassName="h-52"
        imageHeight={208}
      />

      <LinearGradient
        colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0.7)']}
        style={StyleSheet.absoluteFill}
      >
        <View className="absolute bottom-0 w-full p-4">
          <View className="mb-1 flex-row items-center">
            <FontAwesome6
              name="building"
              size={12}
              color={Colors.white}
              style={{ marginRight: 4 }}
            />
            <Text className="text-xs text-white opacity-90">
              {property.name}
            </Text>
          </View>
          <Text className="mb-1 text-2xl font-bold text-white">
            {project.name}
          </Text>
          <View className="mb-2 flex-row items-center">
            <FontAwesome6
              name="location-dot"
              solid
              size={12}
              color={Colors.white}
              style={{ marginRight: 4 }}
            />
            <Text className="text-sm text-white opacity-90">
              {property.address || project.units}
            </Text>
          </View>
          <View
            className="self-start rounded px-3 py-1"
            style={{ backgroundColor: badgeStyle.backgroundColor }}
          >
            <Text
              className="text-xs font-semibold"
              style={{ color: badgeStyle.textColor }}
            >
              {project.type}
            </Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  )
}
