import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

interface EmptyStateProps {
  type: 'items' | 'vendors' | 'search'
  hasData: boolean
}

export const EmptyState: React.FC<EmptyStateProps> = ({ type, hasData }) => {
  const getEmptyStateConfig = () => {
    switch (type) {
      case 'items':
        return {
          icon: 'clipboard-list' as const,
          title: hasData ? 'No Matching Items' : 'No Items Found',
          description: hasData
            ? 'Try adjusting your search or filter criteria.'
            : "This project doesn't have any items yet."
        }
      case 'vendors':
        return {
          icon: 'user-tie' as const,
          title: 'No Vendors Found',
          description: "This project doesn't have any vendors assigned yet."
        }
      case 'search':
        return {
          icon: 'search' as const,
          title: 'No Results Found',
          description: 'Try different search terms or adjust your filters.'
        }
      default:
        return {
          icon: 'exclamation-circle' as const,
          title: 'No Data',
          description: 'No information available.'
        }
    }
  }

  const config = getEmptyStateConfig()

  return (
    <View className="items-center py-12">
      <FontAwesome6
        name={config.icon}
        size={48}
        color={Colors.gray}
        style={{ marginBottom: 16 }}
      />
      <Text className="mb-2 text-lg font-semibold text-gray-600">
        {config.title}
      </Text>
      <Text className="text-gray-500 text-center">{config.description}</Text>
    </View>
  )
}

export default EmptyState
