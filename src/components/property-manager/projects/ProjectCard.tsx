import { Image, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { router } from 'expo-router'

import { Button, Progress } from '@/components'
import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'
import type { Project } from '@/types'
import { getFullAddr } from '@/utils/addr'
import classNames from '@/utils/classname'

export function ProjectCard({ project }: { project: Project }) {
  const getStatusColor = () => {
    switch (project.status) {
      case 'IN_PROGRESS':
        return {
          bg: Colors['primary-light'],
          text: Colors.primary
        }
      case 'PENDING_QUOTES':
        return {
          bg: Colors['info-light'],
          text: Colors.info
        }
      case 'COMPLETED':
        return {
          bg: Colors['success-light'],
          text: Colors.success
        }
      default:
        return {
          bg: Colors['primary-light'],
          text: Colors.primary
        }
    }
  }

  const statusColors = getStatusColor()

  return (
    <View
      className={classNames(
        'mb-3.5 rounded-xl border border-border bg-white p-3.5'
      )}
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-start justify-between">
        <View>
          <Text className="mb-1 text-base font-semibold text-dark">
            {project.projectName}
          </Text>
          <Text className="text-xs text-gray">{getFullAddr(project)}</Text>
        </View>
        <View
          className="rounded-lg px-2 py-0.5"
          style={{ backgroundColor: statusColors.bg }}
        >
          <Text
            className="text-xs font-medium"
            style={{ color: statusColors.text }}
          >
            {project.status}
          </Text>
        </View>
      </View>

      <View className="mb-3 flex-row gap-4">
        <View className="flex flex-row items-center gap-1.5">
          <FontAwesome6 name="calendar-alt" size={12} color={Colors.gray} />
          <Text className="text-xs text-gray">
            Started: {project.startDate || 'N/A'}
          </Text>
        </View>
        <View className="flex flex-row items-center gap-1.5">
          <FontAwesome6 name="clock" size={12} color={Colors.gray} />
          <Text className="text-xs text-gray">
            Due: {project.endDate || 'N/A'}
          </Text>
        </View>
      </View>

      <View className="mb-3">
        <View className="mb-1 flex-row justify-between">
          <Text className="text-xs text-gray">Progress</Text>
          <Text className="text-xs text-gray">{project.completedPercent}</Text>
        </View>
        <Progress
          percentage={
            project.completedPercent ? parseInt(project.completedPercent) : 0
          }
        />
      </View>

      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-xs font-medium">
          Vendors ({project.assignedVendorCount})
        </Text>
        <View className="flex-row">
          {project.vendorPhotoList?.map((avatar, index) => (
            <Image
              key={index}
              source={{ uri: avatar }}
              className="h-7 w-7 rounded-full border-2 border-white"
              style={{ marginLeft: index > 0 ? -8 : 0 }}
            />
          ))}
        </View>
      </View>

      <View className="mt-3 flex flex-row justify-center border-t border-t-border pt-3">
        <Button
          leftIcon="eye"
          variant="primary"
          block={false}
          onPress={() => {
            router.push({
              pathname: '/property-manager/project/[id]',
              params: { id: project.projectId! }
            })
          }}
        >
          View Details
        </Button>
      </View>
    </View>
  )
}
