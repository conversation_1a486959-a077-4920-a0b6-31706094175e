import React, { useState } from 'react'
import { FlatList, Modal, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'

import { client } from '@/services/api'
import { colors } from '@/theme/colors'
import type { PropertyType } from '@/types/property'
import { getFullAddr } from '@/utils/addr'
import classNames from '@/utils/classname'

import { useProject } from './context'

interface PropertySelectorProps {
  selectDefault?: boolean
  value?: number
  onChange?: (propertyId: number) => void
  onChangeProperty?: (property: PropertyType) => void
}

export const PropertySelector = ({
  selectDefault = true,
  value,
  onChange,
  onChangeProperty
}: PropertySelectorProps) => {
  const { setProperties } = useProject()
  const [modalVisible, setModalVisible] = useState(false)
  const request = useRequest(async () => {
    const { data } = await client.POST('/api/v1/pm/property/list', {
      body: {
        pageNum: 1,
        pageSize: 999
      }
    })
    const list = data?.data?.list
    setProperties(list ?? [])
    if (!value && list?.length && selectDefault) {
      onChange?.(list[0]!.propertyId!)
      onChangeProperty?.(list[0]!)
    }
    return list ?? []
  })

  const selected = request.data?.find(
    (item: PropertyType) => item.propertyId === value
  )

  return (
    <>
      <Pressable
        onPress={() => setModalVisible(true)}
        className={classNames(
          'flex-row items-center rounded-lg border border-[#d1d5db] bg-white p-3',
          !selected && 'opacity-60'
        )}
      >
        <View className="flex-1">
          <Text className="mb-0.5 font-semibold">
            {selected ? selected.propertyName : 'Please select'}
          </Text>
          <Text className="text-sm text-gray">{getFullAddr(selected)}</Text>
        </View>
        {selected && (
          <View className="ml-2 rounded-full bg-[#3b82f6] px-2 py-0.5">
            <Text className="text-xs text-white">{selected.propertyType}</Text>
          </View>
        )}
        <FontAwesome6
          name="chevron-down"
          size={14}
          color={colors.palette.neutral400}
          style={{ marginLeft: 8 }}
        />
      </Pressable>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable
          className="flex-1 items-center justify-end bg-black/50"
          onPress={() => setModalVisible(false)}
        >
          <View className="max-h-[70%] w-full rounded-t-lg bg-white p-4">
            <View className="mb-2 flex-row items-center border-b border-b-border pb-4">
              <Text className="mr-auto text-base text-gray">
                Select a property
              </Text>
              <Pressable onPress={() => setModalVisible(false)}>
                <Text className="text-sm font-medium text-primary">Done</Text>
              </Pressable>
            </View>
            <FlatList
              data={request.data || []}
              keyExtractor={item => item.propertyId?.toString() || ''}
              renderItem={({ item }) => (
                <Pressable
                  className={classNames(
                    'rounded-default px-2 py-3',
                    item.propertyId === value && 'bg-primary/10'
                  )}
                  onPress={() => {
                    onChange?.(item.propertyId!)
                    onChangeProperty?.(item)
                    setModalVisible(false)
                  }}
                >
                  <View className="flex-row items-center">
                    <View className="flex-1">
                      <Text
                        className={classNames(
                          'text-base',
                          item.propertyId === value
                            ? 'text-primary'
                            : 'text-black'
                        )}
                      >
                        {item.propertyName}
                      </Text>
                      <Text className="text-xs text-gray">
                        {item.streetAddress}, {item.city}, {item.state}{' '}
                        {item.zipCode}
                      </Text>
                    </View>
                    <View className="ml-2 rounded-full bg-[#3b82f6] px-2 py-0.5">
                      <Text className="text-xs text-white">
                        {item.propertyType}
                      </Text>
                    </View>
                  </View>
                </Pressable>
              )}
              ListEmptyComponent={
                <Text className="py-4 text-center text-gray">
                  No properties found
                </Text>
              }
              style={{ maxHeight: 320 }}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  )
}
