import { useState } from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

interface PropertyInfoCardProps {
  name: string
  address: string
  type: string
  imageUrl: string
  details: {
    sqft: number
    beds: number
    baths: number
  }
}

const PropertyInfoCard = ({
  name,
  address,
  type,
  imageUrl,
  details
}: PropertyInfoCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <View className="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
      <Pressable
        onPress={() => setIsExpanded(!isExpanded)}
        className="flex-row items-center justify-between p-4"
      >
        <View className="flex-1">
          <View className="flex-row items-center gap-2">
            <FontAwesome6
              name="location-dot"
              size={14}
              color={Colors.gray[500]}
            />
            <Text className="text-gray-500 text-sm">{address}</Text>
          </View>
          <View className="mt-1.5 rounded-full bg-green-100 px-2 py-0.5">
            <Text className="text-xs font-medium text-success">{type}</Text>
          </View>
        </View>
        <FontAwesome6
          name="chevron-down"
          size={14}
          color={Colors.gray[400]}
          style={{ transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }}
        />
      </Pressable>

      {isExpanded && (
        <View className="border-t border-gray-100 px-4 pb-4">
          <Text className="mb-4 text-lg font-semibold text-dark">{name}</Text>

          <View className="mb-4 h-40 overflow-hidden rounded-lg">
            <Image
              source={{ uri: imageUrl }}
              className="h-full w-full"
              resizeMode="cover"
            />
          </View>

          <View className="space-y-2">
            <View className="flex-row items-center gap-2">
              <FontAwesome6 name="location-dot" size={14} color={Colors.gray} />
              <Text className="text-gray-500 text-sm">{address}</Text>
            </View>

            <View className="flex-row flex-wrap gap-3">
              <View className="flex-row items-center gap-1">
                <FontAwesome6
                  name="ruler-combined"
                  size={14}
                  color={Colors.gray}
                />
                <Text className="text-xs text-gray">
                  {details.sqft} sq. ft.
                </Text>
              </View>
              <View className="flex-row items-center gap-1">
                <FontAwesome6 name="bed" size={14} color={Colors.gray[500]} />
                <Text className="text-gray-500 text-xs">
                  {details.beds} Beds
                </Text>
              </View>
              <View className="flex-row items-center gap-1">
                <FontAwesome6 name="bath" size={14} color={Colors.gray[500]} />
                <Text className="text-gray-500 text-xs">
                  {details.baths} Baths
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}

export default PropertyInfoCard
