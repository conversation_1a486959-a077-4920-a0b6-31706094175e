import { createContext, useContext, useMemo, useState } from 'react'
import { useRequest } from 'ahooks'
import type { Result } from 'ahooks/lib/useRequest/src/types'
import { useRouter } from 'expo-router'
import type { ReactNode } from 'react'
import { Toast } from 'toastify-react-native'

import { useFormStorage } from '@/hooks/useFormStorage'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useSelectVendor } from '@/store/selectVendor'
import type { ProjectWorkType } from '@/types'
import type { PropertyType } from '@/types/property'

import type { AddedItem } from './types'

export type ProjectFormData = Omit<
  components['schemas']['ProjectInfo'],
  'projectType' | 'estimateStartDate' | 'estimateCompleteDate'
> & {
  projectType: ProjectWorkType
  selectedProperty?: PropertyType
  estimateStartDate: Date | null
  estimateCompleteDate: Date | null
}

export type SelectedVendor = components['schemas']['VendorSelectDTO']

const initialFormData: ProjectFormData = {
  projectId: undefined,
  propertyId: 0,
  managerId: undefined,
  ownerId: undefined,
  projectType: 'REHAB',
  priority: 'MEDIUM',
  affectedUnits: '',
  description: '',
  projectName: '',
  emptyRehabVendorId: undefined,
  estimateStartDate: null,
  estimateCompleteDate: null,
  estimateBudget: undefined,
  additionalNotes: '',
  items: [],
  selectedProperty: undefined
}

interface ProjectContextType {
  editingItem: AddedItem | null
  formData: ProjectFormData
  properties: PropertyType[]
  selectedProperty: PropertyType | undefined
  selectedVendor: SelectedVendor | undefined
  selectVendor: (vendor: SelectedVendor | undefined) => void
  setEditingItem: (item: AddedItem | null) => void
  updateFormData: (data: Partial<ProjectFormData>) => void
  resetFormData: () => void
  clearFormData: () => void
  isLoaded: boolean
  currentStep: number
  setProperties: (properties: PropertyType[]) => void
  setCurrentStep: (step: number) => void
  handleBack: () => void
  handleContinue: () => void
  saveDraft: () => void
  submitRequest: Result<
    { success: boolean; projectId?: number } | undefined,
    [status?: 'DRAFT' | 'SUBMITTED' | undefined]
  >
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined)

export function ProjectProvider({ children }: { children: ReactNode }) {
  const { clearSelected } = useSelectVendor()
  const [currentStep, setCurrentStep] = useState(0)
  const router = useRouter()
  const [properties, setProperties] = useState<PropertyType[]>([])
  const [editingItem, setEditingItem] = useState<AddedItem | null>(null)
  const [selectedVendor, setSelectedVendor] = useState<
    SelectedVendor | undefined
  >()

  const {
    data: formData,
    updateData,
    resetData,
    clearData,
    isLoaded
  } = useFormStorage<ProjectFormData>({
    key: 'pm-project-form',
    initialValue: initialFormData,
    reviver(data) {
      if (data.estimateStartDate) {
        data.estimateStartDate = new Date(data.estimateStartDate)
      }
      if (data.estimateCompleteDate) {
        data.estimateCompleteDate = new Date(data.estimateCompleteDate)
      }
      return data
    }
  })

  const submitRequest = useRequest(
    async (status: 'DRAFT' | 'SUBMITTED' = 'SUBMITTED') => {
      const { estimateStartDate, estimateCompleteDate, ...rest } = {
        ...formData
      }
      const body: components['schemas']['ProjectInfo'] = rest
      // remove temp item id
      if (body.items?.length) {
        for (const item of body.items) {
          if (item.itemId && item.itemId < 0) {
            item.itemId = undefined
          }
        }
      }
      // selected vendor
      if (selectedVendor) {
        body.assignedVendorIds = `${selectedVendor.vendorId}`
      }
      // date format
      body.estimateStartDate = estimateStartDate?.toISOString()
      body.estimateCompleteDate = estimateCompleteDate?.toISOString()
      body.status = status
      const { data, error } = await client.POST('/api/v1/pm/project/add', {
        body
      })
      if (!error) {
        clearData()
        Toast.success(
          status === 'DRAFT' ? 'Project draft saved' : 'Project submitted'
        )
        // Return the project ID from the response
        return { success: true, projectId: data!.data! }
      }
      return { success: false, projectId: undefined }
    },
    {
      manual: true,
      onSuccess(result) {
        if (result?.success && !result.projectId) {
          // Only navigate back if we don't have a project ID (for draft saves)
          clearSelected()
          router.back()
        }
      }
    }
  )

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    } else {
      router.back()
    }
  }

  const handleContinue = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const saveDraft = async () => {
    const result = await submitRequest.runAsync('DRAFT')
    if (result?.success) {
      clearSelected()
      router.back()
    }
  }

  const selectedProperty = useMemo(
    () =>
      properties.find(property => property.propertyId === formData.propertyId),
    [properties, formData.propertyId]
  )

  const value = {
    formData,
    properties,
    selectedProperty,
    selectedVendor,
    selectVendor: setSelectedVendor,
    editingItem,
    setEditingItem,
    updateFormData: updateData,
    resetFormData: resetData,
    clearFormData: clearData,
    isLoaded,
    currentStep,
    setProperties,
    setCurrentStep,
    handleBack,
    handleContinue,
    saveDraft,
    submitRequest
  }

  return (
    <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>
  )
}

export function useProject() {
  const context = useContext(ProjectContext)
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider')
  }
  return context
}
