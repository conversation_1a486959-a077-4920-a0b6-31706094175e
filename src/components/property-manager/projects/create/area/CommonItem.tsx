import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface CommonItemProps {
  name: string
  onPress: () => void
  isShowMore?: boolean
}

const CommonItem = ({ name, onPress, isShowMore = false }: CommonItemProps) => {
  return (
    <Pressable
      onPress={onPress}
      className={classNames(
        'flex-row items-center justify-between rounded-lg border border-gray-200 bg-white p-3 shadow-sm',
        isShowMore &&
          'bg-gray-50 justify-center gap-2 border-dashed border-blue-100'
      )}
    >
      <Text className="text-sm font-medium text-gray-800">{name}</Text>
      {!isShowMore && (
        <View className="h-7 w-7 items-center justify-center rounded-full bg-blue-50">
          <FontAwesome6 name="plus" size={12} color={Colors.primary} />
        </View>
      )}
      {isShowMore && (
        <FontAwesome6 name="chevron-down" size={12} color={Colors.primary} />
      )}
    </Pressable>
  )
}

export default CommonItem
