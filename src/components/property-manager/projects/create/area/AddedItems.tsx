import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

import type { AddedItem as FullAddedItem } from '../types'

interface AddedItemsProps {
  items: FullAddedItem[]
  onItemRemove: (item: FullAddedItem) => void
  onItemEdit: (item: FullAddedItem) => void
}

export function AddedItems({
  items,
  onItemRemove,
  onItemEdit
}: AddedItemsProps) {
  return (
    <View className="mb-6">
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-lg font-semibold text-dark">Added Items</Text>
        <View className="rounded-full bg-gray-100 px-2.5 py-0.5">
          <Text className="text-xs font-medium text-gray">{items.length}</Text>
        </View>
      </View>
      <View className="space-y-2">
        {items.map((item, index) => (
          <Pressable
            key={`${index}`}
            className="flex-row items-center justify-between rounded-lg border border-gray-200 bg-white p-4"
            onPress={() => onItemEdit(item)}
          >
            <View>
              <Text className="text-sm font-medium text-dark">
                {item.itemName}
              </Text>
              <Text className="mt-0.5 text-xs text-gray">{item.areaName}</Text>
            </View>
            <Pressable
              className="h-6 w-6 items-center justify-center rounded-full bg-gray-100"
              onPress={() => {
                onItemRemove(item)
              }}
            >
              <FontAwesome6 name="xmark" size={12} color={Colors.gray} />
            </Pressable>
          </Pressable>
        ))}
        {items.length === 0 && (
          <View className="items-center justify-center rounded-lg border border-dashed border-gray-200 bg-white p-8">
            <FontAwesome6 name="clipboard-list" size={24} color={Colors.gray} />
            <Text className="mt-2 text-sm text-gray">No items added yet</Text>
          </View>
        )}
      </View>
    </View>
  )
}
