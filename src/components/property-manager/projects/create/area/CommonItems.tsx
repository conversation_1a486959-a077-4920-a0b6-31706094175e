import { useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'

import { Button } from '@/components/Button'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'
import type { DictItemType, DictType } from '@/types'

import type { SelectedArea } from '../types'

interface CommonItemsProps {
  selectedArea: SelectedArea
  onItemAdd: (item: DictItemType) => void
  onNewItem: () => void
  showCreate?: boolean
}

export function CommonItems({
  selectedArea,
  onItemAdd,
  onNewItem,
  showCreate = true
}: CommonItemsProps) {
  const [showAll, setShowAll] = useState(false)
  const { getDictItems } = useDict()
  const request = useRequest(
    async () => {
      const res = await getDictItems(
        `ITEM_AREA_${selectedArea.areaType}` as DictType
      )
      return res
    },
    {
      refreshDeps: [selectedArea.areaType]
    }
  )

  const itemsForArea = request.data
  const displayedItems = showAll ? itemsForArea : itemsForArea?.slice(0, 5)

  return (
    <View className="mb-6">
      <Text className="mb-4 text-lg font-semibold text-dark">Common Items</Text>
      <View className="flex flex-col gap-2">
        {displayedItems?.map(item => (
          <Pressable
            key={item.code}
            className="flex-row items-center justify-between rounded-lg border border-gray-200 bg-white p-4"
            onPress={() => onItemAdd(item)}
          >
            <Text className="text-sm text-gray">{item.label}</Text>
            <Pressable
              className="h-6 w-6 items-center justify-center rounded-full bg-primary-light"
              onPress={() => onItemAdd(item)}
            >
              <FontAwesome6 name="plus" size={12} color={Colors.primary} />
            </Pressable>
          </Pressable>
        ))}
        {itemsForArea && !showAll && itemsForArea.length > 5 && (
          <Pressable
            className="flex-row items-center justify-between rounded-lg border border-gray-200 bg-white p-4"
            onPress={() => setShowAll(true)}
          >
            <Text className="text-sm text-gray">Show More Items...</Text>
            <FontAwesome6 name="chevron-down" size={12} color={Colors.gray} />
          </Pressable>
        )}
      </View>

      {showCreate && (
        <Button
          className="mt-4 w-full"
          variant="primary"
          size="default"
          leftIcon="plus"
          onPress={onNewItem}
        >
          Create New Item
        </Button>
      )}
    </View>
  )
}
