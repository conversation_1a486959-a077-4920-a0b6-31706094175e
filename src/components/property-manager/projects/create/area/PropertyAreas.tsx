import { useState } from 'react'
import { Modal, Text, View } from 'react-native'
import { useRequest } from 'ahooks'

import { But<PERSON> } from '@/components/Button'
import { Form } from '@/components/Form'
import { FormItem } from '@/components/FormItem'
import { Input } from '@/components/Input'
import { useDict } from '@/store'
import type { DictItemType } from '@/types'

import type { SelectedArea } from '../types'
import { AreaItem } from './AreaItem'

interface PropertyAreasProps {
  selectedArea: SelectedArea | null
  setSelectedArea: (v: SelectedArea) => void
  onAreasFetched: (areas: DictItemType[]) => void
}

export function PropertyAreas({
  selectedArea,
  setSelectedArea,
  onAreasFetched
}: PropertyAreasProps) {
  const { getDictItems } = useDict()
  const [addAreaModal, setAddAreaModal] = useState<{
    open: boolean
    areaType?: string
    areaName?: string
  }>({ open: false })
  const areaRequest = useRequest(() => getDictItems('PROPERTY_AREA'), {
    onSuccess(v) {
      onAreasFetched(v)
    }
  })
  const addAreaForm = Form.useForm<{ name: string }>()

  const handleAreaSelect = (area: SelectedArea) => {
    setSelectedArea(area)
  }

  const handleAddArea = (area: DictItemType) => {
    setAddAreaModal({
      open: true,
      areaType: area.code ?? '',
      areaName: area.label
    })
  }

  const handleCloseModal = () => setAddAreaModal({ open: false })
  const _onAddArea = (values: { name: string }) => {
    setSelectedArea({
      areaName: values.name,
      areaType: addAreaModal.areaType!
    })
    handleCloseModal()
  }

  return (
    <View className="relative z-10 mb-6">
      <Text className="mb-4 text-lg font-semibold text-dark">
        Property Areas
      </Text>
      <View className="-mx-1 flex-row flex-wrap">
        {areaRequest.data?.map(area => (
          <View key={area.code} className="mb-2.5 w-1/3 px-1">
            <AreaItem
              area={area}
              selectedArea={selectedArea}
              onAreaSelect={handleAreaSelect}
              onAddArea={handleAddArea}
            />
          </View>
        ))}
      </View>
      <Modal
        visible={addAreaModal.open}
        transparent
        animationType="fade"
        onRequestClose={handleCloseModal}
      >
        <View className="flex-1 items-center justify-center bg-black/40">
          <View className="w-80 rounded-xl bg-white p-6">
            <Form<{ name: string }> form={addAreaForm} onFinish={_onAddArea}>
              <Text className="mb-1 text-lg font-bold text-dark">
                {`Add New ${addAreaModal.areaName}`}
              </Text>
              <FormItem
                name="name"
                rules={{ required: true }}
                label={`Enter a name for the new ${addAreaModal.areaName?.toLowerCase() ?? ''}`}
              >
                <Input placeholder={`e.g., Guest ${addAreaModal.areaName}`} />
              </FormItem>
              <View className="flex-row gap-2">
                <Button
                  className="flex-1"
                  variant="cancel"
                  onPress={handleCloseModal}
                >
                  Cancel
                </Button>
                <Button
                  className="flex-1"
                  variant="primary"
                  onPress={addAreaForm.submit}
                >
                  {`Add ${addAreaModal.areaName}`}
                </Button>
              </View>
            </Form>
          </View>
        </View>
      </Modal>
    </View>
  )
}
