import React, { useRef, useState } from 'react'
import { Modal, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors, ShadowStyles } from '@/theme/colors'
import type { DictItemType } from '@/types'
import classNames from '@/utils/classname'

import type { SelectedArea } from '../types'

interface AreaItemProps {
  area: DictItemType
  selectedArea: SelectedArea | null
  onAreaSelect: (area: SelectedArea) => void
  onAddArea: (area: DictItemType) => void
}

/**
 * AreaItem component for rendering property area item (single or dropdown type)
 * Keeps the same height for expanded/collapsed dropdown
 */
export function AreaItem({
  area,
  selectedArea,
  onAreaSelect,
  onAddArea
}: AreaItemProps) {
  // Height for dropdown to keep consistent
  const dropdownHeight = 84 // px, adjusted height

  // const isExpanded = expandedDropdown === area.id
  const [isExpanded, setIsExpanded] = useState(false)
  const [buttonLayout, setButtonLayout] = useState<{
    width: number
    height: number
    px: number
    py: number
  } | null>(null)
  const buttonRef = useRef<View>(null)

  const isSelected = selectedArea?.areaType === area.code

  const handleToggleDropdown = () => {
    if (area.options?.length) {
      if (isExpanded) {
        setIsExpanded(false)
      } else {
        buttonRef.current?.measure((_fx, _fy, width, height, px, py) => {
          setButtonLayout({ width, height, px, py })
          setIsExpanded(true)
        })
      }
    }
  }

  if (!area.options?.length) {
    return (
      <Pressable
        className={classNames(
          'flex-col items-center justify-center rounded-lg border p-4',
          isSelected
            ? 'border-primary bg-primary-light'
            : 'border-border bg-white'
        )}
        onPress={() =>
          onAreaSelect({
            areaName: area.label!,
            areaType: area.code!
          })
        }
        style={{ minHeight: dropdownHeight }}
      >
        <FontAwesome6
          name={area.icon}
          size={24}
          color={isSelected ? Colors.primary : Colors.gray}
          className="mb-2"
        />
        <Text
          className={classNames(
            'truncate text-center text-xs font-medium',
            isSelected ? 'text-primary' : 'text-gray'
          )}
        >
          {area.label}
        </Text>
      </Pressable>
    )
  }

  return (
    <>
      <Pressable
        ref={buttonRef}
        className={classNames(
          'flex-row items-center justify-between rounded-lg border p-4',
          isSelected
            ? 'border-primary bg-primary-light'
            : 'border-border bg-white'
        )}
        onPress={handleToggleDropdown}
        style={{ minHeight: dropdownHeight }}
      >
        <View className="flex-1 flex-col items-center">
          <FontAwesome6
            name={area.icon}
            size={24}
            color={isSelected ? Colors.primary : Colors.gray}
            className="mb-2"
          />
          <Text
            className={classNames(
              'truncate text-xs font-medium',
              isSelected ? 'text-primary' : 'text-gray'
            )}
          >
            {selectedArea?.areaType === area.code
              ? selectedArea?.areaName
              : area.label}
          </Text>
        </View>
        <FontAwesome6
          name="chevron-down"
          size={12}
          color={isSelected ? Colors.primary : Colors.gray}
          style={{
            transform: [{ rotate: isExpanded ? '180deg' : '0deg' }]
          }}
        />
      </Pressable>

      {isExpanded && buttonLayout && (
        <Modal
          transparent
          visible={isExpanded}
          animationType="fade"
          onRequestClose={() => setIsExpanded(false)}
        >
          <Pressable
            className="absolute inset-0"
            onPress={() => setIsExpanded(false)}
          />
          <View
            style={{
              position: 'absolute',
              top: buttonLayout.py + buttonLayout.height,
              left: buttonLayout.px,
              width: buttonLayout.width
            }}
            className="mt-1 gap-y-2 rounded-lg border border-border bg-white px-2 py-4 shadow-lg"
          >
            {area.options?.map(option => (
              <Pressable
                key={option.value}
                className={classNames(
                  'min-h-[64px] flex-row items-center rounded-md border border-border p-2',
                  selectedArea?.areaName === option.label
                    ? 'bg-gray-100'
                    : 'active:bg-gray-50'
                )}
                onPress={() => {
                  onAreaSelect({
                    areaName: option.label!,
                    areaType: area.code!
                  })
                  setIsExpanded(false)
                }}
                style={ShadowStyles.sm}
              >
                <FontAwesome6
                  name={area.icon}
                  size={16}
                  color={
                    selectedArea?.areaName === option.label
                      ? Colors.primary
                      : Colors.gray
                  }
                  className="mr-2"
                />
                <Text
                  className={classNames(
                    'text-xs',
                    selectedArea?.areaName === option.label
                      ? 'text-primary'
                      : 'text-gray'
                  )}
                >
                  {option.label}
                </Text>
              </Pressable>
            ))}
            <Pressable
              className="mt-1 flex-row items-center rounded-md border border-dashed border-primary bg-primary-light p-2"
              onPress={() => {
                onAddArea(area)
                setIsExpanded(false)
              }}
            >
              <FontAwesome6
                name="plus"
                size={12}
                color={Colors.primary}
                className="mr-2"
              />
              <Text className="text-xs font-medium text-primary">
                Add {area.label?.slice(0, -1)}
              </Text>
            </Pressable>
          </View>
        </Modal>
      )}
    </>
  )
}
