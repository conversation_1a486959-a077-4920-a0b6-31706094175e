import { useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import PhotoSwiper from '@/components/PhotoSwiper'
import { Colors, ShadowStyles } from '@/theme/colors'
import type { PropertyType } from '@/types/property'
import { getFullAddr } from '@/utils/addr'

export function PropertyInfoCollapsible({
  property
}: {
  property: PropertyType
}) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <View
      className="mb-6 overflow-hidden rounded-lg border border-border bg-white"
      style={ShadowStyles.sm}
    >
      {/* Header */}
      <Pressable
        className="flex-row items-center justify-between p-4"
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View className="flex-1">
          <View className="flex-row items-center">
            <FontAwesome6 name="location-dot" size={14} color={Colors.gray} />
            <Text className="ml-2 text-sm text-gray">
              {getFullAddr(property, { showZip: false })}
            </Text>
          </View>
          <View className="mt-2">
            <View className="inline-flex w-fit rounded-full bg-success-light px-2 py-0.5">
              <Text className="text-xs font-medium text-success">
                {property.propertyType}
              </Text>
            </View>
          </View>
        </View>
        <FontAwesome6
          name="chevron-down"
          size={14}
          color={Colors.gray}
          style={{ transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }}
        />
      </Pressable>

      {/* Content */}
      {isExpanded && (
        <View className="border-t border-gray-100 p-4">
          <Text className="mb-4 text-lg font-semibold text-dark">
            {property.propertyName}
          </Text>
          <View className="mb-4 h-40 overflow-hidden rounded-lg">
            <PhotoSwiper
              photos={property.photos?.map(i => i.fileUrl!) || []}
              imageClassName="h-full w-full"
              imageHeight={160}
            />
          </View>
          <View className="flex-row items-center">
            <FontAwesome6 name="location-dot" size={14} color={Colors.gray} />
            <Text className="ml-2 text-sm text-gray">
              {getFullAddr(property, { showZip: false })}
            </Text>
          </View>
          <View className="mt-3 flex-row flex-wrap gap-3">
            <View className="flex-row items-center">
              <FontAwesome6 name="building" size={14} color={Colors.gray} />
              <Text className="ml-2 text-sm text-gray">
                {property.propertyType}
              </Text>
            </View>
            <View className="flex-row items-center">
              <FontAwesome6 name="calendar" size={14} color={Colors.gray} />
              <Text className="ml-2 text-sm text-gray">
                Built {property.yearBuilt}
              </Text>
            </View>
            <View className="flex-row items-center">
              <FontAwesome6 name="ruler" size={14} color={Colors.gray} />
              <Text className="ml-2 text-sm text-gray">
                {property.sizeSqFt} sq. ft.
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}
