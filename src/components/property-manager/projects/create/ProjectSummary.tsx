import { Text, View } from 'react-native'

interface ProjectSummaryProps {
  totalBudge: number
  totalItems: number
  // timeline: string
  status: string
}

const ProjectSummary = ({
  totalBudge,
  totalItems,
  // timeline,
  status
}: ProjectSummaryProps) => {
  return (
    <View className="mb-6 rounded-lg bg-white p-4 shadow-sm">
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-lg font-semibold text-dark">Project Summary</Text>
        <Text className="text-lg font-bold text-primary">
          ${totalBudge.toLocaleString()}
        </Text>
      </View>

      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="mb-1 text-xs text-gray">Total Items</Text>
          <Text className="text-sm font-semibold text-dark">{totalItems}</Text>
        </View>
        {/* <View className="flex-1">
          <Text className="mb-1 text-xs text-gray">Timeline</Text>
          <Text className="text-sm font-semibold text-dark">
            {timeline || 'N/A'}
          </Text>
        </View> */}
        <View className="flex-1">
          <Text className="mb-1 text-xs text-gray">Status</Text>
          <Text className="text-sm font-semibold text-dark">{status}</Text>
        </View>
      </View>
    </View>
  )
}

export default ProjectSummary
