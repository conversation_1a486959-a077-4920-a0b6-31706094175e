import { Text, View } from 'react-native'

import { ProjectIcon } from '@/components'
import { ShadowStyles } from '@/theme/colors'
import type { ProjectWorkType } from '@/types'

interface ProjectTypeHeaderProps {
  type: ProjectWorkType
  title: string
  subtitle?: string
}

function ProjectTypeHeader({ type, title, subtitle }: ProjectTypeHeaderProps) {
  return (
    <View
      className="mb-5 flex flex-row items-center rounded-default border border-border bg-white p-4"
      style={ShadowStyles.sm}
    >
      <ProjectIcon type={type} className="mr-3 h-12 w-12 rounded-default" />
      <View className="flex-1">
        <Text className="text-base font-semibold text-dark">{title}</Text>
        {subtitle && <Text className="text-sm text-gray">{subtitle}</Text>}
      </View>
    </View>
  )
}

export default ProjectTypeHeader
