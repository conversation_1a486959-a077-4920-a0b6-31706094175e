import React, { Fragment } from 'react'
import { Text } from 'react-native'

import type { ProjectWorkType } from '@/types'
import { useProjectCreateStore } from '@/store/projectCreate'

import { ProjectTypeCard } from '../ProjectTypeCard'

export function Step1() {
  const { updateFormData, handleContinue } = useProjectCreateStore()

  const handleProjectTypeSelect = (type: ProjectWorkType) => {
    updateFormData({ projectType: type })
    handleContinue()
  }

  return (
    <Fragment>
      <Text className="mb-2 text-base font-semibold">
        What type of project do you want to create?
      </Text>
      <Text className="mb-6 text-sm text-gray">
        Choose a project type based on the scope and nature of work required.
      </Text>
      <ProjectTypeCard
        type="REHAB"
        onPress={() => handleProjectTypeSelect('REHAB')}
      />
      <ProjectTypeCard
        type="WORK_ORDER"
        onPress={() => handleProjectTypeSelect('WORK_ORDER')}
      />
    </Fragment>
  )
}
