import { useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { Toast } from 'toastify-react-native'

import { Button, ProjectIcon } from '@/components'
import { Colors } from '@/theme/colors'
import type { DictItemType } from '@/types'
import { useProjectCreateStore, useProjectSubmit } from '@/store/projectCreate'

import { AddedItems } from '../area/AddedItems'
import { AddEditItemModal } from '../area/AddEditItemModal'
import { CommonItems } from '../area/CommonItems'
import { PropertyAreas } from '../area/PropertyAreas'
import { PropertyInfoCollapsible } from '../PropertyInfoCollapsible'
import type { AddedItem, SelectedArea } from '../types'

export function Step3() {
  const {
    formData,
    editingItem: persistedEditingItem,
    selectedProperty,
    updateFormData,
    handleBack,
    handleContinue,
    saveDraft
  } = useProjectCreateStore()

  const submitRequest = useProjectSubmit()
  const [selectedArea, setSelectedArea] = useState<SelectedArea | null>(null)
  const [addedItems, setAddedItems] = useState<AddedItem[]>(
    formData.items || []
  )

  const [isModalVisible, setModalVisible] = useState(false)
  const [editingItem, setEditingItem] = useState<Partial<AddedItem> | null>(
    null
  )

  const handleCommonItemAdd = (item: DictItemType) => {
    setEditingItem({ itemName: item.label })
    setModalVisible(true)
  }

  const handleCreateNewItem = () => {
    setEditingItem(null) // This will open a blank form
    setModalVisible(true)
  }

  const handleAddedItemEdit = (item: AddedItem) => {
    setEditingItem(item)
    setModalVisible(true)
  }

  const handleItemRemove = (item: AddedItem) => {
    setAddedItems(prev => prev.filter(i => item !== i))
  }

  const handleModalSubmit = (values: AddedItem) => {
    if (editingItem && editingItem.itemId) {
      const index = addedItems.indexOf(editingItem as AddedItem)
      // Edit mode
      setAddedItems(prev =>
        prev.map((i, idx) => (index === idx ? { ...i, ...values } : i))
      )
    } else {
      // Add mode
      setAddedItems(prev => [
        ...prev,
        {
          itemId: -1 * Math.random(),
          ...values,
          ...selectedArea
        } as AddedItem
      ])
    }
    setModalVisible(false)
    setEditingItem(null)
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingItem(null)
  }

  const onSubmit = () => {
    if (!addedItems.length) {
      Toast.error('No items added')
      return
    }
    updateFormData({
      items: addedItems
    })
    handleContinue()
  }

  useEffect(() => {
    if (persistedEditingItem) {
      handleAddedItemEdit(persistedEditingItem)
    }
  }, [persistedEditingItem])

  return (
    <View className="flex-1">
      {/* Project Type Header */}
      <View className="mb-4 flex-row items-center border-b border-[#DFE1E6] pb-3">
        <ProjectIcon
          iconSize={14}
          type={formData.projectType!}
          className="mr-3 h-7 w-7 items-center justify-center rounded-full"
        />
        <Text className="text-base font-semibold text-dark">
          {formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Project
        </Text>
      </View>

      {/* Property Information */}
      {selectedProperty && (
        <PropertyInfoCollapsible property={selectedProperty} />
      )}

      {/* Property Areas */}
      <PropertyAreas
        selectedArea={selectedArea}
        setSelectedArea={setSelectedArea}
        onAreasFetched={v => {
          if (!selectedArea && v.length) {
            setSelectedArea({
              areaType: v[0]!.code!,
              areaName: v[0]!.label!
            })
          }
        }}
      />

      {/* Common Items */}
      {selectedArea && (
        <CommonItems
          selectedArea={selectedArea}
          onItemAdd={handleCommonItemAdd}
          onNewItem={handleCreateNewItem}
        />
      )}

      {/* Added Items */}
      <AddedItems
        items={addedItems}
        onItemRemove={handleItemRemove}
        onItemEdit={handleAddedItemEdit}
      />

      {/* Add/Edit Modal */}
      <AddEditItemModal
        visible={isModalVisible}
        initialValues={editingItem || undefined}
        onSubmit={handleModalSubmit}
        onCancel={handleModalCancel}
        isEdit={!!editingItem && !!editingItem.itemId}
      />
      {/* <ActionButtons onPrev={handleBack} onNext={onSubmit} /> */}
      <Button
        onPress={saveDraft}
        className="mt-6"
        block
        variant="outline"
        rightIcon="floppy-disk"
      >
        Save Draft
      </Button>
      <View className="mt-6 flex flex-row gap-x-4 bg-white">
        <Button
          onPress={handleBack}
          className="flex-1"
          variant="outline"
          leftIcon={
            <FontAwesome6 name="floppy-disk" size={14} color={Colors.dark} />
          }
        >
          Back to Details
        </Button>
        <Button
          onPress={onSubmit}
          className="flex-1"
          variant="primary"
          rightIcon={
            <FontAwesome6 name="arrow-right" size={14} color={Colors.white} />
          }
        >
          Review Items
        </Button>
      </View>
    </View>
  )
}
