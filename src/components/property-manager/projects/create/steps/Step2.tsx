import { useEffect } from 'react'
import { View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { router, useLocalSearchParams } from 'expo-router'

import { Button, Form, FormItem, Input, Select } from '@/components'
import SectionTitle from '@/components/SectionTitle'
import { useDict } from '@/store'
import { Colors, ShadowStyles } from '@/theme/colors'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

import type { ProjectFormData } from '../context'
import { useProject } from '../context'
import ProjectTypeHeader from '../ProjectTypeHeader'
import { PropertySelector } from '../PropertySelector'

export function Step2() {
  const {
    formData,
    selectedVendor,
    selectVendor,
    updateFormData,
    handleContinue,
    setCurrentStep,
    handleBack,
    saveDraft
  } = useProject()
  const form = Form.useForm<ProjectFormData>()
  const { getDictItems } = useDict()
  const priorityRequest = useRequest(() => getDictItems('PRIORITY'))

  // Listen for vendor selection from the vendor select page
  const params = useLocalSearchParams<{ selectedVendorId?: string }>()

  const handleAssignVendor = () => {
    router.push({
      pathname: '/property-manager/vendor/select',
      params: { workType: formData.projectType }
    })
  }

  // Handle vendor selection from vendor select page
  useEffect(() => {
    if (params.selectedVendorId && !selectedVendor) {
      // Fetch vendor details and set as selected vendor
      const fetchVendorDetails = async () => {
        try {
          const { data } = await client.POST('/api/v1/pm/vendor/list', {
            body: {
              pageNum: 1,
              pageSize: 1,
              vendorId: Number(params.selectedVendorId)
            }
          })
          const vendor = data?.data?.list?.[0]
          if (vendor) {
            selectVendor(vendor)
          }
        } catch (error) {
          console.error('Failed to fetch vendor details:', error)
        }
      }
      fetchVendorDetails()
    }
  }, [params.selectedVendorId, selectedVendor, selectVendor])

  useEffect(() => {
    if (selectedVendor) {
      setCurrentStep(3)
    }
  }, [selectedVendor])

  return (
    <>
      <ProjectTypeHeader
        type={formData.projectType!}
        title={`${formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Project`}
      />
      <Form
        className="flex-1"
        form={form}
        style={ShadowStyles.sm}
        onFinish={values => {
          console.log(values)
          updateFormData({
            ...values
          })
          handleContinue()
        }}
        initialValues={formData}
      >
        <View
          className="mb-5 rounded-lg border border-gray-200 bg-white p-4"
          style={ShadowStyles.sm}
        >
          <FormItem<ProjectFormData> name="propertyId" label="Select Property">
            <PropertySelector />
          </FormItem>
        </View>
        <View className="rounded-default border border-gray-200 bg-white p-5">
          <SectionTitle title="Project Information" className="mb-4" />
          <FormItem<ProjectFormData>
            name="projectName"
            label="Project Name"
            rules={{
              required: { value: true, message: 'Project name is required' }
            }}
          >
            <Input placeholder="Enter project name (Optional)" />
          </FormItem>

          <FormItem<ProjectFormData>
            name="description"
            label="Description"
            rules={{
              required: {
                value: true,
                message: 'Please enter project description'
              }
            }}
          >
            <Input
              placeholder="Describe the project scope and requirements (optional)"
              multiline
              numberOfLines={4}
            />
          </FormItem>

          <FormItem<ProjectFormData>
            name="priority"
            label="Priority"
            rules={{
              required: { value: true, message: 'Priority is required' }
            }}
          >
            <Select
              placeholder="Select priority"
              options={
                priorityRequest.data?.map(item => ({
                  label: item.label ?? '',
                  value: item.code ?? ''
                })) || []
              }
            />
          </FormItem>
          {/* <View className="flex flex-row gap-3">
            <FormItem<ProjectFormData>
              className="flex-1"
              name="estimateStartDate"
              label="Start Date"
              dependencies={['estimateCompleteDate']}
              // rules={[{ required: true, message: 'Please select start date' }]}
            >
              {({ estimateCompleteDate }: { estimateCompleteDate: Date }) => {
                return (
                  <DatePicker
                    placeholder="Select start date"
                    maxDate={estimateCompleteDate}
                  />
                )
              }}
            </FormItem>
            <FormItem<ProjectFormData>
              className="flex-1"
              name="estimateCompleteDate"
              label="Estimated Completion"
              dependencies={['estimateStartDate']}
              // rules={[{ required: true, message: 'Please select completion date' }]}
            >
              {({ estimateStartDate }: { estimateStartDate: Date }) => {
                return (
                  <DatePicker
                    placeholder="Select completion date"
                    minDate={estimateStartDate}
                  />
                )
              }}
            </FormItem>
          </View> */}

          {/* <FormItem
            name="budget"
            label="Estimated Budget"
            // rules={[{ required: true, message: 'Please enter project budget' }]}
          >
            <NumberInput
              LeftAccessory={() => <Text className="text-gray">$</Text>}
              placeholder="Enter estimated budget"
            />
          </FormItem> */}

          <FormItem<ProjectFormData>
            name="additionalNotes"
            label="Additional Notes"
          >
            <Input
              placeholder="Any additional notes or special requirements"
              multiline
              numberOfLines={4}
            />
          </FormItem>
        </View>
      </Form>
      <View className="mt-6 flex flex-row gap-x-4 bg-white">
        <Button
          onPress={handleBack}
          className="flex-1"
          variant="outline"
          leftIcon={
            <FontAwesome6 name="arrow-left" size={14} color={Colors.dark} />
          }
        >
          Back
        </Button>
        <Button
          onPress={saveDraft}
          className="flex-1"
          variant="outline"
          rightIcon={
            <FontAwesome6 name="floppy-disk" size={14} color={Colors.white} />
          }
        >
          Save Draft
        </Button>
      </View>

      <View className="mt-4 flex flex-row gap-x-4 bg-white">
        <Button
          onPress={handleAssignVendor}
          className="flex-1"
          variant="success"
        >
          Assign Vendor to Scope
        </Button>
        <Button onPress={form.submit} className="flex-1" variant="primary">
          Scope On My Own
        </Button>
      </View>
    </>
  )
}
