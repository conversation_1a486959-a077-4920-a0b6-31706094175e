import { View } from 'react-native'
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6'

import { But<PERSON> } from '@/components/Button'
import { Colors } from '@/theme/colors'

const ActionButtons = ({
  onPrev,
  onNext,
  nextText = 'Continue'
}: {
  onPrev: () => void
  onNext: () => void
  nextText?: string
}) => {
  return (
    <View className="mt-6 flex flex-row gap-x-4 bg-white">
      <Button
        onPress={onPrev}
        className="flex-1"
        variant="outline"
        leftIcon={
          <FontAwesome6 name="arrow-left" size={14} color={Colors.dark} />
        }
      >
        Back
      </Button>
      <Button
        onPress={onNext}
        className="flex-1"
        variant="primary"
        rightIcon={
          <FontAwesome6 name="arrow-right" size={14} color={Colors.white} />
        }
      >
        {nextText}
      </Button>
    </View>
  )
}

export default ActionButtons
