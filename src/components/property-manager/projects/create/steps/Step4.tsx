import { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'
import { useRouter } from 'expo-router'

// import { format } from 'date-fns'
import { Button } from '@/components'

import { AssignVendorModal } from '../AssignVendorModal'
import { useProject } from '../context'
import ProjectSummary from '../ProjectSummary'
import ProjectTypeHeader from '../ProjectTypeHeader'
import { PropertyInfoCollapsible } from '../PropertyInfoCollapsible'
import { RehabItemCollapsible } from '../RehabItemCollapsible'
import { VendorInfoCard } from '../VendorInfoCard'

// Format timeline string from two Date objects
// function formatTimeline(start: Date | null, end: Date | null) {
//   if (!start || !end) return ''
//   const startDate = start
//   const endDate = end
//   // Same year
//   if (startDate.getFullYear() === endDate.getFullYear()) {
//     // Same month
//     if (startDate.getMonth() === endDate.getMonth()) {
//       // e.g. Jun 15-25, 2023
//       return `${format(startDate, 'MMM d')}-${format(endDate, 'd, yyyy')}`
//     }
//     // e.g. Jun 15 - Jul 25, 2023
//     return `${format(startDate, 'MMM d')} - ${format(endDate, 'MMM d, yyyy')}`
//   }
//   // Cross year
//   return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`
// }

export function Step4() {
  const router = useRouter()
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [submittedProjectId, setSubmittedProjectId] = useState<
    number | undefined
  >()

  const {
    formData,
    selectedVendor,
    setEditingItem,
    updateFormData,
    selectedProperty,
    handleBack,
    submitRequest
  } = useProject()

  const handleSubmitProject = async () => {
    const result = await submitRequest.runAsync('SUBMITTED')
    if (result?.success) {
      setSubmittedProjectId(result.projectId)
      setShowAssignModal(true)
    }
  }

  const handleAssignLater = () => {
    router.back()
  }

  const onChangeVendor = () => {
    router.push('/property-manager/vendor/select')
  }

  return (
    <ScrollView className="space-y-5">
      <ProjectTypeHeader
        type={formData.projectType!}
        title={`${formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Project`}
        subtitle={`${
          formData.projectType === 'REHAB'
            ? 'Property renovation and improvement'
            : 'Work Order'
        } Project`}
      />
      {/* Property Information */}
      {selectedProperty && (
        <PropertyInfoCollapsible property={selectedProperty!} />
      )}

      <ProjectSummary
        totalBudge={
          formData.items?.reduce((sum, item) => sum + (item.budget || 0), 0) ||
          0
        }
        totalItems={formData.items?.length || 0}
        // timeline="Jun 15 - Jul 25, 2023"
        // timeline={formatTimeline(
        //   formData.estimateStartDate,
        //   formData.estimateCompleteDate
        // )}
        status="Ready for Submission"
      />

      {selectedVendor ? (
        <VendorInfoCard
          vendor={selectedVendor}
          onChangeVendor={onChangeVendor}
        />
      ) : (
        <View className="rounded-lg bg-white shadow-sm">
          <View className="mb-6 flex-row items-center">
            <Text className="text-lg font-semibold text-dark">
              {formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Items
            </Text>
            <View className="ml-auto flex h-6 w-6 flex-row items-center justify-center rounded-full bg-primary">
              <Text className="text-xs text-white">
                {formData.items?.length}
              </Text>
            </View>
          </View>
          <View className="space-y-3">
            {formData.items?.map((item, index) => (
              <RehabItemCollapsible
                key={`${index}`}
                item={item}
                onEdit={() => {
                  setEditingItem(item)
                  handleBack()
                }}
                onDelete={() => {
                  let items = formData.items
                  items = items?.filter(i => i !== item)
                  updateFormData({
                    items
                  })
                }}
              />
            ))}
          </View>

          <Button
            variant="outline"
            onPress={handleBack}
            className="my-4"
            leftIcon="plus"
          >
            Add More Items
          </Button>
        </View>
      )}

      <View className="gap-y-3 pb-6">
        <Button
          variant="outline"
          onPress={async () => {
            const result = await submitRequest.runAsync('DRAFT')
            if (result?.success) {
              router.back()
            }
          }}
          leftIcon="save"
          loading={submitRequest.loading}
        >
          Save Draft
        </Button>

        <Button
          variant="primary"
          onPress={handleSubmitProject}
          leftIcon="check"
          loading={submitRequest.loading}
        >
          Submit Project
        </Button>
      </View>

      <AssignVendorModal
        visible={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        projectId={submittedProjectId}
        onAssignLater={handleAssignLater}
      />
    </ScrollView>
  )
}
