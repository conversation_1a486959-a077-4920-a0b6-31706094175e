import { Image, Text, View } from 'react-native'

import { Button } from '@/components'
import SectionTitle from '@/components/SectionTitle'

import type { SelectedVendor } from './context'

interface VendorInfoCardProps {
  vendor: SelectedVendor
  onChangeVendor: () => void
}

export function VendorInfoCard({
  vendor,
  onChangeVendor
}: VendorInfoCardProps) {
  return (
    <View className="rounded-lg bg-white p-4 shadow-sm">
      <SectionTitle title="Vendor" />

      <View className="mb-4 flex-row items-center">
        {vendor.avatar ? (
          <Image
            source={{ uri: vendor.avatar }}
            className="h-12 w-12 rounded-full"
          />
        ) : (
          <View className="h-12 w-12 items-center justify-center rounded-full bg-gray-200">
            <Text className="text-lg font-semibold text-gray-600">
              {vendor.userName?.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        <View className="ml-3 flex-1">
          <Text className="text-base font-semibold text-dark">
            {vendor.userName}
          </Text>
          <Text className="text-sm text-gray-600">{vendor.specialties}</Text>
        </View>
      </View>

      <Button
        variant="outline"
        onPress={onChangeVendor}
        leftIcon="user-tie"
        className="mt-2"
      >
        Change Vendor
      </Button>
    </View>
  )
}
