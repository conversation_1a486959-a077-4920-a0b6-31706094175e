import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'

import { Button } from '@/components'
import { Colors, Shadows } from '@/theme/colors'
import type { ProjectWorkType } from '@/types'

interface ProjectTypeCardProps {
  type: ProjectWorkType
  onPress?: () => void
}

const ProjectTypeConfig = {
  REHAB: {
    title: 'Rehab Project',
    subtitle: 'For major renovations and property improvements',
    icon: 'hammer',
    iconBackground: ['#28c76f', '#1f9d57'],
    checkIconBackground: Colors['success-light'],
    iconColor: Colors.success,
    description:
      'Renovation projects are comprehensive renovations or significant property improvements that typically involve multiple trades, a longer timeline, and a larger budget.',
    features: [
      'Major renovations or property improvements',
      'Multiple trades and vendors involved',
      'Typically higher budget ($2,000+)',
      'Longer timeline (weeks to months)'
    ]
  },
  WORK_ORDER: {
    title: 'Work Order Project',
    subtitle: 'For maintenance, repairs, and smaller tasks',
    icon: 'screwdriver-wrench',
    iconBackground: ['#4f6df5', '#3b5fe2'],
    checkIconBackground: Colors['primary-light'],
    iconColor: Colors.primary,
    description:
      'Work orders are smaller, focused projects that typically address specific maintenance issues, minor repairs, or limited improvements that can be completed quickly.',
    features: [
      'Maintenance issues or minor repairs',
      'Usually one or two trades involved',
      'Typically lower budget (under $2,000)',
      'Shorter timeline (hours to days)'
    ]
  }
} as const

function ProjectTypeCard({ type, onPress }: ProjectTypeCardProps) {
  const config = ProjectTypeConfig[type]

  return (
    <View
      className="mb-4 rounded-default bg-white p-4"
      style={{
        boxShadow: Shadows.default
      }}
    >
      <View className="mb-4 flex-row items-center">
        <LinearGradient
          colors={config.iconBackground}
          className="mr-4 h-[60px] w-[60px] rounded-full"
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ borderRadius: 100 }}
        >
          <View className="mr-4 flex h-[60px] w-[60px] items-center justify-center rounded-full">
            <FontAwesome6 name={config.icon} size={24} color={Colors.white} />
          </View>
        </LinearGradient>
        <View className="flex-1">
          <Text className="text-lg font-bold">{config.title}</Text>
          <Text className="text-sm text-gray">{config.subtitle}</Text>
        </View>
      </View>

      <Text className="mb-4 text-sm text-dark">{config.description}</Text>

      <View className="mb-4">
        {config.features.map((feature, index) => (
          <View key={index} className="mb-2 flex-row items-center">
            <View
              className="mr-2 flex h-6 w-6 items-center justify-center rounded-full"
              style={{ backgroundColor: config.checkIconBackground }}
            >
              <FontAwesome6 name="check" size={12} color={config.iconColor} />
            </View>
            <Text className="text-sm text-dark">{feature}</Text>
          </View>
        ))}
      </View>

      <Button
        size="default"
        variant={type === 'REHAB' ? 'success' : 'primary'}
        leftIcon={
          <FontAwesome6 name={config.icon} size={14} color={Colors.white} />
        }
        onPress={onPress}
      >
        Select {config.title}
      </Button>
    </View>
  )
}

export { ProjectTypeCard }
