import { useState } from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

interface RehabItemCardProps {
  category: string
  title: string
  area: string
  price: number
  description: string
  imageUrl: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  condition: string
  expectedCompletion: string
  onEdit: () => void
  onDelete: () => void
}

const RehabItemCard = ({
  category,
  title,
  area,
  price,
  description,
  imageUrl,
  priority,
  condition,
  expectedCompletion,
  onEdit,
  onDelete
}: RehabItemCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
      case 'urgent':
        return 'text-red-500'
      case 'medium':
        return 'text-yellow-500'
      case 'low':
        return 'text-green-500'
      default:
        return 'text-gray-500'
    }
  }

  return (
    <View className="mb-3 overflow-hidden rounded-lg bg-white shadow-sm">
      <Pressable
        onPress={() => setIsExpanded(!isExpanded)}
        className="flex-row items-center justify-between p-3"
      >
        <View className="flex-1">
          <View className="mb-1 self-start rounded-full bg-blue-100 px-2 py-0.5">
            <Text className="text-xs font-medium text-blue-700">
              {category}
            </Text>
          </View>
          <Text className="mb-1 text-base font-semibold text-gray-800">
            {title}
          </Text>
          <View className="self-start rounded-full bg-blue-100 px-2 py-0.5">
            <Text className="text-xs font-medium text-blue-700">{area}</Text>
          </View>
        </View>
        <View className="ml-4 items-end">
          <Text className="mb-2 text-lg font-semibold text-blue-500">
            ${price?.toLocaleString()}
          </Text>
          <FontAwesome6
            name="chevron-down"
            size={14}
            color={Colors.gray[400]}
            style={{ transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }}
          />
        </View>
      </Pressable>

      {isExpanded && (
        <View className="border-t border-gray-100 px-3 pb-3">
          <Text className="text-gray-500 mb-4 text-sm leading-5">
            {description}
          </Text>

          <View className="h-30 w-30 mb-4 overflow-hidden rounded-lg">
            <Image
              source={{ uri: imageUrl }}
              className="h-full w-full"
              resizeMode="cover"
            />
          </View>

          <View className="mb-4 space-y-3">
            <View className="flex-row">
              <View className="flex-1">
                <Text className="text-gray-500 mb-1 text-xs">Priority</Text>
                <Text
                  className={`text-sm font-medium ${getPriorityColor(priority)}`}
                >
                  {priority.charAt(0).toUpperCase() + priority.slice(1)}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="text-gray-500 mb-1 text-xs">Condition</Text>
                <Text className="text-sm font-medium text-gray-800">
                  {condition}
                </Text>
              </View>
            </View>

            <View className="flex-row">
              <View className="flex-1">
                <Text className="text-gray-500 mb-1 text-xs">Area</Text>
                <Text className="text-sm font-medium text-gray-800">
                  {area}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="text-gray-500 mb-1 text-xs">
                  Expected Completion
                </Text>
                <Text className="text-sm font-medium text-gray-800">
                  {expectedCompletion}
                </Text>
              </View>
            </View>
          </View>

          <View className="flex-row gap-2">
            <Pressable
              onPress={onEdit}
              className="flex-1 flex-row items-center justify-center rounded-lg border border-gray-300 p-2"
            >
              <FontAwesome6 name="pen" size={14} color={Colors.gray[600]} />
              <Text className="ml-2 text-sm text-gray-600">Edit</Text>
            </Pressable>
            <Pressable
              onPress={onDelete}
              className="flex-1 flex-row items-center justify-center rounded-lg border border-red-300 bg-red-50 p-2"
            >
              <FontAwesome6 name="trash" size={14} color={Colors.danger} />
              <Text className="ml-2 text-sm text-red-500">Delete</Text>
            </Pressable>
          </View>
        </View>
      )}
    </View>
  )
}

export default RehabItemCard
