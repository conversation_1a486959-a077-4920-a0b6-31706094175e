import { useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Button } from '@/components/Button'
import PhotoGallery from '@/components/tenant/PhotoGallery'
import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'
import { formatDate } from '@/utils/formatDate'

import type { AddedItem } from './types'

interface RehabItemCollapsibleProps {
  item: AddedItem
  onEdit: () => void
  onDelete: () => void
}

const priorityColors = {
  LOW: 'text-yellow-500',
  MEDIUM: 'text-orange-500',
  HIGH: 'text-red-500',
  URGENT: 'text-red-700'
}

export function RehabItemCollapsible({
  item,
  onEdit,
  onDelete
}: RehabItemCollapsibleProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const priorityColorClass =
    item.priority && item.priority in priorityColors
      ? priorityColors[item.priority as keyof typeof priorityColors]
      : 'text-gray'

  return (
    <View
      className="mb-2 overflow-hidden rounded-lg border border-border bg-white"
      style={ShadowStyles.sm}
    >
      <Pressable
        onPress={() => setIsExpanded(!isExpanded)}
        className="flex-row items-center justify-between p-3"
      >
        <View className="flex-1">
          <View className="mb-1 flex-row items-center">
            <Text className="mr-2 rounded-full bg-primary-light px-2 py-1 text-xs text-primary">
              {item.areaType}
            </Text>
            <Text className="rounded-full bg-primary-light px-2 py-1 text-xs text-primary">
              {item.itemName}
            </Text>
          </View>
          <Text className="mb-1 text-base font-semibold text-dark">
            {item.areaName}
          </Text>
        </View>
        <View className="ml-4 flex-row items-center">
          <Text className="text-lg font-bold text-primary">
            ${item.budget || 0}
          </Text>
          <FontAwesome6
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={16}
            color={Colors.dark}
            className="ml-3 w-6 text-center"
          />
        </View>
      </Pressable>

      {isExpanded && (
        <View className="px-3 pb-4">
          <Text className="mb-4 text-sm text-gray">{item.itemDesc}</Text>
          <PhotoGallery photos={(item.photos as unknown as string[]) ?? []} />
          <View className="mb-4 space-y-3">
            <View className="flex-row">
              <View className="flex-1">
                <Text className="mb-1 text-xs text-gray">Priority</Text>
                <Text
                  className={classNames(
                    'text-sm font-semibold capitalize',
                    priorityColorClass
                  )}
                >
                  {item.priority}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="mb-1 text-xs text-gray">Condition</Text>
                <Text className="text-sm font-semibold capitalize text-dark">
                  {item.condition}
                </Text>
              </View>
            </View>
            <View className="flex-row">
              <View className="flex-1">
                <Text className="mb-1 text-xs text-gray">Area</Text>
                <Text className="text-sm font-semibold text-dark">
                  {item.areaName}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="mb-1 text-xs text-gray">
                  Expected Completion
                </Text>
                <Text className="text-sm font-semibold text-dark">
                  {formatDate(item.expectedCompletionDate)}
                </Text>
              </View>
            </View>
          </View>

          <View className="flex-row space-x-3">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onPress={onEdit}
              leftIcon="pencil"
            >
              Edit
            </Button>
            <Button
              variant="danger"
              size="sm"
              className="flex-1"
              onPress={onDelete}
              leftIcon="trash"
            >
              Delete
            </Button>
          </View>
        </View>
      )}
    </View>
  )
}
