import { Modal, Text, View } from 'react-native'
import { useRouter } from 'expo-router'

import { Button } from '@/components'

interface AssignVendorModalProps {
  visible: boolean
  onClose: () => void
  projectId?: number
  onAssignLater: () => void
}

export function AssignVendorModal({
  visible,
  onClose,
  projectId,
  onAssignLater
}: AssignVendorModalProps) {
  const router = useRouter()

  const handleAssignVendor = () => {
    onClose()
    if (projectId) {
      router.push(`/property-manager/project/${projectId}?tab=items`)
    }
  }

  const handleAssignLater = () => {
    onClose()
    onAssignLater()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-center bg-black/50 px-6">
        <View className="w-full max-w-sm rounded-2xl bg-white p-6 shadow-lg">
          <Text className="mb-2 text-center text-xl font-semibold text-dark">
            Project Submitted Successfully!
          </Text>
          <Text className="mb-6 text-center text-gray-600">
            Would you like to assign vendors to this project now or do it later?
          </Text>

          <View className="gap-3">
            <Button
              variant="primary"
              onPress={handleAssignVendor}
              leftIcon="user-tie"
            >
              Assign Vendor
            </Button>

            <Button
              variant="primary"
              onPress={handleAssignLater}
              leftIcon="clock"
            >
              Assign Later
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  )
}
