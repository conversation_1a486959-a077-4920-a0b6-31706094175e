import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors, colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface VendorCardProps {
  name: string
  specialty: string
  rating: number
  reviewCount: number
  availability: string
  distance: string
  onSelect: () => void
  isSelected?: boolean
  onCall?: () => void
  onEmail?: () => void
}

export function VendorCard({
  name,
  specialty,
  rating,
  reviewCount,
  availability,
  distance,
  onSelect,
  isSelected,
  onCall,
  onEmail
}: VendorCardProps) {
  return (
    <Pressable
      onPress={onSelect}
      className={classNames(
        'mb-3 rounded-lg border border-transparent bg-white p-4',
        isSelected && 'border-success'
      )}
    >
      {isSelected && (
        <View className="absolute right-4 top-4 h-6 w-6 items-center justify-center rounded-full bg-[#36B37E]">
          <FontAwesome6 name="check" size={12} color="white" />
        </View>
      )}

      <View className="mb-3 flex-row items-center">
        <View className="mr-3 h-12 w-12 items-center justify-center rounded-full bg-[#F4F5F7]">
          <FontAwesome6 name="user" size={18} color={colors.text} />
        </View>
        <View>
          <Text className="mb-0.5 text-base font-semibold">{name}</Text>
          <Text className="text-sm text-[#6B778C]">{specialty}</Text>
        </View>
      </View>

      <View className="mb-3 flex-row flex-wrap gap-4">
        <View className="flex-row items-center">
          <FontAwesome6
            name="star"
            size={13}
            color={Colors.success}
            solid
            style={{ marginRight: 6 }}
          />
          <Text className="text-sm text-[#6B778C]">
            {rating} ({reviewCount} reviews)
          </Text>
        </View>
        <View className="flex-row items-center">
          <FontAwesome6
            name="clock"
            size={13}
            solid
            color={Colors.success}
            style={{ marginRight: 6 }}
          />
          <Text className="text-sm text-[#6B778C]">{availability}</Text>
        </View>
        <View className="flex-row items-center">
          <FontAwesome6
            name="location-dot"
            size={13}
            color={Colors.success}
            style={{ marginRight: 6 }}
          />
          <Text className="text-sm text-[#6B778C]">{distance}</Text>
        </View>
      </View>

      <View className="flex-row justify-end">
        <View className="flex-row gap-3">
          <Pressable
            onPress={onCall}
            className="h-9 w-9 items-center justify-center rounded-full bg-[#F4F5F7]"
          >
            <FontAwesome6 name="phone" size={14} color={colors.text} />
          </Pressable>
          <Pressable
            onPress={onEmail}
            className="h-9 w-9 items-center justify-center rounded-full bg-[#F4F5F7]"
          >
            <FontAwesome6 name="envelope" size={14} color={colors.text} />
          </Pressable>
        </View>
      </View>
    </Pressable>
  )
}
