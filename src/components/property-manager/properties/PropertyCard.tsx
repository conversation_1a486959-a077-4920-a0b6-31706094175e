import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'

type Property = components['schemas']['PropertyInfoDTO']

interface PropertyCardProps {
  property: Property
  onPress: () => void
}

export function PropertyCard({ property, onPress }: PropertyCardProps) {
  // const badgeVariant = cva('absolute right-2 top-2 rounded px-2 py-1', {
  //   variants: {
  //     variant: {
  //       active: 'bg-primary-light',
  //       pending: 'bg-warning-light',
  //       completed: 'bg-success-light'
  //     }
  //   }
  // })

  // const textVariant = cva('text-xs', {
  //   variants: {
  //     variant: {
  //       active: 'text-primary',
  //       pending: 'text-warning',
  //       completed: 'text-success'
  //     }
  //   }
  // })

  return (
    <Pressable
      onPress={onPress}
      className="mb-4 overflow-hidden rounded-lg bg-white"
      style={[crossPlatformShadow()]}
    >
      <View className="relative">
        <Image
          source={{ uri: property.photos?.[0]?.fileUrl }}
          className="h-[120px] w-full"
          resizeMode="cover"
        />
        {/* <View className={badgeVariant({ variant })}>
          <Text className={textVariant({ variant })}>{badgeText}</Text>
        </View> */}
      </View>
      <View className="p-3">
        <Text className="mb-1 text-base font-semibold" numberOfLines={1}>
          {property.propertyName}
        </Text>
        <Text className="mb-2 text-sm text-gray" numberOfLines={1}>
          {property.streetAddress}
        </Text>
        <View className="flex-row justify-between">
          <View className="flex-row items-center">
            <FontAwesome6
              name="diagram-project"
              size={14}
              color={Colors.primary}
            />
            <Text className="ml-1 text-sm text-dark">
              {property.projectCount} projects
            </Text>
          </View>
          {property.yearBuilt && (
            <View className="flex-row items-center">
              <FontAwesome6 name="calendar" size={14} color={Colors.primary} />
              <Text className="ml-1 text-sm text-dark">
                {property.yearBuilt}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  )
}
