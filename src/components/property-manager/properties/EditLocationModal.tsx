import { useEffect } from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Toast } from 'toastify-react-native'

import { Button, FormItem, Input, Select } from '@/components'
import type { FormInstance } from '@/components/Form'
import { Form } from '@/components/Form'
import { useStateCityZip } from '@/hooks/useStateCityZip'
import { client } from '@/services/api'
import { Colors, ShadowStyles } from '@/theme/colors'
import type { ID } from '@/types'

interface EditLocationModalProps {
  form: FormInstance<any>
  propertyId: ID
  visible: boolean
  onClose: () => void
  onUpdated: VoidFunction
}

const EditLocationModal = ({
  form,
  propertyId,
  visible,
  onClose,
  onUpdated
}: EditLocationModalProps) => {
  const {
    stateOptions,
    cityOptions,
    zipOptions,
    loading,
    setSelectedState,
    setSelectedCity,
    setSelectedZip
  } = useStateCityZip({
    onZipChange(zip) {
      form.setValue('zipCode', zip)
    }
  })
  const updateLocationInfoRequest = useRequest(
    async (values: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/updateLocationInfo',
        {
          body: {
            propertyId: propertyId,
            ...values
          }
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Location information updated successfully!')
          onClose()
          onUpdated()
        }
      }
    }
  )

  useEffect(() => {
    if (visible) {
      setSelectedState(form.getValues('state'))
      setSelectedCity(form.getValues('city'))
      setSelectedZip(form.getValues('zipCode'))
    }
  }, [visible])

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => onClose()}
    >
      <View className="bg-gray-50 flex-1">
        <View className="border-b border-gray-200 bg-white px-4 py-3">
          <View className="flex-row items-center justify-between">
            <Text className="text-lg font-semibold text-dark">
              Edit Location Information
            </Text>
            <Pressable
              onPress={() => onClose()}
              className="rounded-full bg-gray-100 p-2"
            >
              <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
            </Pressable>
          </View>
        </View>

        <ScrollView className="flex-1" contentContainerStyle={{ padding: 16 }}>
          <View
            className="rounded-default border border-border bg-white p-4"
            style={ShadowStyles.sm}
          >
            <Form
              form={form}
              onFinish={values => updateLocationInfoRequest.run(values)}
            >
              <FormItem
                name="streetAddress"
                label="Street Address"
                rules={{
                  required: {
                    value: true,
                    message: 'Street address is required'
                  }
                }}
              >
                <Input placeholder="Enter street address" />
              </FormItem>

              <View className="flex-row gap-3">
                <FormItem
                  className="flex-1"
                  name="state"
                  label="State"
                  rules={{
                    required: { value: true, message: 'State is required' }
                  }}
                  onValuesChange={(name, v) => setSelectedState(v as string)}
                >
                  <Select<string>
                    loading={loading}
                    options={stateOptions}
                    placeholder="Select state"
                  />
                </FormItem>
                <FormItem
                  className="flex-1"
                  name="city"
                  label="City"
                  rules={{
                    required: { value: true, message: 'City is required' }
                  }}
                  onValuesChange={(name, v) => setSelectedCity(v as string)}
                >
                  <Select<string>
                    loading={loading}
                    options={cityOptions}
                    placeholder="Select city"
                  />
                </FormItem>
              </View>

              <FormItem
                name="zipCode"
                label="ZIP Code"
                rules={{
                  required: { value: true, message: 'ZIP code is required' }
                }}
              >
                <Select<string>
                  loading={loading}
                  options={zipOptions}
                  placeholder="Select ZIP code"
                />
              </FormItem>
            </Form>
          </View>
        </ScrollView>

        <View className="border-t border-gray-200 bg-white p-4">
          <Button
            variant="primary"
            onPress={() =>
              form.handleSubmit(values =>
                updateLocationInfoRequest.run(values)
              )()
            }
            loading={updateLocationInfoRequest.loading}
            className="w-full"
          >
            <Text className="text-white">Update Location Info</Text>
          </Button>
        </View>
      </View>
    </Modal>
  )
}

export default EditLocationModal
