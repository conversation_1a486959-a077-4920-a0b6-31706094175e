import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'

type Draft = {
  draftId: string
  propertyName: string
  streetAddress: string
  city: string
  state: string
  zipCode: string
  status: 'INCOMPLETE' | 'PENDING_REVIEW' | 'NEEDS_PHOTOS'
  lastModified: string
  completionPercentage: number
  photos?: Array<{ fileUrl: string }>
}

interface DraftCardProps {
  draft: Draft
  onPress: () => void
}

const statusConfig = {
  INCOMPLETE: {
    label: 'Incomplete',
    color: Colors.warning,
    bgColor: 'rgba(255, 193, 7, 0.1)',
    icon: 'clock'
  },
  PENDING_REVIEW: {
    label: 'Pending Review',
    color: Colors.info,
    bgColor: 'rgba(13, 202, 240, 0.1)',
    icon: 'eye'
  },
  NEEDS_PHOTOS: {
    label: 'Needs Photos',
    color: Colors.gray,
    bgColor: 'rgba(108, 117, 125, 0.1)',
    icon: 'camera'
  }
} as const

export function DraftCard({ draft, onPress }: DraftCardProps) {
  const statusInfo = statusConfig[draft.status]

  // Format last modified date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    )

    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  // Get progress bar color based on completion percentage
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return Colors.success
    if (percentage >= 50) return Colors.info
    return Colors.warning
  }

  return (
    <Pressable
      onPress={onPress}
      className="mb-4 overflow-hidden rounded-lg bg-white"
      style={[crossPlatformShadow()]}
    >
      <View className="relative">
        {draft.photos && draft.photos.length > 0 ? (
          <Image
            source={{ uri: draft.photos[0]?.fileUrl }}
            className="h-[120px] w-full"
            resizeMode="cover"
          />
        ) : (
          <View className="h-[120px] w-full items-center justify-center bg-gray-100">
            <FontAwesome6 name="image" size={32} color={Colors.gray} />
            <Text className="mt-2 text-xs text-gray">No photo</Text>
          </View>
        )}

        {/* Status Badge */}
        <View
          className="absolute right-2 top-2 rounded px-2 py-1"
          style={{ backgroundColor: statusInfo.bgColor }}
        >
          <View className="flex-row items-center">
            <FontAwesome6
              name={statusInfo.icon as any}
              size={10}
              color={statusInfo.color}
            />
            <Text className="ml-1 text-xs" style={{ color: statusInfo.color }}>
              {statusInfo.label}
            </Text>
          </View>
        </View>
      </View>

      <View className="p-3">
        <Text className="mb-1 text-base font-semibold" numberOfLines={1}>
          {draft.propertyName}
        </Text>
        <Text className="mb-2 text-sm text-gray" numberOfLines={1}>
          {draft.streetAddress}
        </Text>
        <Text className="mb-3 text-xs text-gray" numberOfLines={1}>
          {draft.city}, {draft.state} {draft.zipCode}
        </Text>

        {/* Progress Bar */}
        <View className="mb-2">
          <View className="mb-1 flex-row items-center justify-between">
            <Text className="text-xs text-gray">Progress</Text>
            <Text className="text-xs font-medium text-dark">
              {draft.completionPercentage}%
            </Text>
          </View>
          <View className="h-2 w-full rounded-full bg-gray-200">
            <View
              className="h-2 rounded-full"
              style={{
                width: `${draft.completionPercentage}%`,
                backgroundColor: getProgressColor(draft.completionPercentage)
              }}
            />
          </View>
        </View>

        {/* Last Modified */}
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <FontAwesome6 name="clock" size={12} color={Colors.gray} />
            <Text className="ml-1 text-xs text-gray">
              {formatDate(draft.lastModified)}
            </Text>
          </View>

          <View className="flex-row items-center">
            <FontAwesome6 name="edit" size={12} color={Colors.primary} />
            <Text className="ml-1 text-xs text-primary">Edit</Text>
          </View>
        </View>
      </View>
    </Pressable>
  )
}
