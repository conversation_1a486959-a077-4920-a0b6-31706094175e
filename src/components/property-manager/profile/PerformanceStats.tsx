import React from 'react'
import { Text, View } from 'react-native'
import { Bars3BottomLeftIcon } from 'react-native-heroicons/outline'

import { ShadowStyles } from '@/theme/colors'

export function PerformanceStats({
  properties,
  vendors,
  activeRehabs
}: {
  properties?: number
  vendors?: number
  activeRehabs?: number
}) {
  return (
    <View
      className="mb-4 flex-col rounded-2xl border border-border bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="mb-3 flex-row items-center">
        <Bars3BottomLeftIcon size={20} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Performance Stats
        </Text>
      </View>
      <View className="flex-row items-center justify-between">
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-pm">{properties}</Text>
          <Text className="text-gray-400 mt-1 text-xs">Properties</Text>
        </View>
        <View className="mx-2 h-8 w-px bg-gray-200" />
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-success">{activeRehabs}</Text>
          <Text className="text-gray-400 mt-1 text-xs">Active Projects</Text>
        </View>
        <View className="mx-2 h-8 w-px bg-gray-200" />
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-warning">{vendors}</Text>
          <Text className="text-gray-400 mt-1 text-xs">Vendors</Text>
        </View>
      </View>
    </View>
  )
}
