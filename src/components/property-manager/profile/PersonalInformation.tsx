import React from 'react'
import { Text, View } from 'react-native'
import { UserIcon } from 'react-native-heroicons/solid'

import { ShadowStyles } from '@/theme/colors'

export function PersonalInformation({
  name,
  phone,
  location,
  company
}: {
  name?: string
  phone?: string
  location?: string
  company?: string
}) {
  return (
    <View
      className="mb-4 rounded-2xl border border-border bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="mb-3 flex-row items-center">
        <UserIcon size={20} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-dark">
          Personal Information
        </Text>
      </View>
      <View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-dark">Full Name</Text>
          <Text className="text-sm text-gray">{name}</Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-dark">Phone Number</Text>
          <Text className="text-sm text-gray">{phone}</Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-dark">Location</Text>
          <Text className="text-sm text-gray">{location}</Text>
        </View>
        <View className="flex-row items-center justify-between py-2">
          <Text className="text-sm font-semibold text-dark">Company</Text>
          <Text className="text-sm text-gray">{company}</Text>
        </View>
      </View>
    </View>
  )
}
