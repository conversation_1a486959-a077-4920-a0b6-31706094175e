import { View } from 'react-native'

import { DatePicker, Form, FormItem, Input, Select } from '@/components'
import ProjectTypeHeader from '@/components/property-manager/projects/create/ProjectTypeHeader'
import ActionButtons from '@/components/property-manager/projects/create/steps/ActionButtons'
import SectionTitle from '@/components/SectionTitle'
import { ShadowStyles } from '@/theme/colors'

import type { ProjectFormData } from './context'
import { useProject } from './context'

export function Step2() {
  const { formData, updateFormData, handleContinue, handleBack } = useProject()
  const form = Form.useForm<ProjectFormData>()

  return (
    <>
      <ProjectTypeHeader
        type={formData.projectType!}
        title={`${formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Project`}
      />
      <Form
        className="flex-1"
        form={form}
        style={ShadowStyles.sm}
        onFinish={values => {
          console.log(values)
          updateFormData({
            ...values
          })
          handleContinue()
        }}
        initialValues={formData}
      >
        <View className="rounded-default border border-gray-200 bg-white p-5">
          <SectionTitle title="Enter Project Information" className="mb-4" />
          <FormItem<ProjectFormData>
            name="projectName"
            label="Project Name"
            rules={{
              required: { value: true, message: 'Project name is required' }
            }}
          >
            <Input placeholder="Enter project name (Optional)" />
          </FormItem>

          <FormItem<ProjectFormData>
            name="description"
            label="Description"
            rules={{
              required: {
                value: true,
                message: 'Please enter project description'
              }
            }}
          >
            <Input
              placeholder="Describe the project scope and requirements (optional)"
              multiline
              numberOfLines={4}
            />
          </FormItem>

          <FormItem<ProjectFormData>
            name="vendorPropertyAddress"
            label="Property Address"
            rules={{
              required: {
                value: true,
                message: 'Property address is required'
              }
            }}
          >
            <Input placeholder="Enter property address" />
          </FormItem>
          <View className="flex flex-row gap-3">
            <FormItem<ProjectFormData>
              className="flex-1"
              name="vendorPropertyType"
              label="Property Type"
              // rules={{
              //   required: { value: true, message: 'Property type is required' }
              // }}
            >
              <Select placeholder="Property type" dictType="PROPERTY_TYPE" />
            </FormItem>
            <FormItem<ProjectFormData>
              className="flex-1"
              name="priority"
              label="Priority"
              rules={{
                required: { value: true, message: 'Priority is required' }
              }}
            >
              <Select placeholder="Select priority" dictType="PRIORITY" />
            </FormItem>
          </View>

          <View className="flex flex-row gap-3">
            <FormItem<ProjectFormData>
              className="flex-1"
              name="estimateStartDate"
              label="Start Date"
              dependencies={['estimateCompleteDate']}
              // rules={[{ required: true, message: 'Please select start date' }]}
            >
              {/* @ts-ignore */}
              {({ estimateCompleteDate }: { estimateCompleteDate: Date }) => {
                return (
                  <DatePicker
                    placeholder="Select start date"
                    maxDate={estimateCompleteDate}
                  />
                )
              }}
            </FormItem>
            <FormItem<ProjectFormData>
              className="flex-1"
              name="estimateCompleteDate"
              label="Estimated Completion"
              dependencies={['estimateStartDate']}
              // rules={[{ required: true, message: 'Please select completion date' }]}
            >
              {/* @ts-ignore */}
              {({ estimateStartDate }: { estimateStartDate: Date }) => {
                return (
                  <DatePicker
                    placeholder="Select completion date"
                    minDate={estimateStartDate}
                  />
                )
              }}
            </FormItem>
          </View>

          {/* <FormItem
            name="budget"
            label="Estimated Budget"
            // rules={[{ required: true, message: 'Please enter project budget' }]}
          >
            <NumberInput
              LeftAccessory={() => <Text className="text-gray">$</Text>}
              placeholder="Enter estimated budget"
            />
          </FormItem> */}

          <FormItem<ProjectFormData>
            name="additionalNotes"
            label="Additional Notes"
          >
            <Input
              placeholder="Any additional notes or special requirements"
              multiline
              numberOfLines={4}
            />
          </FormItem>
        </View>
      </Form>
      <ActionButtons onPrev={handleBack} onNext={form.submit} />
    </>
  )
}
