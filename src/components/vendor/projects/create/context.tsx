import { createContext, useContext, useState } from 'react'
import { useRequest } from 'ahooks'
import type { Result } from 'ahooks/lib/useRequest/src/types'
import { useRouter } from 'expo-router'
import type { ReactNode } from 'react'
import { Toast } from 'toastify-react-native'

import type { AddedItem } from '@/components/property-manager/projects/create/types'
import { useFormStorage } from '@/hooks/useFormStorage'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useSelectVendor } from '@/store/selectVendor'
import type { ProjectWorkType } from '@/types'

export type ProjectFormData = Omit<
  components['schemas']['ProjectInfo'],
  'propertyId' | 'projectType' | 'estimateStartDate' | 'estimateCompleteDate'
> & {
  projectType: ProjectWorkType
  estimateStartDate: Date | null
  estimateCompleteDate: Date | null
}

const initialFormData: ProjectFormData = {
  projectId: undefined,
  projectType: 'REHAB',
  priority: 'MEDIUM',
  affectedUnits: '',
  description: '',
  projectName: '',
  emptyRehabVendorId: undefined,
  estimateStartDate: null,
  estimateCompleteDate: null,
  estimateBudget: undefined,
  additionalNotes: '',
  items: []
}

interface ProjectContextType {
  editingItem: AddedItem | null
  formData: ProjectFormData
  setEditingItem: (item: AddedItem | null) => void
  updateFormData: (data: Partial<ProjectFormData>) => void
  resetFormData: () => void
  clearFormData: () => void
  isLoaded: boolean
  currentStep: number
  setCurrentStep: (step: number) => void
  handleBack: () => void
  handleContinue: () => void
  submitRequest: Result<boolean, []>
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined)

export function ProjectProvider({ children }: { children: ReactNode }) {
  const { clearSelected } = useSelectVendor()
  const [currentStep, setCurrentStep] = useState(0)
  const router = useRouter()
  const [editingItem, setEditingItem] = useState<AddedItem | null>(null)

  const {
    data: formData,
    updateData,
    resetData,
    clearData,
    isLoaded
  } = useFormStorage<ProjectFormData>({
    key: 'vendor-project-form',
    initialValue: initialFormData,
    reviver(data) {
      if (data.estimateStartDate) {
        data.estimateStartDate = new Date(data.estimateStartDate)
      }
      if (data.estimateCompleteDate) {
        data.estimateCompleteDate = new Date(data.estimateCompleteDate)
      }
      return data
    }
  })

  const submitRequest = useRequest(
    async () => {
      const { estimateStartDate, estimateCompleteDate, ...rest } = {
        ...formData
      }
      const body: Omit<components['schemas']['ProjectInfo'], 'propertyId'> =
        rest
      // remove temp item id
      if (body.items?.length) {
        for (const item of body.items) {
          if (item.itemId && item.itemId < 0) {
            item.itemId = undefined
          }
        }
      }
      // date format
      body.estimateStartDate = estimateStartDate?.toISOString()
      body.estimateCompleteDate = estimateCompleteDate?.toISOString()
      body.status = 'SUBMITTED'
      const { error } = await client.POST('/api/v1/vendor/project/add', {
        // @ts-ignore
        body
      })
      if (!error) {
        clearData()
        Toast.success('Project saved')
      }
      return !error
    },
    {
      manual: true,
      onSuccess(v) {
        if (v) {
          clearSelected()
          router.back()
        }
      }
    }
  )

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    } else {
      router.back()
    }
  }

  const handleContinue = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const value = {
    formData,
    editingItem,
    setEditingItem,
    updateFormData: updateData,
    resetFormData: resetData,
    clearFormData: clearData,
    isLoaded,
    currentStep,
    setCurrentStep,
    handleBack,
    handleContinue,
    submitRequest
  }

  return (
    <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>
  )
}

export function useProject() {
  const context = useContext(ProjectContext)
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider')
  }
  return context
}
