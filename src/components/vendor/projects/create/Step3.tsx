import { useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import { useRouter } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { ProjectIcon } from '@/components'
import { AddedItems } from '@/components/property-manager/projects/create/area/AddedItems'
import { AddEditItemModal } from '@/components/property-manager/projects/create/area/AddEditItemModal'
import { CommonItems } from '@/components/property-manager/projects/create/area/CommonItems'
import { PropertyAreas } from '@/components/property-manager/projects/create/area/PropertyAreas'
import ActionButtons from '@/components/property-manager/projects/create/steps/ActionButtons'
import type {
  AddedItem,
  SelectedArea
} from '@/components/property-manager/projects/create/types'
import { useSelectVendor } from '@/store/selectVendor'
import type { DictItemType } from '@/types'

import { useProject } from './context'

export function Step3() {
  // eslint-disable-next-line unused-imports/no-unused-vars
  const router = useRouter()
  const {
    formData,
    editingItem: persistedEditingItem,
    updateFormData,
    handleBack,
    handleContinue
  } = useProject()
  const { selectedVendors } = useSelectVendor()
  const [selectedArea, setSelectedArea] = useState<SelectedArea | null>(null)
  const [addedItems, setAddedItems] = useState<AddedItem[]>(
    formData.items || []
  )

  const [isModalVisible, setModalVisible] = useState(false)
  const [editingItem, setEditingItem] = useState<Partial<AddedItem> | null>(
    null
  )

  const handleCommonItemAdd = (item: DictItemType) => {
    setEditingItem({ itemName: item.label })
    setModalVisible(true)
  }

  const handleCreateNewItem = () => {
    setEditingItem(null) // This will open a blank form
    setModalVisible(true)
  }

  const handleAddedItemEdit = (item: AddedItem) => {
    setEditingItem(item)
    setModalVisible(true)
  }

  const handleItemRemove = (item: AddedItem) => {
    setAddedItems(prev => prev.filter(i => item !== i))
  }

  const handleModalSubmit = (values: AddedItem) => {
    if (editingItem && editingItem.itemId) {
      const index = addedItems.indexOf(editingItem as AddedItem)
      // Edit mode
      setAddedItems(prev =>
        prev.map((i, idx) => (index === idx ? { ...i, ...values } : i))
      )
    } else {
      // Add mode
      setAddedItems(prev => [
        ...prev,
        {
          itemId: -1 * Math.random(),
          ...values,
          ...selectedArea
        } as AddedItem
      ])
    }
    setModalVisible(false)
    setEditingItem(null)
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingItem(null)
  }

  const onSubmit = () => {
    // no items needed for assigned project
    if (!selectedVendors?.length) {
      if (!addedItems.length) {
        Toast.error('No items added')
        return
      }
    }
    updateFormData({
      items: addedItems,
      emptyRehabVendorId:
        selectedVendors && selectedVendors.length > 0
          ? selectedVendors[0]!.vendorId
          : undefined
    })
    handleContinue()
  }

  useEffect(() => {
    if (persistedEditingItem) {
      handleAddedItemEdit(persistedEditingItem)
    }
  }, [persistedEditingItem])

  return (
    <View className="flex-1">
      {/* Project Type Header */}
      <View className="mb-4 flex-row items-center border-b border-[#DFE1E6] pb-3">
        <ProjectIcon
          iconSize={14}
          type={formData.projectType!}
          className="mr-3 h-7 w-7 items-center justify-center rounded-full"
        />
        <Text className="text-base font-semibold text-dark">
          {formData.projectType === 'REHAB' ? 'Rehab' : 'Work Order'} Project
        </Text>
      </View>

      {/* Property Areas */}
      <PropertyAreas
        selectedArea={selectedArea}
        setSelectedArea={setSelectedArea}
        onAreasFetched={v => {
          if (!selectedArea && v.length) {
            setSelectedArea({
              areaType: v[0]!.code!,
              areaName: v[0]!.label!
            })
          }
        }}
      />

      {/* Common Items */}
      {selectedArea && (
        <CommonItems
          selectedArea={selectedArea}
          onItemAdd={handleCommonItemAdd}
          onNewItem={handleCreateNewItem}
        />
      )}

      {/* Added Items */}
      <AddedItems
        items={addedItems}
        onItemRemove={handleItemRemove}
        onItemEdit={handleAddedItemEdit}
      />

      {/* Add/Edit Modal */}
      <AddEditItemModal
        visible={isModalVisible}
        initialValues={editingItem || undefined}
        onSubmit={handleModalSubmit}
        onCancel={handleModalCancel}
        isEdit={!!editingItem && !!editingItem.itemId}
      />
      <ActionButtons onPrev={handleBack} onNext={onSubmit} />
    </View>
  )
}
