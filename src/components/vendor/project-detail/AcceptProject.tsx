import { useState } from 'react'
import { Text, View } from 'react-native'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { <PERSON><PERSON>, CardWrapper } from '@/components'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

import Alert from './Alert'
import ProjectItem from './ProjectItem'
import ProjectItemList from './ProjectItemList'
import ProjectStatusHeader from './ProjectStatusHeader'

interface IAcceptProjectProps {
  projectItemList: components['schemas']['ItemInfoDTO'][]
  projectId?: number
  vendorId?: number
}

const AcceptProject = (props: IAcceptProjectProps) => {
  const { projectItemList, projectId, vendorId } = props
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Submit allocation function
  const handleConfirmWorkStarted = async () => {
    if (!projectId || !vendorId) {
      Toast.error('Missing project or vendor information')
      return
    }

    if (!projectItemList || projectItemList.length === 0) {
      Toast.error('No items to process')
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare allocation data for all items
      const allocationData = projectItemList.map(item => ({
        projectId,
        itemId: item.itemId!,
        vendorId,
        allocationStatus: 'STARTED',
        actualStartDate: new Date().toISOString(),
        actualCost: 0
      }))

      // Call API
      const response = await client.POST('/api/v1/vendor/allocation/submit', {
        body: allocationData
      })

      if (response.data?.code === 200) {
        Toast.success('Work started successfully!')
        setTimeout(() => {
          router.back()
        }, 1500)
      } else {
        Toast.error(response.data?.message || 'Failed to confirm work started')
      }
    } catch (error) {
      console.error('Confirm work started error:', error)
      Toast.error('Failed to confirm work started. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <CardWrapper>
      <View className="rounded-xl p-4">
        <ProjectStatusHeader status="IN_PROGRESS" />
        <View className="mb-4">
          <Alert
            message="Ready to Start Work"
            description="Please confirm that you're ready to begin work on this project."
            type="info"
          />
        </View>

        <View className="mb-4">
          <CardWrapper>
            <View className="p-4">
              <Text className="mb-3 text-base font-semibold">
                Scheduled Information
              </Text>
              <View className="mb-3 flex-row items-center justify-between">
                <Text className="text-sm text-gray-700">
                  Scheduled Start Date:
                </Text>
                <Text className="text-sm">June 27, 2025</Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-sm text-gray-700">Deadline</Text>
                <Text className="text-sm">August 27, 2025</Text>
              </View>
            </View>
          </CardWrapper>
        </View>

        <ProjectItemList
          list={projectItemList}
          listItemComponent={(expanded, onSetExpand, item) => {
            return (
              <ProjectItem
                item={item}
                expanded={expanded}
                onSetExpand={onSetExpand}
                projectHeader={<AcceptedProjectItemHeader />}
              />
            )
          }}
        />
        <View className="border-t border-gray-200 pt-5">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">
              Total Approved Amount:
            </Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="flex-row items-center justify-end gap-2">
            <Button onPress={() => router.back()} disabled={isSubmitting}>
              Back
            </Button>
            <Button
              variant="primary"
              onPress={handleConfirmWorkStarted}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Confirm Work Started'}
            </Button>
          </View>
        </View>
      </View>
    </CardWrapper>
  )
}

const AcceptedProjectItemHeader = () => {
  return (
    <View className="px-4 pb-3 text-sm font-semibold text-primary">
      Approved: $2,692
    </View>
  )
}

export default AcceptProject
