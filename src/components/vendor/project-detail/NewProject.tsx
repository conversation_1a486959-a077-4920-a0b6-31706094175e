import { useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { <PERSON><PERSON>, CardWrapper } from '@/components'
import { NumberInput } from '@/components/NumberInput'
import type { AddedItem } from '@/components/property-manager/projects/create/types'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'

import { AddEditItemModal } from './AddEditItemModal'
import Alert from './Alert'
import ProjectItem from './ProjectItem'
import ProjectItemList from './ProjectItemList'
import ProjectStatusHeader from './ProjectStatusHeader'

interface INewProjectProps {
  projectItemList: components['schemas']['ItemInfoDTO'][] | undefined
  projectId?: number
  vendorId?: number
  projectData?: components['schemas']['ProjectInfoDTO']
}

const NewProject = (props: INewProjectProps) => {
  const { projectItemList, projectId, vendorId, projectData } = props
  const [addNewItem, setAddNewItem] = useState(false)
  const [quoteValues, setQuoteValues] = useState<Record<number, number>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAddingItem, setIsAddingItem] = useState(false)

  // Calculate total quote amount - only recalculate when explicitly triggered
  const [totalQuote, setTotalQuote] = useState(0)

  // Function to recalculate total quote
  const calculateTotalQuote = () => {
    const total = Object.values(quoteValues).reduce(
      (sum, value) => sum + (value || 0),
      0
    )
    setTotalQuote(total)
  }

  // Handle quote value change (only update state, don't calculate total yet)
  const handleQuoteChange = (itemId: number, value: number) => {
    setQuoteValues(prev => ({
      ...prev,
      [itemId]: value
    }))
  }

  // Handle input blur - trigger total calculation
  const handleQuoteBlur = () => {
    calculateTotalQuote()
  }

  // Handle add item modal submit
  const handleAddItemSubmit = async (values: AddedItem) => {
    if (!projectId) {
      Toast.error('Project ID is required')
      return
    }

    setIsAddingItem(true)

    try {
      // Convert AddedItem to ItemInfoDTO format for API
      const itemData: components['schemas']['ItemInfoDTO'] = {
        projectId,
        propertyId: projectData?.propertyId || 0,
        ownerId: projectData?.ownerId || 0,
        managerId: projectData?.managerId,
        leaseId: projectData?.leaseId,
        areaType: values.areaType || 'GENERAL',
        areaName: values.areaName || 'General Area',
        itemName: values.itemName || '',
        itemDesc: values.itemDesc,
        condition: values.condition,
        priority: values.priority,
        budget: values.budget || 0,
        photos:
          values.photos?.map((photo: any) => ({
            fileKey: photo.fileKey || photo,
            fileName: photo.fileName || 'image.jpg',
            fileUrl: photo.fileUrl || photo
          })) || []
      }

      // Call API to add item
      const response = await client.POST('/api/v1/vendor/item/add', {
        body: [itemData]
      })

      if (response.data?.code === 200) {
        Toast.success('Item added successfully!')
        setAddNewItem(false)
        // TODO: Refresh project items list
        // You might want to refetch the project data here
      } else {
        Toast.error(response.data?.message || 'Failed to add item')
      }
    } catch (error) {
      console.error('Add item error:', error)
      Toast.error('Failed to add item. Please try again.')
    } finally {
      setIsAddingItem(false)
    }
  }

  // Handle add item modal cancel
  const handleAddItemCancel = () => {
    setAddNewItem(false)
  }

  // Submit quote function based on PROMPTS.md
  const handleSubmitQuote = async () => {
    if (!projectId || !vendorId) {
      Toast.error('Missing project or vendor information')
      return
    }

    // Validate all items have quotes
    const missingQuotes =
      projectItemList?.filter(item => {
        const itemId = item.itemId
        return itemId && (!quoteValues[itemId] || quoteValues[itemId] <= 0)
      }) || []

    if (missingQuotes.length > 0) {
      Toast.error('Please provide quotes for all items')
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare quote data according to VendorItemQuotesSubmitDTO schema
      const quoteData: components['schemas']['VendorItemQuotesSubmitDTO'][] =
        projectItemList?.map(item => ({
          projectId,
          itemId: item.itemId!,
          vendorId,
          submittedQuote: quoteValues[item.itemId!],
          status: 'SUBMITTED'
        })) || []

      // Submit quotes to API
      const response = await client.POST('/api/v1/vendor/quote/submit', {
        body: quoteData
      })

      if (response.data?.code === 200) {
        Toast.success('Quote submitted successfully!')
        // Navigate back to projects list after delay (similar to PROMPTS.md)
        setTimeout(() => {
          router.back()
        }, 1500)
      } else {
        Toast.error(response.data?.message || 'Failed to submit quote')
      }
    } catch (error) {
      console.error('Submit quote error:', error)
      Toast.error('Failed to submit quote. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <CardWrapper>
      <View className="rounded-xl p-4">
        <ProjectStatusHeader status="PENDING_QUOTES" />
        <View className="mb-4">
          <Alert
            message="Quote Requested"
            description="Please review the project items and provide your quote for each item."
            type="info"
          />
        </View>
        <ProjectItemList
          list={projectItemList}
          listItemComponent={(expanded, onSetExpand, item) => {
            return (
              <ProjectItem
                item={item}
                expanded={expanded}
                onSetExpand={onSetExpand}
                projectHeader={
                  <NewProjectItemHeader
                    itemId={item.itemId!}
                    value={quoteValues[item.itemId!] || 0}
                    onChange={handleQuoteChange}
                    onBlur={handleQuoteBlur}
                  />
                }
              />
            )
          }}
        />
        {!addNewItem && (
          <Button
            className="my-4"
            leftIcon={<FontAwesome6 name="plus" color={Colors.white} />}
            variant="primary"
            onPress={() => {
              setAddNewItem(true)
            }}
          >
            Add Item
          </Button>
        )}
        {addNewItem && (
          <AddEditItemModal
            visible={addNewItem}
            showBudget={true}
            onSubmit={handleAddItemSubmit}
            onCancel={handleAddItemCancel}
            isEdit={false}
            loading={isAddingItem}
          />
        )}
        <View className="border-t border-gray-200 pt-5">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">Total Quote Amount</Text>
            <Text className="text-sm font-semibold">
              ${totalQuote.toLocaleString()}
            </Text>
          </View>
          <View className="flex-row items-center justify-end gap-2">
            <Button onPress={() => router.back()} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onPress={handleSubmitQuote}
              disabled={isSubmitting || totalQuote === 0}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Quote'}
            </Button>
          </View>
        </View>
      </View>
    </CardWrapper>
  )
}

interface NewProjectItemHeaderProps {
  itemId: number
  value: number
  onChange: (itemId: number, value: number) => void
  onBlur: () => void
}

const NewProjectItemHeader = ({
  itemId,
  value,
  onChange,
  onBlur
}: NewProjectItemHeaderProps) => {
  const handleInputChange = (numValue: number | null) => {
    const finalValue = numValue || 0
    onChange(itemId, finalValue)
  }

  const handleBlur = () => {
    onBlur()
  }

  return (
    <>
      <View className="flex-row flex-wrap items-center border-b border-gray-200 p-4">
        <Text className="text-xs text-gray-700">Your Quote:</Text>
        <View className="mx-2 flex-1">
          <NumberInput
            placeholder="Enter price"
            value={value > 0 ? value.toString() : ''}
            onChange={handleInputChange}
            onBlur={handleBlur}
            min={0}
            decimalPlaces={2}
            allowNegative={false}
            LeftAccessory={() => <Text className="text-gray">$</Text>}
            className="rounded px-2 py-1"
          />
        </View>
      </View>
    </>
  )
}

export default NewProject
