import { useState } from 'react'
import { Text, View } from 'react-native'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { <PERSON><PERSON>, CardWrapper, DatePicker } from '@/components'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

import <PERSON><PERSON> from './Alert'
import ProjectItem from './ProjectItem'
import ProjectItemList from './ProjectItemList'
import ProjectStatusHeader from './ProjectStatusHeader'

interface IApprovedProjectProps {
  projectItemList: components['schemas']['ItemInfoDTO'][]
  projectId?: number
  vendorId?: number
}

const ApprovedProject = (props: IApprovedProjectProps) => {
  const { projectItemList, projectId, vendorId } = props
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [startDate, setStartDate] = useState<Date | null>(null)

  // Submit allocation function
  const handleAllocationSubmit = async (allocationStatus: string) => {
    if (!projectId || !vendorId) {
      Toast.error('Missing project or vendor information')
      return
    }

    if (!projectItemList || projectItemList.length === 0) {
      Toast.error('No items to process')
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare allocation data for all items
      const allocationData = projectItemList.map(item => ({
        projectId,
        itemId: item.itemId!,
        vendorId,
        allocationStatus,
        actualStartDate: startDate?.toISOString() || new Date().toISOString(),
        actualCost: 0
      }))

      // Call API
      const response = await client.POST('/api/v1/vendor/allocation/submit', {
        body: allocationData
      })

      if (response.data?.code === 200) {
        Toast.success(`Work ${allocationStatus.toLowerCase()} successfully!`)
        setTimeout(() => {
          router.back()
        }, 1500)
      } else {
        Toast.error(response.data?.message || 'Failed to submit')
      }
    } catch (error) {
      console.error('Allocation submit error:', error)
      Toast.error('Failed to submit. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <CardWrapper>
      <View className="rounded-xl p-4">
        <ProjectStatusHeader status="SUBMITTED" />
        <View className="mb-4">
          <Alert
            message="Your quote has been approved!"
            description="Please review the project details and accept the work to proceed."
            type="warning"
          />
        </View>
        <ProjectItemList
          list={projectItemList}
          listItemComponent={(expanded, onSetExpand, item) => {
            return (
              <ProjectItem
                item={item}
                expanded={expanded}
                onSetExpand={onSetExpand}
                projectHeader={<ApprovedProjectItemHeader />}
              />
            )
          }}
        />
        <View className="border-t border-gray-200 pt-5">
          <View className="mb-4">
            <Text className="mb-4 text-sm">When can you start the work?</Text>
            <DatePicker
              value={startDate || undefined}
              onChange={setStartDate}
              placeholder="Select start date"
            />
          </View>
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">
              Total Approved Amount:
            </Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="flex-row items-center justify-end gap-2">
            <Button
              onPress={() => handleAllocationSubmit('REJECTED')}
              disabled={isSubmitting}
            >
              Decline
            </Button>
            <Button
              variant="primary"
              onPress={() => handleAllocationSubmit('ACCEPTED')}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Accept Work'}
            </Button>
          </View>
        </View>
      </View>
    </CardWrapper>
  )
}

const ApprovedProjectItemHeader = () => {
  return (
    <>
      <View className="px-4 pb-3 text-sm font-semibold text-gray-700">
        Your Quote: $2,692
      </View>
      <View className="px-4 pb-3 text-sm font-semibold text-warning">
        Approved: $2,834
      </View>
    </>
  )
}

export default ApprovedProject
