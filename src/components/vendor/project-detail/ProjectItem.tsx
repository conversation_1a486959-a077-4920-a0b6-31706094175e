import { Image, Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import type { components } from '@/services/api/schema'

interface IProjectItemProps {
  item: components['schemas']['ItemInfoDTO']
  expanded: boolean
  onSetExpand: (id: number) => void
  projectHeader: React.ReactNode
  projectContent?: React.ReactNode
}

const ProjectItem = ({
  item,
  expanded,
  onSetExpand,
  projectHeader,
  projectContent
}: IProjectItemProps) => {
  return (
    <View className="mb-3 rounded-lg bg-white pt-3 shadow">
      {/* Main title section */}
      <View>
        <Pressable
          onPress={() => onSetExpand(item.itemId!)}
          className="mb-2 flex-row items-center justify-between px-4"
        >
          <View className="flex-row items-center gap-2">
            <FontAwesome6
              name={expanded ? 'chevron-down' : 'chevron-right'}
              size={14}
              color="black"
            />
            <Text className="text-sm font-semibold">{item.itemName}</Text>
          </View>
          <Text className="text-sm text-gray-600">Countertops</Text>
        </Pressable>
        {projectHeader}
      </View>

      {/* Content section */}
      {expanded && (
        <View className="bg-gray-100 p-4">
          <Text className="mb-3 text-sm text-gray-700">{item.itemDesc}</Text>
          {/* Quantity & Category */}
          <View className="mt-1 flex-row flex-wrap justify-between">
            <View className="flex-1">
              <Text className="text-xs text-gray-600">Quantity</Text>
              <Text className="font-medium">45 sqft</Text>
            </View>
            <View className="flex-1">
              <Text className="text-xs text-gray-600">Category</Text>
              <Text className="font-medium">Countertops</Text>
            </View>
          </View>
          {projectContent}
          <View className="mt-4 flex-row gap-2">
            <Image
              source={{ uri: 'https://picsum.photos/seed/prop1-1/300/200' }}
              className="h-[80px] w-[80px]"
              resizeMode="cover"
            />
            <Image
              source={{ uri: 'https://picsum.photos/seed/prop1-2/300/200' }}
              className="h-[80px] w-[80px]"
              resizeMode="cover"
            />
            <Image
              source={{ uri: 'https://picsum.photos/seed/prop1-3/300/200' }}
              className="h-[80px] w-[80px]"
              resizeMode="cover"
            />
          </View>
        </View>
      )}
    </View>
  )
}

export default ProjectItem
