import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

const Alert = ({
  message,
  description,
  type = 'info'
}: {
  message: string
  description: string
  type: 'success' | 'info' | 'warning' | 'error'
}) => {
  const icon = {
    success: 'check-circle',
    info: 'circle-info',
    warning: 'circle-info',
    error: 'exclamation-circle'
  }[type]

  const iconStyle = {
    success: 'text-green-500',
    info: 'text-primary',
    warning: 'text-yellow-500',
    error: 'text-red-500'
  }[type]

  const bgColor = {
    success: 'bg-green-50',
    info: 'bg-[rgba(63,81,181,0.1)]',
    warning: 'bg-yellow-50',
    error: 'bg-red-50'
  }[type]

  return (
    <View
      className={`flex-row items-center justify-between gap-3 p-4 ${bgColor} rounded-md`}
    >
      <FontAwesome6 name={icon} className={iconStyle} size={24} />
      <View className="flex-1">
        <Text className="text-base font-bold text-gray-700">{message}</Text>
        <Text className="text-sm text-gray-700">{description}</Text>
      </View>
    </View>
  )
}

export default Alert
