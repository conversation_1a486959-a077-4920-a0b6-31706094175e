import { useState } from 'react'
import { Text, View } from 'react-native'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { AddPhotos, Button, CardWrapper, Input } from '@/components'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

import ProjectItem from './ProjectItem'
import ProjectItemList from './ProjectItemList'
import ProjectStatusHeader from './ProjectStatusHeader'

interface IStartedProjectProps {
  projectItemList: components['schemas']['ItemInfoDTO'][]
  projectId?: number
  vendorId?: number
}

const StartedProject = (props: IStartedProjectProps) => {
  const { projectItemList, projectId, vendorId } = props
  const [photos, setPhotos] = useState<components['schemas']['FileInfo'][]>([])
  const [actualCosts, setActualCosts] = useState<Record<number, number>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Handle actual cost change for each item
  const handleActualCostChange = (itemId: number, cost: number) => {
    setActualCosts(prev => ({
      ...prev,
      [itemId]: cost
    }))
  }

  // Submit completion
  const handleMarkAsCompleted = async () => {
    if (!projectId || !vendorId) {
      Toast.error('Missing project or vendor information')
      return
    }

    if (!projectItemList || projectItemList.length === 0) {
      Toast.error('No items to process')
      return
    }

    // Validate all items have actual costs
    const missingCosts = projectItemList.filter(item => {
      const itemId = item.itemId
      return itemId && (!actualCosts[itemId] || actualCosts[itemId] <= 0)
    })

    if (missingCosts.length > 0) {
      Toast.error('Please provide actual costs for all items')
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare allocation data for all items
      const allocationData = projectItemList.map(item => ({
        projectId,
        itemId: item.itemId!,
        vendorId,
        allocationStatus: 'COMPLETED',
        actualStartDate: new Date().toISOString(),
        actualCost: actualCosts[item.itemId!] || 0
      }))

      // Call API
      const response = await client.POST('/api/v1/vendor/allocation/submit', {
        body: allocationData
      })

      if (response.data?.code === 200) {
        Toast.success('Work completed successfully!')
        setTimeout(() => {
          router.back()
        }, 1500)
      } else {
        Toast.error(response.data?.message || 'Failed to mark as completed')
      }
    } catch (error) {
      console.error('Mark as completed error:', error)
      Toast.error('Failed to mark as completed. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <CardWrapper>
      <View className="rounded-xl p-4">
        <ProjectStatusHeader status="DRAFT" />
        <ProjectItemList
          list={projectItemList}
          listItemComponent={(expanded, onSetExpand, item) => {
            return (
              <ProjectItem
                item={item}
                expanded={expanded}
                onSetExpand={onSetExpand}
                projectHeader={
                  <StartedProjectItemHeader
                    itemId={item.itemId!}
                    approvedAmount={item.budget || 0}
                    actualCost={actualCosts[item.itemId!] || 0}
                    onActualCostChange={handleActualCostChange}
                  />
                }
              />
            )
          }}
        />
        <View className="border-t border-gray-200 pt-5">
          <View>
            <Text>Project Completion Photos</Text>
            <AddPhotos
              title=""
              description="Add photos to help us better understand the issue"
              onChange={setPhotos}
              value={photos}
            />
          </View>
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">
              Total Approved Amount:
            </Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="flex-row items-center justify-end gap-2">
            <Button
              onPress={() => {
                router.back()
              }}
            >
              Back
            </Button>
            <Button
              variant="primary"
              onPress={handleMarkAsCompleted}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Mark as Completed'}
            </Button>
          </View>
        </View>
      </View>
    </CardWrapper>
  )
}

interface StartedProjectItemHeaderProps {
  itemId: number
  approvedAmount?: number
  actualCost: number
  onActualCostChange: (itemId: number, cost: number) => void
}

const StartedProjectItemHeader = ({
  itemId,
  approvedAmount,
  actualCost,
  onActualCostChange
}: StartedProjectItemHeaderProps) => {
  const handleInputChange = (text: string) => {
    const numValue = parseFloat(text) || 0
    onActualCostChange(itemId, numValue)
  }

  return (
    <>
      <View className="px-4 pb-3 text-sm font-semibold text-warning">
        Approved: ${approvedAmount?.toLocaleString() || '0'}
      </View>
      <View className="flex-row flex-wrap items-center border-b border-gray-200 p-4">
        <Text className="text-xs text-gray-700">Actual Cost:</Text>
        <View className="mx-2 flex-1">
          <Input
            placeholder="Enter price"
            keyboardType="numeric"
            value={actualCost > 0 ? actualCost.toString() : ''}
            onChangeText={handleInputChange}
            LeftAccessory={() => <Text className="text-gray">$</Text>}
            className="rounded px-2 py-1"
          />
        </View>
      </View>
    </>
  )
}

export default StartedProject
