import { Fragment, useState } from 'react'
import { Text, View } from 'react-native'
import { router } from 'expo-router'

import { <PERSON><PERSON>, CardWrapper, Rating } from '@/components'

import Alert from './Alert'
import ProjectItem from './ProjectItem'
import ProjectItemList from './ProjectItemList'
import ProjectStatusHeader from './ProjectStatusHeader'

interface ICompleteProjectProps {
  projectItemList: any[]
}

const CompleteProject = (props: ICompleteProjectProps) => {
  const { projectItemList } = props
  // eslint-disable-next-line unused-imports/no-unused-vars
  const [photos, setPhotos] = useState<string[]>([])

  return (
    <CardWrapper>
      <View className="rounded-xl p-4">
        <ProjectStatusHeader status="COMPLETED" />
        <View className="mb-4">
          <Alert
            message="Project Successfully Completed"
            description="Great job! The client has reviewed and accepted your work."
            type="success"
          />
        </View>
        <View className="mb-4">
          <CardWrapper>
            <View className="p-4">
              <Text className="mb-3 text-base font-semibold">
                Client Feedback
              </Text>
              <View>
                <Text>Rating:</Text>
                <Rating rating={4.5} />
              </View>
              <Text>Comment:</Text>
              <Text className="rounded-default bg-gray-100 p-4 text-xs">
                "Great job on the project! The work was completed on time and to
                our specifications. Very professional service!"
              </Text>
            </View>
          </CardWrapper>
        </View>
        <ProjectItemList
          list={projectItemList}
          listItemComponent={(expanded, onSetExpand, item) => {
            return (
              <ProjectItem
                item={item}
                expanded={expanded}
                onSetExpand={onSetExpand}
                projectHeader={<CompleteProjectItemHeader />}
                projectContent={<CompleteProjectItemContent />}
              />
            )
          }}
        />
        <View className="border-t border-gray-200 pt-5">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">
              Total Approved Amount:
            </Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">Total Actual Cost:</Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-sm text-gray-700">Final Payment:</Text>
            <Text className="text-sm">$1000</Text>
          </View>
          <View className="flex-row items-center justify-end gap-2">
            <Button
              onPress={() => {
                router.back()
              }}
            >
              Back
            </Button>
          </View>
        </View>
      </View>
    </CardWrapper>
  )
}

const CompleteProjectItemHeader = () => {
  return (
    <Fragment>
      <View className="px-4 pb-3 text-sm font-semibold text-success">
        Final: $2,692
      </View>
    </Fragment>
  )
}

const CompleteProjectItemContent = () => {
  return (
    <Fragment>
      <Text className="my-2 text-sm font-semibold">Completion Details:</Text>
      <View className="mb-2 rounded-default bg-white p-2 text-sm font-semibold text-success">
        <View className="mb-2 flex-row items-center justify-between">
          <Text>Quote Amount:</Text>
          <Text>$1000</Text>
        </View>
        <View className="flex-row items-center justify-between">
          <Text>Actual Cost:</Text>
          <Text>$1000</Text>
        </View>
      </View>
    </Fragment>
  )
}

export default CompleteProject
