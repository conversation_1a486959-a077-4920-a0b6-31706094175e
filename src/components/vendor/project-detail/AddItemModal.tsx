import { Modal, Pressable, <PERSON><PERSON>View, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'

import {
  AddPhotos,
  Button,
  DatePicker,
  Form,
  FormItem,
  Input,
  Select
} from '@/components'
import { client } from '@/services/api'

const AddItemModal = ({
  visible,
  onClose
}: {
  visible: boolean
  onClose: () => void
}) => {
  const form = Form.useForm<{}>()
  // FIXME: projectId

  // eslint-disable-next-line unused-imports/no-unused-vars
  const { run: addItem } = useRequest(() => {
    return client.POST('/api/v1/vendor/item/add', {
      // FIXME
      body: [
        {
          areaType: 'string',
          areaName: 'string',
          itemName: 'string',
          itemDesc: 'string',
          condition: 'string',
          priority: 'MEDIUM',
          projectId: 0,
          propertyId: 0,
          ownerId: 0,
          specialInstruction: 'string',
          expectedCompletionDate: 'string',
          budget: 0,
          option: 'string',
          photos: [
            {
              createdBy: 0,
              createdTime: 'string',
              updatedBy: 0,
              updatedTime: 'string',
              isDeleted: true,
              fileId: 0,
              entityType: 'string',
              entityId: 0,
              fileKey: 'string',
              fileName: 'string',
              category: 'string'
            }
          ]
        }
      ]
    })
  })

  const handleClose = () => {
    onClose()
  }

  const onSubmit = (values: {}) => {
    console.log(values)
    // addItem()
  }
  return (
    <Modal
      animationType="fade" // can also be 'fade' or 'none'
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <View className="flex h-full w-full items-center justify-center bg-black/50">
        <View className="max-h-[80%] min-w-[80%] bg-white p-4">
          <ScrollView showsVerticalScrollIndicator>
            <View className="mb-4 flex-row items-center justify-between border-b border-b-border pb-2">
              <Text className="text-lg font-bold">Add New Item</Text>
              <Pressable onPress={handleClose}>
                <FontAwesome6 name="xmark" size={14} color="black" />
              </Pressable>
            </View>

            <Form form={form}>
              <FormItem name="itemName" label="Item Name">
                <Input />
              </FormItem>
              <FormItem name="itemDesc" label="Description">
                <Input multiline />
              </FormItem>
              <FormItem name="areaType" label="Property Area">
                <Select
                  options={[
                    { label: 'Countertops', value: 'countertops' },
                    { label: 'Flooring', value: 'flooring' },
                    { label: 'Painting', value: 'painting' },
                    { label: 'Plumbing', value: 'plumbing' },
                    { label: 'Electrical', value: 'electrical' },
                    { label: 'HVAC', value: 'hvac' },
                    { label: 'Other', value: 'other' }
                  ]}
                />
              </FormItem>
              <FormItem name="areaName" label="Common Rehab Items">
                <Select
                  options={[
                    { label: 'Countertops', value: 'countertops' },
                    { label: 'Flooring', value: 'flooring' },
                    { label: 'Painting', value: 'painting' },
                    { label: 'Plumbing', value: 'plumbing' },
                    { label: 'Electrical', value: 'electrical' },
                    { label: 'HVAC', value: 'hvac' },
                    { label: 'Other', value: 'other' }
                  ]}
                />
              </FormItem>
              <FormItem name="condition" label="Condition">
                <Select
                  options={[
                    { label: 'Poor', value: 'poor' },
                    { label: 'Fair', value: 'fair' },
                    { label: 'Good', value: 'good' },
                    { label: 'Excellent', value: 'excellent' }
                  ]}
                />
              </FormItem>
              <FormItem name="priority" label="Priority">
                <Select
                  options={[
                    { label: 'Low', value: 'low' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'High', value: 'high' }
                  ]}
                />
              </FormItem>
              <FormItem name="photos" label="Photos">
                <AddPhotos title="" description="" />
              </FormItem>
              <FormItem
                name="expectedCompletionDate"
                label="Expected Completion"
              >
                <DatePicker />
              </FormItem>
              <FormItem name="budget" label="Quote Budget">
                <Input />
              </FormItem>
              <View className="mt-4 flex-row justify-end gap-2">
                <Button onPress={handleClose}>Cancel</Button>
                <Button variant="primary" onPress={form.handleSubmit(onSubmit)}>
                  Save
                </Button>
              </View>
            </Form>
          </ScrollView>
        </View>
      </View>
    </Modal>
  )
}

export default AddItemModal
