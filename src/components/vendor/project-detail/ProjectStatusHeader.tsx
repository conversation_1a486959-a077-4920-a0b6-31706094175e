import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

type ProjectStatus =
  | 'PENDING_QUOTES'
  | 'SUBMITTED'
  | 'IN_PROGRESS'
  | 'DRAFT'
  | 'COMPLETED'
  | 'CANCELLED'

interface ProjectStatusHeaderProps {
  status: ProjectStatus
}

// 状态配置映射
const statusConfig: Record<
  ProjectStatus,
  {
    title: string
    statusText: string
    statusIcon: string
    statusColor: string
  }
> = {
  PENDING_QUOTES: {
    title: 'New Project Request',
    statusText: 'Quote Requested',
    statusIcon: 'file-alt',
    statusColor: Colors.primary
  },
  SUBMITTED: {
    title: 'Approved Quote',
    statusText: 'Quote Approved',
    statusIcon: 'check-circle',
    statusColor: Colors.warning
  },
  IN_PROGRESS: {
    title: 'Project Ready to Start',
    statusText: 'Scheduled',
    statusIcon: 'calendar-check',
    statusColor: Colors.info
  },
  DRAFT: {
    title: 'Work In Progress',
    statusText: 'In Progress',
    statusIcon: 'hammer',
    statusColor: Colors.warning
  },
  COMPLETED: {
    title: 'Completed Project',
    statusText: 'Completed',
    statusIcon: 'check-circle',
    statusColor: Colors.success
  },
  CANCELLED: {
    title: 'Completed Project',
    statusText: 'Completed',
    statusIcon: 'check-circle',
    statusColor: Colors.success
  }
}

const ProjectStatusHeader = ({ status }: ProjectStatusHeaderProps) => {
  const config = statusConfig[status]

  return (
    <View
      className="mb-4 flex-row items-center justify-between border-b pb-2"
      style={{
        borderBottomColor: Colors['gray-300']
      }}
    >
      <Text className="m-0 text-lg font-semibold text-gray-900">
        {config.title}
      </Text>
      <View className="flex-row items-center gap-1">
        <FontAwesome6
          name={config.statusIcon as any}
          size={14}
          color={config.statusColor}
        />
        <Text className="text-sm" style={{ color: config.statusColor }}>
          {config.statusText}
        </Text>
      </View>
    </View>
  )
}

export default ProjectStatusHeader
