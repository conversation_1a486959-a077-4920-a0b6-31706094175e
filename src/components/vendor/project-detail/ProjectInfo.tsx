import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { CardWrapper } from '@/components/CardWrapper'
import type { Project } from '@/types'
import { getFullAddr } from '@/utils/addr'

const ProjectInfo = ({ project }: { project: Project | undefined }) => {
  return (
    <View className="mb-4">
      <CardWrapper>
        <View className="rounded-xl p-5 shadow-inherit">
          <Text className="mb-2 text-xl font-semibold">
            {project?.projectName}
          </Text>
          <View className="mb-4 flex-row items-center gap-[6px]">
            <FontAwesome6 name="location-dot" />
            <Text className="text-sm text-gray-700">
              {getFullAddr(project, { showZip: false })}
            </Text>
          </View>
          <View className="mb-4 inline-block">
            <Text className="rounded-full bg-[rgba(21,101,192,0.1)] px-3 py-1.5 text-center text-xs font-medium leading-none text-primary">
              {project?.status}
            </Text>
          </View>
          <View className="mt-3 flex-row flex-wrap gap-4">
            <View className="flex flex-1">
              <Text className="mb-1 text-xs text-gray-600">
                Project Manager
              </Text>
              <Text className="text-gray-900">
                {project?.propertyManager?.name}
              </Text>
            </View>
            <View className="flex flex-1">
              <Text className="mb-1 text-xs text-gray-600">Deadline</Text>
              <Text className="text-gray-900">{project?.endDate}</Text>
            </View>
          </View>
          <View className="mt-3 flex-row flex-wrap gap-4">
            <View className="flex flex-1">
              <Text className="mb-1 text-xs text-gray-600">Items</Text>
              <Text className="text-gray-900">{project?.itemCount}</Text>
            </View>
          </View>
        </View>
      </CardWrapper>
    </View>
  )
}

export default ProjectInfo
