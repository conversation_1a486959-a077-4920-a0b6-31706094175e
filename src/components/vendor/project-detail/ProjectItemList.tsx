import { useEffect, useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import type { components } from '@/services/api/schema'

interface IProjectItemListProps {
  list: components['schemas']['ItemInfoDTO'][] | undefined
  listItemComponent: (
    expanded: boolean,
    onSetExpand: (id: number | string) => void,
    item: components['schemas']['ItemInfoDTO']
  ) => React.ReactNode
}

const ProjectItemList = ({
  list,
  listItemComponent
}: IProjectItemListProps) => {
  const [expandedMap, setExpandedMap] = useState<{
    [x: string]: boolean
    [x: number]: boolean
  }>({})
  const [expandAll, setExpandAll] = useState(false)

  useEffect(() => {
    if (!list) return

    setExpandedMap(prev => {
      const result = { ...prev }
      list.forEach(item => {
        // 只为新的 item 设置默认值，不覆盖已存在的状态
        if (!(item.itemId! in result)) {
          result[item.itemId!] = false
        }
      })
      return result
    })
  }, [list])

  const handleSetExpandAll = (bool: boolean) => {
    const result = assignAllExpanded(expandedMap, bool)
    setExpandAll(bool)
    setExpandedMap(result)
  }

  const onSetExpand = (id: number | string) => {
    setExpandedMap(prev => {
      return {
        ...prev,
        [id]: !prev[id]
      }
    })
  }

  return (
    <View>
      <View className="mb-3 flex-row items-center justify-between">
        <Text className="text-sm">Project Item List({list?.length || 0})</Text>
        <Pressable
          className="flex-row items-center gap-1"
          onPress={() => {
            handleSetExpandAll(!expandAll)
          }}
        >
          <FontAwesome6
            name={expandAll ? 'minus-square' : 'plus-square'}
            size={14}
            className="text-primary"
          />
          <Text className="text-xs text-primary">
            {expandAll ? 'Collapse All' : 'Expand All'}
          </Text>
        </Pressable>
      </View>
      <View>
        {list?.map(item => {
          return (
            <View key={item.itemId}>
              {listItemComponent(
                !!expandedMap[item.itemId!],
                onSetExpand,
                item
              )}
            </View>
          )
        })}
      </View>
    </View>
  )
}

const assignAllExpanded = (
  expandedMap: {
    [x: string | number]: boolean
  },
  bool: boolean
) => {
  const result: {
    [x: string | number]: boolean
  } = {}
  Object.keys(expandedMap).forEach(key => {
    result[key] = bool
  })
  return result
}

export default ProjectItemList
