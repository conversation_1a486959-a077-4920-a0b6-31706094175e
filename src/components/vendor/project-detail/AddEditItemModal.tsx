import React from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'

import { Button, DatePicker, Form, FormItem, Input, Select } from '@/components'
import { AddPhotos } from '@/components/AddPhotos'
import type { AddedItem } from '@/components/property-manager/projects/create/types'

interface AddEditItemModalProps {
  visible: boolean
  showBudget?: boolean
  initialValues?: Partial<AddedItem>
  onSubmit: (values: AddedItem) => void
  onCancel: () => void
  isEdit?: boolean
  loading?: boolean
}

export function AddEditItemModal({
  visible,
  showBudget = true,
  initialValues,
  onSubmit,
  onCancel: _onCancel,
  isEdit = false,
  loading = false
}: AddEditItemModalProps) {
  const form = Form.useForm<AddedItem>()
  const onCancel = () => {
    form.reset()
    _onCancel()
  }
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <ScrollView>
        <View className="flex-1 items-center justify-center bg-black/30 px-2 py-2">
          <View className="w-full max-w-md rounded-2xl bg-white p-6 shadow-xl">
            {/* Header */}
            <View className="mb-4 flex-row items-center justify-between">
              <Text className="text-lg font-bold text-dark">
                {isEdit ? 'Edit Item' : 'Add Item'}
              </Text>
              <Pressable onPress={onCancel} className="p-1">
                <Text className="text-gray-400 text-2xl">×</Text>
              </Pressable>
            </View>
            {/* Form */}
            <Form<AddedItem>
              form={form}
              onFinish={v => {
                onSubmit(v)
                form.reset()
              }}
              initialValues={initialValues || {}}
            >
              <FormItem<AddedItem>
                label="Item Name"
                name="itemName"
                rules={{
                  required: { value: true, message: 'Item name is required' }
                }}
              >
                <Input placeholder="Enter item name" />
              </FormItem>
              <FormItem<AddedItem>
                label="Description"
                name="itemDesc"
                rules={{
                  required: { value: true, message: 'Description is required' }
                }}
              >
                <Input
                  placeholder="Enter description"
                  multiline
                  numberOfLines={3}
                />
              </FormItem>
              <FormItem<AddedItem>
                label="Property Area"
                name="areaType"
                rules={{
                  required: {
                    value: true,
                    message: 'Property Area is required'
                  }
                }}
              >
                <Select dictType="ITEM_AREA_KITCHEN" placeholder="Select" />
              </FormItem>
              <FormItem<AddedItem>
                label="Common Rehab Items"
                name="areaName"
                rules={{
                  required: {
                    value: true,
                    message: 'Common Rehab Items is required'
                  }
                }}
              >
                <Select dictType="ITEM_AREA_MECHANICAL" placeholder="Select" />
              </FormItem>
              <FormItem<AddedItem>
                label="Condition"
                name="condition"
                rules={{
                  required: {
                    value: true,
                    message: 'Condition is required'
                  }
                }}
              >
                <Select
                  dictType="PROJECT_ITEM_CONDITION"
                  placeholder="Select"
                />
              </FormItem>
              <FormItem<AddedItem>
                label="Priority"
                name="priority"
                rules={{
                  required: { value: true, message: 'Priority is required' }
                }}
              >
                <Select dictType="PRIORITY" placeholder="Select" />
              </FormItem>
              <FormItem<AddedItem> label="Photos" name="photos">
                <AddPhotos title="" description="" />
              </FormItem>
              <FormItem<AddedItem>
                label="Special Instructions (Optional)"
                name="specialInstruction"
              >
                <Input
                  placeholder="Any special instructions or requirements"
                  multiline
                  numberOfLines={3}
                />
              </FormItem>
              <FormItem<AddedItem>
                label="Expected Completion"
                name="expectedCompletionDate"
              >
                <DatePicker placeholder="YYYY/MM/DD" />
              </FormItem>
              {showBudget && (
                <FormItem<AddedItem> label="Quote Budget" name="budget">
                  <Input placeholder="Enter budget amount" />
                </FormItem>
              )}
            </Form>
            {/* Footer */}
            <View className="mt-6 flex-row gap-2">
              <Button
                variant="outline"
                className="flex-1"
                onPress={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                className="flex-1"
                onPress={() => {
                  form.submit()
                }}
                disabled={loading}
                loading={loading}
              >
                {loading ? 'Saving...' : isEdit ? 'Save Changes' : 'Save'}
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </Modal>
  )
}
