// components/AnalyticsCard.tsx
import { Text, View } from 'react-native'

import { Rating } from '@/components'
import SectionTitle from '@/components/SectionTitle'
import classNames from '@/utils/classname'

const data = [4, 7, 5.5, 8, 7, 10]

const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']

export default function AnalyticsCard() {
  return (
    <>
      <SectionTitle title="Performance" />
      <View className="mb-4 space-y-4 rounded-2xl bg-white p-4 shadow-md">
        {/* Metrics Row */}
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-gray-500">Average Rating</Text>
            <View className="flex-row items-center space-x-1">
              <Text className="text-lg font-bold text-yellow-500">4.9</Text>
              <Rating rating={4.9} size={14} />
            </View>
          </View>

          <View>
            <Text className="text-gray-500">On-Time Rate</Text>
            <Text className="text-lg font-bold text-green-600">96%</Text>
          </View>

          <View>
            <Text className="text-gray-500">Quote Success</Text>
            <Text className="text-lg font-bold text-indigo-600">78%</Text>
          </View>
        </View>

        {/* Revenue Title */}
        <Text className="font-semibold text-gray-900">Monthly Revenue</Text>

        {/* Chart */}
        <View className="h-40 flex-row items-end space-x-2">
          {data.map((value, index) => (
            <View key={index} className="flex-1 items-center">
              <View
                style={{ height: value * 8 }}
                className={classNames(
                  'w-4 rounded-t-md',
                  index === 4 ? 'bg-amber-400' : 'bg-indigo-600'
                )}
              />
              <Text className="text-gray-500 mt-1 text-xs">
                {monthLabels[index]}
              </Text>
            </View>
          ))}
        </View>

        {/* Footer Label */}
        <Text className="text-gray-400 mt-2 text-right text-xs">
          Last 6 months
        </Text>
      </View>
    </>
  )
}
