import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { addDays } from 'date-fns'

import { ScheduleTodoCard } from '@/components/ScheduleTodoCard'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'

export const UpcomingList = () => {
  const { data } = useRequest(() => {
    return client.POST('/api/v1/vendor/upcoming', {
      body: {
        pageNum: 1,
        pageSize: 2
      }
    })
  })

  console.log('data upcoming', data)

  const scheduleList = [
    {
      deadline: addDays(new Date(), 1).valueOf(),
      progress: '30%',
      title: 'Bathroom Remodel',
      projectId: '1234567890'
    },
    {
      deadline: addDays(new Date(), 12).valueOf(),
      progress: '75%',
      title: 'Deck Restoration',
      projectId: '12345678'
    }
  ]
  return (
    <>
      <SectionTitle
        title="Upcoming Deadlines"
        href="/vendor/schedule"
        linkText="View Schedule"
      />
      <View className="mb-4">
        {scheduleList.map(schedule => {
          return (
            <View className="pb-4">
              <ScheduleTodoCard
                projectId={schedule.projectId}
                deadline={schedule.deadline}
                title={schedule.title}
                progress={schedule.progress}
              />
            </View>
          )
        })}
      </View>
    </>
  )
}
