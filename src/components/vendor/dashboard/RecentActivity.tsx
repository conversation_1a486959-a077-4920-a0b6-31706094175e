import { View } from 'react-native'
import { useRequest } from 'ahooks'

import { ActivityCard } from '@/components/ActivtiyCard'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'

export const RecentActivity = () => {
  const { data } = useRequest(async () => {
    const res = await client.POST('/api/v1/vendor/recent', {
      body: {
        pageNum: 0,
        pageSize: 3
      }
    })
    if (res.data?.code === 200) {
      return res.data.data
    }
    return []
  })
  return (
    <>
      <SectionTitle title="Recent Activity" />
      {data?.map(item => {
        return (
          <View className="mb-4" key={item.activityId}>
            <ActivityCard
              type={item.activityType as 'Approved' | 'Message' | 'Rating'}
            />
          </View>
        )
      })}
      {['Approved', 'Message', 'Rating'].map(type => (
        <View className="mb-4" key={type}>
          <ActivityCard type={type as 'Approved' | 'Message' | 'Rating'} />
        </View>
      ))}
    </>
  )
}
