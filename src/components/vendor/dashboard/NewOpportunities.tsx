import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { addDays } from 'date-fns'

import SectionTitle from '@/components/SectionTitle'
import { WorkOrderCard } from '@/components/WorkOrderCard'
import { client } from '@/services/api'

export const NewOpportunities = () => {
  const { data } = useRequest(async () => {
    const res = await client.GET('/api/v1/vendor/opportunities')
    if (res.data?.code === 200) {
      return res.data.data
    }
    return []
  })

  console.log('data opportunities', data)

  const workOrderList = [
    {
      orderId: 1234567890,
      orderType: 'hammer',
      title: 'Bathroom Remodel',
      deadline: addDays(new Date(), 1).toISOString(),
      pm: '<PERSON>'
    },
    {
      orderId: 1234567890,
      orderType: 'hammer',
      title: 'Bathroom Remodel',
      deadline: addDays(new Date(), 1).toISOString(),
      pm: '<PERSON>'
    },
    {
      orderId: 1234567890,
      orderType: 'hammer',
      title: 'Bathroom Remodel',
      deadline: addDays(new Date(), 1).toISOString(),
      pm: '<PERSON>'
    }
  ]
  return (
    <>
      <SectionTitle
        title="New Opportunities"
        href="/vendor/projects"
        linkText="View All"
      />
      <View>
        {data?.map(workOrder => (
          <View className="mb-4" key={workOrder.projectId}>
            <WorkOrderCard
              orderId={workOrder.projectId!}
              orderType={workOrder.projectType!}
              title={workOrder.projectName!}
              deadline={workOrder.endDate!}
              pm={workOrder.pmName!}
            />
          </View>
        ))}
        {workOrderList.map(workOrder => (
          <View className="mb-4" key={workOrder.orderId}>
            <WorkOrderCard
              orderId={workOrder.orderId}
              orderType={workOrder.orderType}
              title={workOrder.title}
              deadline={workOrder.deadline}
              pm={workOrder.pm}
            />
          </View>
        ))}
      </View>
    </>
  )
}
