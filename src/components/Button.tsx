import { type ComponentProps } from 'react'
import type { PressableProps, StyleProp, ViewStyle } from 'react-native'
import { Pressable, Text } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { cva, type VariantProps } from 'class-variance-authority'
import type { ReactNode } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

const buttonVariants = cva(
  'flex-row items-center justify-center min-w-10 rounded-default cursor-pointer transition-all',
  {
    variants: {
      variant: {
        'default': 'bg-light text-dark border border-border',
        'primary': 'bg-primary text-white',
        'primary-light': 'bg-primary-light text-dark',
        'warning': 'bg-warning text-white',
        'outline': 'border border-border bg-white text-dark',
        'tenant': 'bg-tenant text-white',
        'tenant-light': 'bg-tenant-light border border-tenant',
        'approval': 'bg-success text-white',
        'success': 'bg-success text-white',
        'danger': 'bg-danger text-white',
        'cancel': 'bg-light-gray text-gray border border-border',
        'danger-outline': 'bg-transparent text-danger border border-danger'
      },
      size: {
        xs: 'px-3 py-2 text-xs',
        sm: 'px-[14px] py-[10px] text-sm',
        default: 'px-4 py-3 text-base'
        // large: 'h-10 px-6 py-4'
      },
      block: {
        true: 'flex',
        false: 'inline-flex self-start'
      }
    },
    defaultVariants: {
      variant: 'outline',
      size: 'default'
    }
  }
)

const textVariants = cva('', {
  variants: {
    variant: {
      'default': 'text-dark',
      'primary': 'text-white',
      'primary-light': 'text-primary',
      'warning': 'text-white',
      'outline': 'text-dark',
      'tenant': 'text-white',
      'tenant-light': 'text-tenant',
      'approval': 'text-white',
      'success': 'text-white',
      'danger': 'text-white',
      'cancel': 'text-gray',
      'danger-outline': 'text-danger'
    }
  }
})

const iconColors: Record<
  NonNullable<VariantProps<typeof buttonVariants>['variant']>,
  string
> = {
  'default': Colors.dark,
  'primary': Colors.white,
  'primary-light': Colors.dark,
  'warning': Colors.white,
  'outline': Colors.dark,
  'tenant': Colors.white,
  'tenant-light': Colors.tenant,
  'approval': Colors.white,
  'success': Colors.white,
  'danger': Colors.white,
  'cancel': Colors.gray,
  'danger-outline': Colors.danger
}

export type ButtonProps = {
  loading?: boolean
  disabled?: boolean
  leftIcon?: ReactNode | ComponentProps<typeof FA>['name']
  leftIconProps?: ComponentProps<typeof FA>
  rightIcon?: ReactNode | ComponentProps<typeof FA>['name']
  block?: boolean
  children?: ReactNode
  onPress?: () => void
  variant?: VariantProps<typeof buttonVariants>['variant']
  size?: VariantProps<typeof buttonVariants>['size']
  className?: string
  style?: StyleProp<ViewStyle>
}

const Button = ({
  leftIcon,
  rightIcon,
  children,
  loading,
  disabled,
  variant = 'default',
  size,
  className,
  block = true,
  leftIconProps,
  ...props
}: ButtonProps & PressableProps) => {
  // const textColor = useMemo(() => {
  //   return ['default', 'outline'].includes(variant || '')
  //     ? Colors.dark
  //     : 'white'
  // }, [variant])

  return (
    <Pressable
      className={classNames(
        'gap-2',
        buttonVariants({ variant, size, className, block })
      )}
      disabled={disabled || loading}
      {...props}
    >
      {(leftIcon || loading) &&
        ((!leftIcon && loading) || typeof leftIcon === 'string' ? (
          <FA
            name={
              loading
                ? 'spinner'
                : (leftIcon as ComponentProps<typeof FA>['name'])
            }
            className={classNames(
              textVariants({ variant }),
              loading ? 'animate-spin' : ''
            )}
            color={iconColors[variant!]}
            {...leftIconProps}
          />
        ) : (
          leftIcon
        ))}
      <Text className={textVariants({ variant })}>{children}</Text>
      {rightIcon}
    </Pressable>
  )
}

export { Button }
