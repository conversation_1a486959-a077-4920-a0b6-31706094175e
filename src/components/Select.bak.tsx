import { Platform, View } from 'react-native'
import { Picker } from '@react-native-picker/picker'

import { Colors } from '@/theme/colors'

export type SelectProps = {
  placeholder?: string
  value?: string | number
  options: { label: string; value: string | number }[]
  onChange?: (value?: string | number) => void
}

function Select({
  placeholder = 'Please select',
  value,
  options,
  onChange
}: SelectProps) {
  const itemStyle = {
    padding: 0,
    fontSize: 14,
    height: Platform.OS === 'android' ? 44 : undefined
  }
  return (
    <View className="min-h-[44px] w-full overflow-hidden rounded-default border border-solid border-border bg-white px-3">
      <Picker
        testID="select-picker"
        className="m-0 h-[44px] min-h-0 w-full text-sm"
        selectedValue={value}
        onValueChange={onChange}
        itemStyle={itemStyle}
        style={{
          height: 44,
          margin: 0,
          padding: 0
        }}
      >
        <Picker.Item
          label={placeholder}
          value=""
          color={Colors.gray}
          style={itemStyle}
        />
        {options.map(option => (
          <Picker.Item
            key={option.value}
            label={option.label}
            value={option.value}
            style={itemStyle}
          />
        ))}
      </Picker>
    </View>
  )
}

export { Select }
