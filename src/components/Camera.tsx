import { useState } from 'react'
import { Alert, Text, TouchableOpacity, View } from 'react-native'
import { Image } from 'expo-image'
import * as ImagePicker from 'expo-image-picker'

import { Button } from '@/components/Button'
import { Icon } from '@/components/Icon'
import classNames from '@/utils/classname'

export interface CameraProps {
  onPhotoTaken?: (uri: string) => void
  onClose?: () => void
  className?: string
}

export const Camera = ({ onPhotoTaken, onClose, className }: CameraProps) => {
  const [capturedImage, setCapturedImage] = useState<string | null>(null)
  const [isCapturing, setIsCapturing] = useState(false)

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync()
    if (status !== 'granted') {
      Alert.alert(
        'Permission needed',
        'Camera permission is required to take photos'
      )
      return false
    }
    return true
  }

  const takePicture = async () => {
    const hasPermission = await requestPermissions()
    if (!hasPermission) return

    try {
      setIsCapturing(true)
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8
      })

      if (!result.canceled && result.assets[0]) {
        setCapturedImage(result.assets[0].uri)
      }
    } catch {
      Alert.alert('Error', 'Failed to take picture')
    } finally {
      setIsCapturing(false)
    }
  }

  const retakePicture = () => {
    setCapturedImage(null)
  }

  const confirmPicture = () => {
    if (capturedImage && onPhotoTaken) {
      onPhotoTaken(capturedImage)
    }
  }

  if (capturedImage) {
    return (
      <View className={classNames('flex-1 bg-black', className)}>
        <Image
          source={{ uri: capturedImage }}
          className="flex-1"
          contentFit="cover"
        />
        <View className="absolute bottom-0 left-0 right-0 bg-black/50 p-4">
          <View className="flex-row justify-center space-x-4">
            <Button
              onPress={retakePicture}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              <Icon icon="view" className="mr-2" />
              Retake
            </Button>
            <Button
              onPress={confirmPicture}
              variant="primary"
              size="sm"
              className="flex-1"
            >
              <Icon icon="check" className="mr-2" />
              Use Photo
            </Button>
          </View>
        </View>
      </View>
    )
  }

  return (
    <View className={classNames('flex-1 bg-gray-100', className)}>
      {/* Header */}
      <View className="flex-row items-center justify-between border-b border-gray-200 bg-white p-4">
        <Button onPress={onClose} variant="outline" size="sm">
          <Icon icon="x" />
        </Button>
        <Text className="text-lg font-semibold">Take Photo</Text>
        <View className="w-10" />
      </View>

      {/* Camera Preview Area */}
      <View className="flex-1 items-center justify-center p-8">
        <View className="mb-8 aspect-square w-full items-center justify-center rounded-lg bg-gray-200">
          <Icon icon="view" size={64} className="text-gray-400" />
          <Text className="text-gray-500 mt-4 text-center">
            Tap the button below to take a photo
          </Text>
        </View>

        {/* Camera Button */}
        <TouchableOpacity
          onPress={takePicture}
          disabled={isCapturing}
          className={classNames(
            'h-20 w-20 items-center justify-center rounded-full bg-primary',
            isCapturing && 'opacity-50'
          )}
        >
          {isCapturing ? (
            <Icon icon="view" size={32} className="text-white" />
          ) : (
            <Icon icon="view" size={32} className="text-white" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  )
}
