import React, { useState } from 'react'
import {
  ActivityIndicator,
  FlatList,
  Modal,
  Pressable,
  Text,
  View
} from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import FA from '@expo/vector-icons/FontAwesome6'
import { useRequest } from 'ahooks'

import { useDict } from '@/store/dict'
import { Colors } from '@/theme/colors'
import type { DictType } from '@/types'
import classNames from '@/utils/classname'

type Option<T extends string | number> = { label: string; value: T }

export type SelectProps<T extends string | number> = {
  testId?: string
  placeholder?: string
  value?: T
  clearable?: boolean
  /**
   * Whether the select is disabled. When true, the select cannot be opened and appears with disabled styling.
   */
  disabled?: boolean

  onChange?: (value?: T) => void
} & (
  | {
      options: Option<T>[]
      loading?: boolean
    }
  | {
      dictType: DictType
    }
)

function Select<T extends string | number>({
  testId,
  placeholder = 'Please select',
  clearable = true,
  disabled = false,
  value,
  onChange,
  ...props
}: SelectProps<T>) {
  const [modalVisible, setModalVisible] = useState(false)
  const { getDictItems } = useDict()
  const insets = useSafeAreaInsets()

  const request = useRequest(
    async () => {
      if ('dictType' in props && props.dictType) {
        return getDictItems(props.dictType)
      }
      return undefined
    },
    // @ts-ignore
    { refreshDeps: [props['dictType']] }
  )

  const loading = 'options' in props ? props.loading : request.loading

  const options: Option<T>[] | undefined =
    'options' in props
      ? props.options
      : request.data?.map(i => ({
          label: i.label ?? '',
          value: (i.code ?? '') as T
        }))
  const selectedOption = options?.find(option => option.value === value)

  return (
    <>
      <Pressable
        testID={testId}
        onPress={disabled ? undefined : () => setModalVisible(true)}
        className={classNames(
          'w-full flex-row items-center justify-between overflow-hidden rounded-default border border-solid border-border px-3 py-3',
          disabled ? 'bg-gray-100 opacity-60' : 'bg-white'
        )}
        disabled={disabled}
      >
        <Text
          className={classNames(
            'text-[16px] leading-[20px]',
            disabled ? 'text-gray-500' : 'text-black',
            !selectedOption?.label ? 'opacity-60' : ''
          )}
        >
          {selectedOption?.label || placeholder}
        </Text>
        <FA
          className="-translate-y-1"
          name="sort-down"
          size={14}
          color={disabled ? Colors.gray : Colors.gray}
          style={{ opacity: disabled ? 0.5 : 1 }}
        />
      </Pressable>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible && !disabled}
        onRequestClose={() => setModalVisible(false)}
        statusBarTranslucent={true}
      >
        <View
          className="flex-1"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        >
          <Pressable
            className="flex-1"
            onPress={() => setModalVisible(false)}
          />
          <View
            className="w-full rounded-t-default bg-white p-4"
            style={{ paddingBottom: Math.max(16, insets.bottom) }}
          >
            <View className="flex-row items-center border-b border-b-border pb-4">
              <Text className="mr-auto text-base text-gray">
                Select an option
              </Text>
              {clearable && (
                <Pressable
                  onPress={() => {
                    onChange?.(undefined)
                    setModalVisible(false)
                  }}
                >
                  <Text className="mr-4 text-sm font-medium text-warning">
                    Clear
                  </Text>
                </Pressable>
              )}
              <Pressable onPress={() => setModalVisible(false)}>
                <Text className="text-sm font-medium text-primary">Done</Text>
              </Pressable>
            </View>
            {loading ? (
              <ActivityIndicator />
            ) : (
              <FlatList
                className="max-h-[80vh]"
                data={options}
                keyExtractor={item => `${item.value}`}
                renderItem={({ item }) => (
                  <Pressable
                    className={classNames(
                      'py-3',
                      item.value === value && 'text-primary'
                    )}
                    onPress={() => {
                      onChange?.(item.value)
                      setModalVisible(false)
                    }}
                  >
                    <Text
                      className={classNames(
                        'text-base',
                        item.value === value ? 'text-primary' : 'text-black'
                      )}
                    >
                      {item.label}
                    </Text>
                  </Pressable>
                )}
              />
            )}
          </View>
        </View>
      </Modal>
    </>
  )
}

export { Select }
