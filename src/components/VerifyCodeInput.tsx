import React, { useRef, useState } from 'react'
import type { TextInputKeyPressEventData } from 'react-native'
import { TextInput, View } from 'react-native'
import type { FC } from 'react'

import classNames from '@/utils/classname'

export interface VerifyCodeInputProps {
  value?: string
  onChange?: (value: string) => void
  className?: string
  testID?: string
}

export const VerifyCodeInput: FC<VerifyCodeInputProps> = ({
  value = '',
  onChange,
  className,
  testID
}) => {
  const [code, setCode] = useState(value.split(''))
  const inputRefs = useRef<Array<TextInput | null>>([])

  const handleChange = (text: string, index: number) => {
    // Handle paste event (text length > 1)
    if (text.length > 1) {
      const pastedCode = text.slice(0, 6).split('')
      setCode(pastedCode)
      onChange?.(pastedCode.join(''))
      return
    }

    const newCode = [...code]
    newCode[index] = text
    setCode(newCode)
    onChange?.(newCode.join(''))

    // Move to next input if text is entered
    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyPress = (
    e: { nativeEvent: TextInputKeyPressEventData },
    index: number
  ) => {
    // Handle backspace
    if (e.nativeEvent.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  return (
    <View
      testID={testID}
      className={classNames('flex flex-row justify-between gap-x-2', className)}
    >
      {Array.from({ length: 6 }).map((_, index) => (
        <TextInput
          key={index}
          ref={ref => {
            inputRefs.current[index] = ref
          }}
          value={code[index]}
          onChangeText={text => handleChange(text, index)}
          onKeyPress={e => handleKeyPress(e, index)}
          maxLength={1}
          textAlignVertical="center"
          keyboardType="number-pad"
          className="h-14 w-1/6 max-w-14 flex-1 rounded-default border-[2px] border-border text-center text-lg"
          testID={`${testID}-input-${index}`}
        />
      ))}
    </View>
  )
}
