import type { StyleProp, ViewProps, ViewStyle } from 'react-native'
import { View } from 'react-native'

import { Colors, Shadows } from '@/theme/colors'
import classNames from '@/utils/classname'

export type BorderCardProps = {
  position?: 'left' | 'right' | 'top' | 'bottom'
  color?: string
  className?: string
  style?: StyleProp<ViewStyle>
}

const borderColor = '#eee'

const BorderCard = ({
  color = Colors.primary,
  position = 'left',
  className,
  children,
  style,
  ...props
}: BorderCardProps & ViewProps) => {
  return (
    <View
      className={classNames(
        'relative overflow-hidden rounded-xl border border-solid bg-white p-4',
        className
      )}
      {...props}
      style={[
        {
          borderLeftWidth: position === 'left' ? 4 : 1,
          borderLeftColor: position === 'left' ? color : borderColor,
          borderRightWidth: position === 'right' ? 4 : 1,
          borderRightColor: position === 'right' ? color : borderColor,
          borderTopWidth: position === 'top' ? 4 : 1,
          borderTopColor: position === 'top' ? color : borderColor,
          borderBottomWidth: position === 'bottom' ? 4 : 1,
          borderBottomColor: position === 'bottom' ? color : borderColor,
          boxShadow: Shadows.default
        },
        style
      ]}
    >
      {children}
    </View>
  )
}

export { BorderCard }
