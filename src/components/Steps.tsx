import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { cva, type VariantProps } from 'class-variance-authority'
import clsx from 'clsx'
import type { FC } from 'react'

type StepItem = {
  title: string
}

const stepVariants = cva('', {
  variants: {
    variant: {
      primary: 'bg-primary'
    }
  }
})

const titleVariants = cva('', {
  variants: {
    variant: {
      primary: 'text-primary'
    }
  }
})

type StepsProps = {
  direction?: 'horizontal' | 'vertical'
  variant?: VariantProps<typeof stepVariants>['variant']
  className?: string
  current?: number
  onChange?: (index: number) => void
  items: StepItem[]
  testID?: string
}

export const Steps: FC<StepsProps> = ({
  direction = 'horizontal',
  variant = 'primary',
  className = '',
  current = 0,
  onChange,
  items,
  testID
}) => {
  const containerClass =
    direction === 'vertical'
      ? 'flex-col items-start'
      : 'flex-row items-center justify-between'

  return (
    <View
      testID={testID}
      className={clsx('relative', containerClass, className)}
    >
      {items.map((item, idx) => {
        const isActive = idx === current
        const isCompleted = idx < current

        return (
          <Pressable
            key={idx}
            className={clsx(
              'relative z-[2] gap-2',
              direction === 'vertical'
                ? 'flex-row items-start'
                : 'flex-col items-center'
            )}
            onPress={() => onChange?.(idx)}
          >
            <View
              testID={`step-${idx + 1}`}
              className={clsx(
                'inline-flex h-[30px] w-[30px] items-center justify-center rounded-full text-base',
                isActive
                  ? stepVariants({ variant })
                  : isCompleted
                    ? 'bg-success text-white'
                    : 'border border-border bg-light text-gray'
              )}
            >
              <Text
                className={isActive || isCompleted ? 'text-white' : 'text-gray'}
              >
                {idx + 1}
              </Text>
            </View>
            <Text
              className={clsx(
                'text-xs',
                isActive ? 'font-semibold' : '',
                isActive ? titleVariants({ variant }) : 'text-gray'
              )}
            >
              {item.title}
            </Text>
          </Pressable>
        )
      })}
      <View className="z-1 absolute left-4 right-4 top-[14px] h-[2px] bg-border" />
    </View>
  )
}

export default Steps
