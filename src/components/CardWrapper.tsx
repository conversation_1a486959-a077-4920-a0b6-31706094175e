import { View } from 'react-native'
import type { ReactNode } from 'react'

export function CardWrapper({
  children,
  borderLeftColor
}: {
  children: ReactNode
  borderLeftColor?: string
}) {
  return (
    <View
      className={`w-full rounded-lg border border-[#e5e7eb] shadow-sm ${borderLeftColor ? 'border-l-4' : ''} ${borderLeftColor ? borderLeftColor : ''}`}
    >
      {children}
    </View>
  )
}
