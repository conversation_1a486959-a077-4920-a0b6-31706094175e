import React from 'react'
import { TouchableOpacity, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { FC } from 'react'

import { colors } from '@/theme/colors'

export interface StarProps {
  value?: number
  onChange?: (value: number) => void
  size?: number
  gap?: number
  allowHalf?: boolean
  disabled?: boolean
}

export const Star: FC<StarProps> = ({
  value = 0,
  onChange,
  size = 16,
  gap = 2,
  allowHalf = false,
  disabled = false
}) => {
  const handlePress = (index: number, isHalf: boolean) => {
    if (disabled || !onChange) return
    const newValue = isHalf ? index + 0.5 : index + 1
    onChange(Number(newValue.toFixed(1)))
  }

  const renderStar = (index: number) => {
    const isHalf = allowHalf && value >= index + 0.5 && value < index + 1
    const isFull = value >= index + 1

    return (
      <View key={index} style={{ marginRight: index < 4 ? gap : 0 }}>
        <TouchableOpacity
          testID={`star-${index}`}
          onPress={() => handlePress(index, false)}
          disabled={disabled}
          activeOpacity={0.7}
        >
          <FontAwesome6
            name={isHalf ? 'star-half-stroke' : 'star'}
            size={size}
            solid={isFull}
            color={colors.palette.accent500}
          />
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View className="flex-row items-center">
      {Array.from({ length: 5 }, (_, i) => renderStar(i))}
    </View>
  )
}
