import React from 'react'
import { Pressable, ScrollView, Text } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors, Shadows } from '@/theme/colors'
import classNames from '@/utils/classname'

export type SegmentedOption<T extends string> = {
  label: string
  value: T
  icon?: keyof typeof FontAwesome6.glyphMap
}

export type SegmentedProps<T extends string> = {
  options: SegmentedOption<T>[]
  value?: T
  onChange?: (value: T) => void
  className?: string
  itemClassName?: string
  testID?: string
}

export const Segmented = <T extends string>({
  options,
  value,
  onChange,
  className,
  itemClassName,
  testID
}: SegmentedProps<T>) => {
  return (
    <ScrollView
      testID={testID}
      horizontal
      showsHorizontalScrollIndicator={false}
      className={classNames('rounded-default bg-[#f3f4f6] p-1', className)}
      contentContainerClassName="min-w-full"
    >
      {options.map(option => {
        const isActive = value === option.value
        return (
          <Pressable
            key={option.value}
            role="button"
            onPress={() => onChange?.(option.value)}
            className={classNames(
              'flex flex-1 flex-row items-center justify-center rounded-default px-4 py-3',
              isActive ? 'bg-white text-primary' : 'bg-transparent',
              itemClassName
            )}
            style={{
              boxShadow: isActive ? Shadows.default : ''
            }}
          >
            {option.icon && (
              <FontAwesome6
                name={option.icon}
                className="mr-2"
                size={14}
                color={isActive ? Colors.primary : Colors.gray}
              />
            )}
            <Text
              className={classNames(
                'text-sm',
                isActive ? 'text-primary' : 'text-gray'
              )}
            >
              {option.label}
            </Text>
          </Pressable>
        )
      })}
    </ScrollView>
  )
}
