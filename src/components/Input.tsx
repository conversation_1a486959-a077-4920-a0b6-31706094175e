import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'
import type {
  StyleProp,
  TextInputProps,
  TextStyle,
  ViewStyle
} from 'react-native'
import { Platform, Pressable, TextInput, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import clsx from 'clsx'
import type { ComponentType, Ref } from 'react'

import { Colors } from '@/theme/colors'

export interface TextFieldAccessoryProps {
  status: InputProps['status']
  multiline: boolean
  editable: boolean
}

export interface InputProps extends Omit<TextInputProps, 'ref' | 'onChange'> {
  /**
   * A style modifier for different input states.
   */
  status?: 'error' | 'disabled'
  /**
   * Whether the input is disabled. When true, the input cannot be edited and appears with disabled styling.
   */
  disabled?: boolean
  /**
   * The placeholder text to display if not using `placeholderTx`.
   */
  placeholder?: string
  /**
   * Optional input style override.
   */
  style?: StyleProp<TextStyle>
  /**
   * Style overrides for the container
   */
  containerStyle?: StyleProp<ViewStyle>
  /**
   * An optional component to render on the right side of the input.
   * Example: `RightAccessory={(props) => <Icon icon="ladybug" containerStyle={props.style} color={props.editable ? colors.textDim : colors.text} />}`
   * Note: It is a good idea to memoize this.
   */
  RightAccessory?: ComponentType<TextFieldAccessoryProps>
  /**
   * An optional component to render on the left side of the input.
   * Example: `LeftAccessory={(props) => <Icon icon="ladybug" containerStyle={props.style} color={props.editable ? colors.textDim : colors.text} />}`
   * Note: It is a good idea to memoize this.
   */
  LeftAccessory?: ComponentType<TextFieldAccessoryProps>
  clearable?: boolean
  onChange?: (str: string) => void
  onClear?: () => void
}

export interface InputHandle extends TextInput {
  setText: (text: string) => void
}

/**
 * A component that allows for the entering and editing of text.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/TextField/}
 * @param {TextFieldProps} props - The props for the `TextField` component.
 * @returns {JSX.Element} The rendered `TextField` component.
 */
export const Input = forwardRef<InputHandle, InputProps>(function TextField(
  props: InputProps,
  ref: Ref<InputHandle>
) {
  const {
    placeholder,
    status,
    disabled: disabledProp,
    RightAccessory,
    LeftAccessory,
    clearable = true,
    value,
    onChange,
    onClear,
    ...TextInputProps
  } = props
  const input = useRef<TextInput>(null)

  const disabled =
    disabledProp || TextInputProps.editable === false || status === 'disabled'

  function setText(text: string | undefined) {
    if (Platform.OS === 'web') {
      ;(input.current as unknown as HTMLInputElement).value = text ?? ''
    } else {
      input.current?.setNativeProps({ text: text ?? '' })
    }
  }

  useEffect(() => {
    if (value !== input.current?.state) {
      setText(value)
    }
  }, [value])

  useImperativeHandle(
    ref,
    () =>
      ({
        ...input.current,
        setText
      }) as InputHandle
  )

  return (
    <View
      className={clsx(
        'flex w-full flex-row items-center rounded-default border border-border px-3 py-3',
        disabled ? 'bg-gray-100 opacity-60' : 'bg-white',
        props.className
      )}
      style={props.containerStyle}
    >
      {!!LeftAccessory && (
        <LeftAccessory
          status={status}
          editable={!disabled}
          multiline={TextInputProps.multiline ?? false}
        />
      )}
      <TextInput
        ref={input}
        className={clsx(
          'min-h-[20px] min-w-0 flex-1 outline-none',
          LeftAccessory && 'ml-3',
          (RightAccessory || clearable) && 'mr-3',
          disabled && 'text-gray-500'
        )}
        underlineColorAndroid={Colors.transparent}
        textAlignVertical="center"
        placeholder={placeholder}
        placeholderTextColor={disabled ? Colors.gray : Colors.gray}
        // placeholderTextColor={Colors['light-gray']}
        {...TextInputProps}
        value={value}
        onChangeText={onChange}
        editable={!disabled}
        style={[
          {
            paddingHorizontal: 0,
            paddingVertical: 0,
            fontSize: 16,
            color: disabled ? Colors.gray : undefined
          },
          props.style
        ]}
      />
      {clearable && !!value?.toString() && !disabled ? (
        <Pressable
          onPress={() => {
            input.current?.clear()
            onChange?.('')
            onClear?.()
          }}
          // Let Tab key skip this element
          accessible={false}
          importantForAccessibility="no"
          tabIndex={-1}
        >
          <FA
            className={RightAccessory ? 'mr-3' : ''}
            name="xmark"
            size={16}
            color={Colors.gray}
            style={{ opacity: 0.6 }}
          />
        </Pressable>
      ) : null}
      {RightAccessory ? (
        <RightAccessory
          status={status}
          editable={!disabled}
          multiline={TextInputProps.multiline ?? false}
        />
      ) : null}
    </View>
  )
})

Input.displayName = 'Input'
