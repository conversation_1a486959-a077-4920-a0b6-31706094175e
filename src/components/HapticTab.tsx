import type { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs'
import * as Haptics from 'expo-haptics'
const { PlatformPressable } = require('@react-navigation/elements')

export function HapticTab(props: typeof BottomTabBarButtonProps) {
  return (
    <PlatformPressable
      {...props}
      // @ts-expect-error
      onPressIn={ev => {
        if (process.env.EXPO_OS === 'ios') {
          // Add a soft haptic feedback when pressing down on the tabs.
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        }
        props.onPressIn?.(ev)
      }}
    />
  )
}
