import { Text, View } from 'react-native'
import FA from 'react-native-vector-icons/FontAwesome6'
import { cva, type VariantProps } from 'class-variance-authority'
import type { ComponentProps, ReactNode } from 'react'

import classNames from '@/utils/classname'

const bgVariants = cva('', {
  variants: {
    type: {
      info: 'bg-info-light',
      tenant: 'bg-tenant-light',
      success: 'bg-success-light'
      // warning: 'bg-warning-bg',
      // danger: 'bg-danger-bg'
    }
  }
})

const iconVariants = cva('', {
  variants: {
    type: {
      info: 'bg-info',
      tenant: 'bg-tenant',
      success: 'bg-success'
    }
  }
})

const textVariants = cva('', {
  variants: {
    type: {
      info: 'text-info',
      tenant: 'text-tenant',
      success: 'text-success'
    }
  }
})

export type AlertProps = {
  title?: string
  titleClassName?: string
  message?: string
  messageClassName?: string
  leftIcon?: ComponentProps<typeof FA>['name'] | ReactNode
  type?: VariantProps<typeof bgVariants>['type']
  testID?: string
  // onClose: () => void
}

const Alert = ({
  title,
  titleClassName,
  message,
  messageClassName,
  leftIcon,
  type = 'info',
  testID
}: AlertProps) => {
  return (
    <View
      testID={testID}
      className={classNames(
        'flex flex-row items-center gap-4 rounded-md p-4',
        bgVariants({ type })
      )}
    >
      {leftIcon &&
        (typeof leftIcon === 'string' ? (
          <View
            className={classNames(
              'inline-flex h-10 w-10 flex-shrink-0 flex-row items-center justify-center rounded-full',
              iconVariants({ type })
            )}
          >
            <FA name={leftIcon} size={20} color="white" />
          </View>
        ) : (
          leftIcon
        ))}
      {title || message ? (
        <View className="flex flex-1 flex-col gap-[5px] text-base">
          {title && (
            <Text
              className={classNames(
                'text-sm font-semibold',
                textVariants({ type }),
                titleClassName
              )}
            >
              {title}
            </Text>
          )}
          {message && (
            <Text className={classNames('text-sm', messageClassName)}>
              {message}
            </Text>
          )}
        </View>
      ) : null}
    </View>
  )
}

export default Alert
