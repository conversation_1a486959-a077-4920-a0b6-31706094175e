import { Image, Text, View } from 'react-native'

import { useAuth } from '@/store'
import classNames from '@/utils/classname'

// TenantDashboardHeader displays the welcome message and avatar for the tenant dashboard
export default function TenantDashboardHeader() {
  const { user } = useAuth()
  return (
    <View
      className={classNames(
        'mb-4 flex-row items-center justify-between border-b bg-white p-5',
        'border-border'
      )}
    >
      <View>
        <Text className="mb-1 text-xl font-semibold text-dark">
          Hello, {user?.userName}
        </Text>
        <Text className="text-sm text-dark/80">
          Welcome to your tenant dashboard
        </Text>
      </View>
      <View className="h-10 w-10 items-center justify-center rounded-full bg-tenant-light">
        {user?.avatar ? (
          <Image
            source={{ uri: user.avatar }}
            width={40}
            height={40}
            className="h-full w-full rounded-full"
          />
        ) : (
          <Text className="text-lg font-semibold text-tenant">
            {user?.userName?.charAt(0)}
          </Text>
        )}
      </View>
    </View>
  )
}
