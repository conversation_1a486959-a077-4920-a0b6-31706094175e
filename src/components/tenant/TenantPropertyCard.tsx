import { Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'

import { useMyProperty } from '@/store/myProperty'
import { ShadowStyles } from '@/theme/colors'
import { getFullAddr } from '@/utils/addr'
import { formatDate } from '@/utils/formatDate'

import PhotoSwiper from '../PhotoSwiper'

// TenantPropertyCard displays the current property information
export default function TenantPropertyCard() {
  const { myProperty } = useMyProperty()
  return (
    <View
      className="mb-5 overflow-hidden rounded-lg bg-white"
      style={ShadowStyles.default}
    >
      <PhotoSwiper
        photos={myProperty!.photos!.map(i => i.fileUrl!)}
        imageHeight={144}
      />
      <View className="p-4">
        <Text className="mb-1 text-base font-semibold text-dark">
          {myProperty!.propertyName}
        </Text>
        <Text className="mb-2 text-sm text-gray">
          {getFullAddr(myProperty, { showZip: false })}
        </Text>
        <View className="mb-2 flex-row justify-between">
          <Text className="text-xs text-gray">
            <FA name="bed" size={14} className="text-tenant" />{' '}
            {myProperty!.bedroomCount} Beds
          </Text>
          <Text className="text-xs text-gray">
            <FA name="bath" size={14} className="text-tenant" />{' '}
            {myProperty!.bathroomCount} Baths
          </Text>
          <Text className="text-xs text-gray">
            <FA name="vector-square" size={14} className="text-tenant" />{' '}
            {myProperty!.sizeSqFt} sq ft
          </Text>
        </View>
        <View className="flex-row">
          <Text className="text-xs text-gray">
            <FA name="calendar-days" size={14} className="text-tenant" /> Lease
            ends: {/* @ts-ignore */}
            {formatDate(myProperty?.leaseEndDate)}
          </Text>
        </View>
      </View>
    </View>
  )
}
