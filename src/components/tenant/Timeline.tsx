import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

// Timeline item type definition
export interface TimelineItem {
  icon: string
  title: string
  date: string
  description: string
}

interface TimelineProps {
  items: TimelineItem[]
}

/**
 * Timeline component for displaying activity history
 */
export default function Timeline({ items }: TimelineProps) {
  return (
    <View className="mt-2">
      {items.map((item, idx) => (
        <View key={idx} className="relative mb-5 flex-row">
          {/* Timeline vertical line */}
          {idx < items.length - 1 && (
            <View
              style={{
                position: 'absolute',
                top: 30,
                left: 15,
                bottom: -20,
                width: 2,
                backgroundColor: '#e5e7eb',
                zIndex: 0
              }}
            />
          )}
          {/* Timeline icon */}
          <View className="z-10 mr-4 h-8 w-8 items-center justify-center rounded-full bg-tenant">
            <FontAwesome6 name={item.icon} size={16} color="#fff" solid />
          </View>
          {/* Timeline content */}
          <View className="flex-1">
            <Text className="mb-1 font-semibold">{item.title}</Text>
            <Text className="mb-1 text-xs text-gray">{item.date}</Text>
            <Text className="text-sm leading-5">{item.description}</Text>
          </View>
        </View>
      ))}
    </View>
  )
}
