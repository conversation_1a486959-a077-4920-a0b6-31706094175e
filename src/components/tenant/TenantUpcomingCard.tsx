import { Text, TouchableOpacity, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'

import { Colors, ShadowStyles } from '@/theme/colors'

// TenantUpcomingCard displays an upcoming event for the tenant
export default function TenantUpcomingCard() {
  return (
    <View
      className="mb-4 flex-row items-center rounded-lg bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="mr-4 w-12 items-center">
        <Text className="text-xs uppercase text-gray">May</Text>
        <Text className="text-xl font-bold text-tenant">15</Text>
      </View>
      <View className="flex-1">
        <Text className="mb-1 text-sm font-semibold text-dark">
          Property Inspection
        </Text>
        <Text className="text-xs text-gray">2:00 PM - 4:00 PM</Text>
      </View>
      <TouchableOpacity className="ml-2">
        <FA name="chevron-right" size={20} color={Colors.tenant} />
      </TouchableOpacity>
    </View>
  )
}
