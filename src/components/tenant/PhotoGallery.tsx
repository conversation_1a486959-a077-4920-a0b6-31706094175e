import React, { useMemo, useState } from 'react'
import {
  ActivityIndicator,
  Image,
  Pressable,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6'
import { useRequest } from 'ahooks'

import type { LocalPhoto } from '@/hooks/useChoosePhoto'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import PhotoPreview from '../common/PhotoPreview'

type PhotoGalleryProps = {
  className?: string
  removable?: boolean
  onRemove?: (index: number) => void
  localPhotos?: LocalPhoto[]
} & (
  | {
      photos: string[]
    }
  | {
      remotePhotos: components['schemas']['FileInfo'][]
    }
)

/**
 * PhotoGallery component for displaying a grid of images
 */
function PhotoGallery(props: PhotoGalleryProps) {
  // Memoize file keys to prevent unnecessary API calls
  const remotePhotos = 'remotePhotos' in props ? props.remotePhotos : []
  const isPhotosMode = 'photos' in props
  const fileKeys = useMemo(() => {
    if (isPhotosMode) {
      return null
    }
    return remotePhotos
      .map(i => i.fileKey)
      .filter((k): k is string => typeof k === 'string')
  }, [isPhotosMode, remotePhotos])

  const request = useRequest(
    async () => {
      if ('photos' in props) {
        return Promise.resolve(props.photos)
      }

      // Only make API call if we have valid file keys
      if (!fileKeys || fileKeys.length === 0) {
        return []
      }

      const { data } = await client.POST(
        '/api/v1/file/presigned-urls/download',
        {
          body: {
            fileKeys
          }
        }
      )
      return data?.data?.map(i => i.url) ?? []
    },
    {
      refreshDeps: [fileKeys],
      // Cache the result to prevent unnecessary re-renders
      cacheKey: fileKeys ? `photo-urls-${fileKeys.join(',')}` : undefined
    }
  )

  const [previewIndex, setPreviewIndex] = useState(-1)

  // merge local and remote photos
  type PhotoItem =
    | LocalPhoto
    | { uri: string; isRemote: true; isDownloading?: boolean }

  const allPhotos: PhotoItem[] = useMemo(() => {
    const localPhotos = props.localPhotos || []

    // Handle remote photos with download state
    let remotePhotoItems: PhotoItem[] = []
    if (!isPhotosMode && remotePhotos.length > 0) {
      if (request.loading) {
        // Show loading state for remote photos while downloading URLs
        remotePhotoItems = remotePhotos.map(() => ({
          uri: '', // Placeholder
          isRemote: true,
          isDownloading: true
        }))
      } else if (request.data) {
        remotePhotoItems = request.data.map(uri => ({
          uri,
          isRemote: true,
          isDownloading: false
        }))
      }
    }

    return [...localPhotos, ...remotePhotoItems]
  }, [
    props.localPhotos,
    isPhotosMode,
    remotePhotos,
    request.loading,
    request.data
  ])

  const allPhotoUrls = useMemo(
    () => allPhotos.map(photo => photo.uri).filter(Boolean),
    [allPhotos]
  )

  return (
    <>
      <View
        className={classNames('mt-2 flex-row flex-wrap gap-2', props.className)}
      >
        {allPhotos.map((photo, idx) => {
          const isLocal = 'asset' in photo
          const isRemote = 'isRemote' in photo
          const isDownloading = isRemote && photo.isDownloading
          const uri = photo.uri

          return (
            <Pressable
              key={isLocal ? photo.id : `remote_${idx}`}
              className="relative aspect-square min-w-[31%] max-w-[33.33%] flex-1 rounded-lg bg-gray-100"
              onPress={() =>
                !isDownloading && uri ? setPreviewIndex(idx) : undefined
              }
            >
              {/* Show image only if we have a valid URI */}
              {uri && !isDownloading && (
                <Image
                  source={{ uri }}
                  className="h-full w-full rounded-lg"
                  resizeMode="cover"
                />
              )}

              {/* upload progress for local photos */}
              {isLocal && photo.isUploading && (
                <View className="absolute inset-0 flex-row items-center justify-center rounded-lg bg-black/50">
                  <View className="items-center">
                    <ActivityIndicator size="small" color={Colors.white} />
                    <Text className="mt-1 text-xs text-white">
                      {photo.uploadProgress.toFixed(2)}%
                    </Text>
                  </View>
                </View>
              )}

              {/* download progress for remote photos */}
              {isDownloading && (
                <View className="absolute inset-0 flex-row items-center justify-center rounded-lg bg-gray-200">
                  <View className="items-center">
                    <ActivityIndicator size="small" color={Colors.gray} />
                    <Text className="mt-1 text-xs text-gray">Loading...</Text>
                  </View>
                </View>
              )}

              {/* remove btn */}
              {props.removable && !isDownloading && (
                <TouchableOpacity
                  className="absolute -right-2 -top-2 z-10 h-6 w-6 flex-row items-center justify-center rounded-full bg-black/70 p-1"
                  onPress={() => props.onRemove?.(idx)}
                  hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                >
                  <FontAwesome6 name="xmark" size={16} color={Colors.white} />
                </TouchableOpacity>
              )}
            </Pressable>
          )
        })}
      </View>

      <PhotoPreview
        index={previewIndex}
        visible={previewIndex > -1}
        onVisibleChange={() => setPreviewIndex(-1)}
        urls={useMemo(
          () =>
            allPhotoUrls.map(url => ({
              url
            })),
          [allPhotoUrls]
        )}
      />
    </>
  )
}

// Custom comparison function to prevent unnecessary re-renders
const areEqual = (
  prevProps: PhotoGalleryProps,
  nextProps: PhotoGalleryProps
) => {
  // Compare localPhotos
  if (prevProps.localPhotos !== nextProps.localPhotos) {
    return false
  }

  // Compare removable and onRemove
  if (
    prevProps.removable !== nextProps.removable ||
    prevProps.onRemove !== nextProps.onRemove
  ) {
    return false
  }

  // Compare className
  if (prevProps.className !== nextProps.className) {
    return false
  }

  // Compare photos vs remotePhotos
  if ('photos' in prevProps && 'photos' in nextProps) {
    return prevProps.photos === nextProps.photos
  }

  if ('remotePhotos' in prevProps && 'remotePhotos' in nextProps) {
    // Deep compare remotePhotos array
    if (prevProps.remotePhotos.length !== nextProps.remotePhotos.length) {
      return false
    }

    return prevProps.remotePhotos.every((prev, index) => {
      const next = nextProps.remotePhotos[index]
      return (
        prev?.fileKey === next?.fileKey && prev?.fileName === next?.fileName
      )
    })
  }

  // Different prop types (photos vs remotePhotos)
  return false
}

export default React.memo(PhotoGallery, areEqual)
