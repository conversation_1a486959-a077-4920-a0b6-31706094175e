import { Text, TouchableOpacity, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { router } from 'expo-router'

import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

// Quick action button data
type FA6IconName = keyof typeof FA.glyphMap
const actions: { icon: FA6IconName; label: string; onPress: () => void }[] = [
  {
    icon: 'wrench',
    label: 'Request Repair',
    onPress: () => {
      router.push('/tenant/maintenance/create')
    }
  },
  {
    icon: 'comment',
    label: 'Message',
    onPress: () => {
      router.push('/tenant/messages')
    }
  },
  {
    icon: 'calendar-days',
    label: 'Calendar',
    onPress: () => {
      router.push('/tenant/calendar')
    }
  },
  { icon: 'bell', label: 'Notifications', onPress: () => {} }
]

// TenantQuickActions displays a grid of quick action buttons
export default function TenantQuickActions() {
  return (
    <View className="mb-6 flex flex-row gap-2">
      {actions.map(action => (
        <TouchableOpacity
          key={action.label}
          className={classNames(
            'flex flex-1 flex-col items-center justify-center rounded-lg bg-white py-3'
          )}
          style={ShadowStyles.default}
          onPress={action.onPress}
          activeOpacity={0.7}
        >
          <FA
            name={action.icon}
            size={24}
            solid
            className="mb-2"
            color={Colors.tenant}
          />
          <Text className="text-center text-xs font-medium text-dark">
            {action.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )
}
