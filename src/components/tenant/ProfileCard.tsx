import React from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { useActionSheet } from '@expo/react-native-action-sheet'
import { FontAwesome6 } from '@expo/vector-icons'

import { useChoosePhoto } from '@/hooks/useChoosePhoto'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useAuth } from '@/store'
import { useMyProperty } from '@/store/myProperty'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'
import { formatDate } from '@/utils/formatDate'

// ProfileCard component for tenant profile page
export function ProfileCard({
  profile
}: {
  profile: components['schemas']['TenantProfileDTO']
}) {
  const {
    computed: { avatar },
    refreshMe
  } = useAuth()
  const { showActionSheetWithOptions } = useActionSheet()
  const { myProperty } = useMyProperty()
  const { chooseFromCamera, chooseFromLibrary } = useChoosePhoto({
    async onUploaded(fileKeys) {
      const { error } = await client.PATCH('/api/v1/admin/user/me/avatar', {
        // @ts-ignore
        body: {
          fileKey: fileKeys[0]!.fileKey
        }
      })
      if (!error) {
        refreshMe()
      }
    }
  })
  const onSelectPhoto = () => {
    showActionSheetWithOptions(
      {
        options: ['Take Photo', 'Choose from Library', 'Cancel'],
        cancelButtonIndex: 2,
        title: 'Select Photo',
        message: 'Please select a photo from your library or take a new one',
        userInterfaceStyle: 'light',
        tintColor: '#000',
        destructiveButtonIndex: 2
      },
      selectedIndex => {
        if (selectedIndex === 0) {
          chooseFromCamera()
        } else if (selectedIndex === 1) {
          chooseFromLibrary()
        }
      }
    )
  }

  return (
    <View
      className="mb-5 overflow-hidden rounded-2xl"
      style={{
        backgroundColor: Colors.white,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 2
      }}
    >
      {/* Profile Header with gradient background */}
      <View
        className="items-center px-5 py-8"
        style={{
          backgroundColor: Colors.tenant
        }}
      >
        {/* Avatar with upload button */}
        <View className="relative mb-4">
          <View
            className={classNames(
              'h-20 w-20 items-center justify-center rounded-full',
              'bg-white/20'
            )}
          >
            {avatar ? (
              <Image
                className="h-full w-full rounded-full"
                source={{
                  uri: avatar
                }}
                width={80}
                height={80}
              />
            ) : (
              <Text className="text-3xl font-semibold text-white">
                {profile.personalInfo?.userName?.charAt(0)?.toUpperCase()}
              </Text>
            )}
            {/* Avatar upload button */}
            <Pressable
              className="w-7.5 h-7.5 absolute -bottom-1.5 -right-1.5 items-center justify-center rounded-full border-2 border-white"
              style={{ backgroundColor: Colors.tenant }}
              onPress={onSelectPhoto}
              accessibilityLabel="Change avatar"
            >
              <FontAwesome6 name="camera" size={16} color={Colors.white} />
            </Pressable>
          </View>
        </View>
        <Text className="mb-1 text-xl font-semibold text-white">
          {profile.personalInfo?.userName}
        </Text>
        <Text className="text-sm text-white opacity-90">
          {profile.personalInfo?.email}
        </Text>
      </View>

      {/* Personal Information Section */}
      <View className="px-5 py-5">
        <View className="mb-3 flex-row items-center">
          <FontAwesome6
            name="user"
            size={16}
            color={Colors.tenant}
            solid
            className="mr-2"
          />
          <Text className="text-base font-semibold text-dark">
            Personal Information
          </Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-200 py-3">
          <Text className="text-gray-500 text-sm">Full Name</Text>
          <Text className="text-sm font-medium text-dark">
            {profile.personalInfo?.userName}
          </Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-200 py-3">
          <Text className="text-gray-500 text-sm">Phone Number</Text>
          <Text className="text-sm font-medium text-dark">
            {profile.personalInfo?.phoneNumber}
          </Text>
        </View>
        <View className="flex-row items-center justify-between py-3">
          <Text className="text-gray-500 text-sm">Date of Birth</Text>
          <Text className="text-sm font-medium text-dark">
            {formatDate(profile.personalInfo?.birthday)}
          </Text>
        </View>
      </View>

      {/* Property Information Section */}
      <View className="px-5 pb-5">
        <View className="mb-3 flex-row items-center">
          <FontAwesome6
            name="house"
            size={16}
            color={Colors.tenant}
            className="mr-2"
          />
          <Text className="text-base font-semibold text-dark">
            Property Information
          </Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-200 py-3">
          <Text className="text-gray-500 text-sm">Current Property</Text>
          <Text className="text-sm font-medium text-dark">
            {myProperty?.propertyName}
          </Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-200 py-3">
          <Text className="text-gray-500 text-sm">Lease Start</Text>
          <Text className="text-sm font-medium text-dark">
            {formatDate(profile.currentLease?.beginDate)}
          </Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-200 py-3">
          <Text className="text-gray-500 text-sm">Lease End</Text>
          <Text className="text-sm font-medium text-dark">
            {formatDate(profile.currentLease?.endDate)}
          </Text>
        </View>
        <View className="flex-row items-center justify-between py-3">
          <Text className="text-gray-500 text-sm">Monthly Rent</Text>
          <Text className="text-sm font-medium text-dark">
            {profile.currentLease?.monthlyRent}
          </Text>
        </View>
      </View>
    </View>
  )
}
