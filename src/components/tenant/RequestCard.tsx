import React from 'react'
import { Text, View } from 'react-native'
import { router } from 'expo-router'
import type { FC } from 'react'

import { BorderCard, Button } from '@/components'
import type { components } from '@/services/api/schema'
import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'
import { confirm } from '@/utils/confirm'
import { formatDate } from '@/utils/formatDate'

export interface RequestCardProps {
  request: components['schemas']['ItemInfoDTO']
  onCancelRequest: VoidFunction
}

export function getRequestColor(status: string | undefined) {
  switch (status) {
    case 'DRAFT':
      return [Colors.dark, Colors.light]
    case 'SUBMITTED':
      return [Colors.primary, Colors['primary-light']]
    case 'PENDING_QUOTES':
      return [Colors.warning, Colors['warning-light']]
    case 'IN_PROGRESS':
      return [Colors.info, Colors['info-light']]
    case 'COMPLETED':
      return [Colors.success, Colors['success-light']]
    default:
      return [Colors.danger, Colors['danger-light']]
  }
}

/**
 * Card component for displaying a maintenance request with status, details, and actions.
 */
export const RequestCard: FC<RequestCardProps> = ({
  request,
  onCancelRequest
}) => {
  const status = request.status?.toUpperCase()
  const [textColor, bgColor] = getRequestColor(status)
  const onCancel = () => {
    confirm('Are you sure to cancel this maintenance request?', onCancelRequest)
  }
  return (
    <BorderCard
      className={classNames('mb-4')}
      color={textColor}
      style={ShadowStyles.default}
    >
      {/* Request ID */}
      <Text className="mb-1 text-xs text-gray">
        Ticket #{request.projectId}
      </Text>
      {/* Title */}
      <Text className="mb-2 text-base font-semibold text-dark">
        {request.itemName}
      </Text>
      {/* Details */}
      <View className="mb-3 flex flex-col gap-2">
        <View className="flex flex-row text-sm">
          <Text className="w-24 text-gray">Submitted:</Text>
          <Text className="flex-1">{formatDate(request.createdTime)}</Text>
        </View>
        {status === 'COMPLETED' ? (
          <View className="flex flex-row text-sm">
            <Text className="w-24 text-gray">Completed:</Text>
            <Text className="flex-1">{formatDate(request.updatedTime)}</Text>
          </View>
        ) : status === 'CANCELLED' ? (
          <View className="flex flex-row text-sm">
            <Text className="w-24 text-gray">Cancelled:</Text>
            <Text className="flex-1">{formatDate(request.updatedTime)}</Text>
          </View>
        ) : (
          <View className="flex flex-row text-sm">
            <Text className="w-24 text-gray">Priority:</Text>
            <Text className="flex-1">{request.priority}</Text>
          </View>
        )}
        <View className="flex flex-row items-center text-sm">
          <Text className="w-24 text-gray">Status:</Text>
          <View
            className={classNames('rounded px-2 py-0.5')}
            style={{
              backgroundColor: bgColor
            }}
          >
            <Text
              className={classNames('text-xs font-semibold')}
              style={{ color: textColor }}
            >
              {request.status}
            </Text>
          </View>
        </View>
      </View>
      {/* Actions */}
      <View className="mt-2 flex flex-row gap-2">
        {/* <TouchableOpacity
          className="flex-1 flex-row items-center justify-center rounded border border-tenant bg-tenant/10 px-3 py-2"
          onPress={onView}
          accessibilityLabel="View Details"
        >
          <FontAwesome6
            name="eye"
            size={16}
            color={Colors.tenant}
            solid
            className="mr-2"
          />
          <Text className="font-medium text-tenant">View Details</Text>
        </TouchableOpacity> */}
        <Button
          variant="tenant-light"
          className="flex-1"
          leftIcon="eye"
          leftIconProps={{ solid: true }}
          onPress={() =>
            router.push({
              pathname: '/tenant/maintenance/[id]',
              params: { id: request.projectId! }
            })
          }
          accessibilityLabel="View Details"
        >
          View Details
        </Button>
        {['DRAFT', 'SUBMITTED', 'PENDING_QUOTES'].includes(status || '') && (
          <Button
            variant="cancel"
            leftIcon="xmark"
            className="flex-1"
            onPress={onCancel}
            accessibilityLabel="Cancel Request"
          >
            Cancel
          </Button>
        )}
      </View>
    </BorderCard>
  )
}

export default RequestCard
