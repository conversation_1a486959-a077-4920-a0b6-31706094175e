import { useMemo, useState } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

interface CalendarViewProps {
  view: 'month' | 'list'
  onViewChange: (view: 'month' | 'list') => void
  onDayPress?: (day: number) => void
}

const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

function getDaysMatrix(year: number, month: number) {
  // month: 0-based
  const today = new Date()
  const isCurrentMonth =
    today.getFullYear() === year && today.getMonth() === month
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const prevMonthLastDay = new Date(year, month, 0)
  const daysInMonth = lastDay.getDate()
  const daysInPrevMonth = prevMonthLastDay.getDate()
  const startWeekDay = firstDay.getDay() // 0=Sun
  const days = []
  // Fill previous month's days
  for (let i = 0; i < startWeekDay; i++) {
    days.push({
      day: daysInPrevMonth - startWeekDay + i + 1,
      isToday: false,
      isOtherMonth: true,
      isPrevMonth: true,
      isNextMonth: false
    })
  }
  // Fill current month's days
  for (let i = 1; i <= daysInMonth; i++) {
    days.push({
      day: i,
      isToday: isCurrentMonth && i === today.getDate(),
      isOtherMonth: false,
      isPrevMonth: false,
      isNextMonth: false
    })
  }
  // Fill next month's days
  const totalCells = Math.ceil(days.length / 7) * 7
  for (let i = 1; days.length < totalCells; i++) {
    days.push({
      day: i,
      isToday: false,
      isOtherMonth: true,
      isPrevMonth: false,
      isNextMonth: true
    })
  }
  // Group into weeks
  const weeks = []
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7))
  }
  return weeks
}

const CalendarView = ({
  view,
  onViewChange,
  onDayPress
}: CalendarViewProps) => {
  // Use state to store current year and month
  const [current, setCurrent] = useState(() => {
    const now = new Date()
    return { year: now.getFullYear(), month: now.getMonth() }
  })
  const { year, month } = current
  const weeks = useMemo(() => getDaysMatrix(year, month), [year, month])

  // Handlers for switching month
  const handlePrevMonth = () => {
    setCurrent(prev => {
      let month = prev.month - 1
      let year = prev.year
      if (month < 0) {
        month = 11
        year -= 1
      }
      return { year, month }
    })
  }
  const handleNextMonth = () => {
    setCurrent(prev => {
      let month = prev.month + 1
      let year = prev.year
      if (month > 11) {
        month = 0
        year += 1
      }
      return { year, month }
    })
  }

  return (
    <View>
      {/* Calendar Navigation */}
      <View className="mb-4 flex-row items-center justify-between">
        <View className="flex-row items-center">
          <TouchableOpacity
            className="mr-2 h-9 w-9 items-center justify-center rounded-full border border-border bg-white"
            activeOpacity={0.7}
            onPress={handlePrevMonth}
          >
            <FontAwesome6 name="chevron-left" size={16} color={Colors.dark} />
          </TouchableOpacity>
          <Text className="mx-2 text-lg font-semibold">{`${new Date(year, month).toLocaleString('default', { month: 'long' })} ${year}`}</Text>
          <TouchableOpacity
            className="ml-2 h-9 w-9 items-center justify-center rounded-full border border-border bg-white"
            activeOpacity={0.7}
            onPress={handleNextMonth}
          >
            <FontAwesome6 name="chevron-right" size={16} color={Colors.dark} />
          </TouchableOpacity>
        </View>
        <View className="flex-row items-center rounded-full bg-light-gray p-1">
          <TouchableOpacity
            className={classNames(
              'rounded-full px-3 py-1',
              view === 'month' ? 'bg-white font-medium' : ''
            )}
            onPress={() => onViewChange('month')}
            activeOpacity={0.7}
          >
            <Text
              className={classNames(
                'text-xs',
                view === 'month' ? 'font-medium text-dark' : 'text-gray'
              )}
            >
              Month
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={classNames(
              'rounded-full px-3 py-1',
              view === 'list' ? 'bg-white font-medium' : ''
            )}
            onPress={() => onViewChange('list')}
            activeOpacity={0.7}
          >
            <Text
              className={classNames(
                'text-xs',
                view === 'list' ? 'font-medium text-dark' : 'text-gray'
              )}
            >
              List
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Calendar Grid */}
      {view === 'month' && (
        <View
          className="mb-5 overflow-hidden rounded-xl bg-white"
          style={ShadowStyles.default}
        >
          {/* Weekdays */}
          <View className="flex-row bg-tenant-light">
            {weekdays.map(w => (
              <View key={w} className="flex-1 items-center py-2">
                <Text className="text-xs font-medium text-tenant">{w}</Text>
              </View>
            ))}
          </View>
          {/* Days Grid - week by week */}
          {weeks.map((week, weekIdx) => (
            <View
              key={weekIdx}
              className="flex-row border-b border-l border-border"
            >
              {week.map((d, i) => (
                <TouchableOpacity
                  key={i}
                  className={classNames(
                    'h-12 flex-1 border-r border-t border-border',
                    d.isToday ? 'bg-tenant-light font-bold' : '',
                    d.isOtherMonth ? 'opacity-40' : '',
                    'p-1'
                  )}
                  activeOpacity={0.7}
                  onPress={() =>
                    !d.isOtherMonth && onDayPress && onDayPress(d.day)
                  }
                  disabled={d.isOtherMonth}
                >
                  <Text
                    className={classNames(
                      'text-base',
                      d.isToday ? 'text-tenant' : 'text-dark',
                      d.isOtherMonth ? 'text-gray' : ''
                    )}
                    style={{ textAlign: 'center' }}
                  >
                    {d.day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>
      )}
    </View>
  )
}

export default CalendarView
