import { Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'

import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

type FA6IconName = keyof typeof FA.glyphMap

interface TenantNotificationCardProps {
  icon: FA6IconName
  type: 'maintenance' | 'announcement'
  title: string
  text: string
  time: string
}

// TenantNotificationCard displays a notification item for the tenant
export default function TenantNotificationCard({
  icon,
  type,
  title,
  text,
  time
}: TenantNotificationCardProps) {
  const iconBg = type === 'maintenance' ? 'bg-warning/20' : 'bg-info/20'
  const iconColor = type === 'maintenance' ? Colors.warning : Colors.info
  return (
    <View
      className="mb-4 flex-row items-center rounded-lg bg-white p-4"
      style={ShadowStyles.default}
    >
      <View
        className={classNames(
          'mr-4 h-11 w-11 items-center justify-center rounded-full',
          iconBg
        )}
      >
        <FA name={icon} size={20} color={iconColor} />
      </View>
      <View className="flex-1">
        <Text className="mb-1 text-sm font-semibold text-dark">{title}</Text>
        <Text className="text-xs text-gray">{text}</Text>
      </View>
      <Text className="ml-2 text-xs text-gray">{time}</Text>
    </View>
  )
}
