import React, { createRef, useEffect } from 'react'
import { act, fireEvent, render } from '@testing-library/react-native'

import type { FormInstance } from '../Form'
import { Form } from '../Form'
import { FormItem } from '../FormItem'
import { Input } from '../Input'

describe('Form & FormItem', () => {
  it('renders and submits with values', async () => {
    const onFinish = jest.fn()

    const TestForm = () => {
      const form = Form.useForm()

      useEffect(() => {
        formRef.current = form
      }, [form])

      return (
        <Form form={form} onFinish={onFinish}>
          <FormItem name="username" label="Username" rules={{ required: true }}>
            <Input placeholder="Username" />
          </FormItem>
        </Form>
      )
    }

    const formRef = createRef<FormInstance>()

    const { getByPlaceholderText } = render(<TestForm />)
    const input = getByPlaceholderText('Username')
    await act(async () => {
      fireEvent.changeText(input, 'testuser')
      fireEvent(input, 'blur')
    })
    await act(async () => {
      formRef.current?.submit()
    })
    expect(onFinish).toHaveBeenCalledWith({ username: 'testuser' })
  })

  it('shows required error', async () => {
    const onFinish = jest.fn()

    // eslint-disable-next-line unused-imports/no-unused-vars
    const TestForm = () => {
      const form = Form.useForm()

      useEffect(() => {
        formRef.current = form
      }, [form])

      return (
        <Form form={form} onFinish={onFinish}>
          <FormItem
            name="username"
            label="Username"
            rules={{ required: { value: true, message: 'Required' } }}
          >
            <Input placeholder="Username" />
          </FormItem>
        </Form>
      )
    }

    const formRef = createRef<FormInstance>()

    // const { getByText } = render(<TestForm />)
    await act(async () => {
      formRef.current?.submit()
    })
    // expect(getByText('Required')).toBeTruthy()
    expect(onFinish).not.toHaveBeenCalled()
  })
})
