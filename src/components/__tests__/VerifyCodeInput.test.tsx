import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { VerifyCodeInput } from '../VerifyCodeInput'

describe('VerifyCodeInput', () => {
  it('renders 6 input boxes', () => {
    const { getAllByTestId } = render(<VerifyCodeInput testID="verify-code" />)
    const inputs = getAllByTestId(/verify-code-input-\d/)
    expect(inputs).toHaveLength(6)
  })

  it('handles input changes correctly', () => {
    const onChange = jest.fn()
    const { getAllByTestId } = render(
      <VerifyCodeInput onChange={onChange} testID="verify-code" />
    )
    const inputs = getAllByTestId(/verify-code-input-\d/)

    // Input first digit
    fireEvent.changeText(inputs[0], '1')
    expect(onChange).toHaveBeenCalledWith('1')

    // Input second digit
    fireEvent.changeText(inputs[1], '2')
    expect(onChange).toHaveBeenCalledWith('12')
  })

  it('applies custom className', () => {
    const customClassName = 'custom-class'
    const { getByTestId } = render(
      <VerifyCodeInput className={customClassName} testID="verify-code" />
    )
    const container = getByTestId('verify-code')
    expect(container.props.className).toContain(customClassName)
  })

  it('initializes with value', () => {
    const { getAllByTestId } = render(
      <VerifyCodeInput value="123456" testID="verify-code" />
    )
    const inputs = getAllByTestId(/verify-code-input-\d/)
    expect(inputs[0].props.value).toBe('1')
    expect(inputs[1].props.value).toBe('2')
    expect(inputs[2].props.value).toBe('3')
    expect(inputs[3].props.value).toBe('4')
    expect(inputs[4].props.value).toBe('5')
    expect(inputs[5].props.value).toBe('6')
  })

  it('handles paste event', () => {
    const onChange = jest.fn()
    const { getAllByTestId } = render(
      <VerifyCodeInput onChange={onChange} testID="verify-code" />
    )
    const inputs = getAllByTestId(/verify-code-input-\d/)

    // Simulate paste event by using changeText
    fireEvent.changeText(inputs[0], '123456')
    expect(onChange).toHaveBeenCalledWith('123456')
  })

  it('handles backspace key', () => {
    const { getAllByTestId } = render(
      <VerifyCodeInput value="123456" testID="verify-code" />
    )
    const inputs = getAllByTestId(/verify-code-input-\d/)

    // Focus last input using fireEvent
    fireEvent(inputs[5], 'focus')

    // Simulate backspace
    fireEvent(inputs[5], 'keyPress', { nativeEvent: { key: 'Backspace' } })
    expect(inputs[4].props.value).toBe('5')
  })
})
