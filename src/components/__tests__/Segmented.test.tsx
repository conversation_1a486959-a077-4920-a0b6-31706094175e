import React from 'react'
import { FontAwesome6 } from '@expo/vector-icons'
import { act, fireEvent, render } from '@testing-library/react-native'

import { Segmented } from '../Segmented'

const options = [
  { label: 'Email Login', value: 'email', icon: 'envelope' as const },
  { label: 'Phone Login', value: 'phone', icon: 'mobile-screen' as const }
]

async function wait() {
  await act(async () => {
    // Wait for any state updates to complete
    await new Promise(resolve => setTimeout(resolve, 0))
  })
}

describe('Segmented', () => {
  it('renders all options', async () => {
    const { getByText } = render(<Segmented options={options} value="email" />)
    await wait()
    expect(getByText('Email Login')).toBeTruthy()
    expect(getByText('Phone Login')).toBeTruthy()
  })

  it('calls onChange when option is pressed', async () => {
    const onChange = jest.fn()
    const { getByText } = render(
      <Segmented options={options} value="email" onChange={onChange} />
    )
    await act(async () => {
      fireEvent.press(getByText('Phone Login'))
      // Wait for any state updates to complete
      await new Promise(resolve => setTimeout(resolve, 0))
    })
    expect(onChange).toHaveBeenCalledWith('phone')
  })

  it('applies correct styles for active and inactive options', async () => {
    const { getByTestId, getAllByRole } = render(
      <Segmented options={options} value="email" testID="segmented" />
    )
    await wait()
    const container = getByTestId('segmented')
    const buttons = getAllByRole('button')
    const activeButton = buttons[0]
    const inactiveButton = buttons[1]

    // Check container styles
    expect(container.props.className).toContain('bg-[#f3f4f6]')
    expect(container.props.className).toContain('rounded-default')
    expect(container.props.horizontal).toBe(true)
    expect(container.props.showsHorizontalScrollIndicator).toBe(false)

    // Check active button styles
    expect(activeButton.props.className).toContain('bg-white')
    expect(activeButton.props.className).toContain('text-primary')
    expect(activeButton.props.style).toHaveProperty('boxShadow')

    // Check inactive button styles
    expect(inactiveButton.props.className).toContain('bg-transparent')
  })

  it('renders with custom className', async () => {
    const customClassName = 'custom-class'
    const { getByTestId } = render(
      <Segmented
        options={options}
        value="email"
        className={customClassName}
        testID="segmented-container"
      />
    )
    await wait()
    const container = getByTestId('segmented-container')
    expect(container.props.className).toContain(customClassName)
  })

  it('renders icons correctly', async () => {
    const { UNSAFE_getAllByType } = render(
      <Segmented options={options} value="email" testID="segmented" />
    )
    await wait()
    // Find all FontAwesome icons
    const icons = UNSAFE_getAllByType(FontAwesome6)
    const iconNames = icons.map(icon => icon.props.name)
    expect(iconNames).toContain('envelope')
    expect(iconNames).toContain('mobile-screen')
  })
})
