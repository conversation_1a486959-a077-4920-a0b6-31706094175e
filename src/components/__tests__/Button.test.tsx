describe('Button Component Props', () => {
  it('should accept required and optional props', () => {
    const validProps = {
      label: 'Test Button',
      iconName: 'save-outline',
      variant: 'primary',
      onPress: jest.fn(),
      disabled: false,
      style: { marginBottom: 10 }
    }

    expect(validProps).toEqual({
      label: expect.any(String),
      iconName: expect.any(String),
      variant: expect.stringMatching(/^(primary|secondary|danger)$/),
      onPress: expect.any(Function),
      disabled: expect.any(Boolean),
      style: expect.any(Object)
    })
  })
})
