import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { Steps } from '../Steps'

describe('Steps', () => {
  const mockItems = [
    { title: 'Step 1' },
    { title: 'Step 2' },
    { title: 'Step 3' }
  ]

  it('renders correctly with default props', () => {
    const { getByText } = render(<Steps items={mockItems} />)

    // Check if all step titles are rendered
    mockItems.forEach(item => {
      expect(getByText(item.title)).toBeTruthy()
    })

    // Check if all step numbers are rendered
    mockItems.forEach((_, index) => {
      expect(getByText((index + 1).toString())).toBeTruthy()
    })
  })

  it('renders correctly with vertical direction', () => {
    const { getByText } = render(
      <Steps items={mockItems} direction="vertical" />
    )

    // Check if all step titles are rendered
    mockItems.forEach(item => {
      expect(getByText(item.title)).toBeTruthy()
    })
  })

  it('handles step changes correctly', () => {
    const onChangeMock = jest.fn()
    const { getByText } = render(
      <Steps items={mockItems} onChange={onChangeMock} />
    )

    // Click on the second step
    fireEvent.press(getByText('Step 2'))

    // Verify that onChange callback is called with index 1 (second step)
    expect(onChangeMock).toHaveBeenCalledWith(1)
  })

  it('applies correct styles for active and completed steps', () => {
    const { getByTestId } = render(<Steps items={mockItems} current={1} />)

    // Get the first step number element (should be completed)
    const step1 = getByTestId('step-1')
    expect(step1.props.className).toContain('bg-success')

    // Get the second step number element (should be active)
    const step2 = getByTestId('step-2')
    expect(step2.props.className).toContain('bg-primary')

    // Get the third step number element (should be incomplete)
    const step3 = getByTestId('step-3')
    expect(step3.props.className).toContain('bg-light')
  })

  it('renders with custom className', () => {
    const customClassName = 'custom-class'
    const { getByTestId } = render(
      <Steps
        items={mockItems}
        className={customClassName}
        testID="steps-container"
      />
    )

    // Verify that custom className is applied
    const container = getByTestId('steps-container')
    expect(container.props.className).toContain(customClassName)
  })
})
