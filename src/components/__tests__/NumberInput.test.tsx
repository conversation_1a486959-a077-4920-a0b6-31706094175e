import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { NumberInput } from '../NumberInput'

describe('NumberInput', () => {
  it('renders correctly', () => {
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" />
    )
    expect(getByPlaceholderText('Enter number')).toBeTruthy()
  })

  it('handles basic number input', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" onChange={onChange} />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '123')
    expect(onChange).toHaveBeenCalledWith(123)
  })

  it('handles decimal numbers', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput
        placeholder="Enter number"
        decimalPlaces={2}
        onChange={onChange}
      />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '123.45')
    expect(onChange).toHaveBeenCalledWith(123.45)
  })

  it('limits decimal places', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput
        placeholder="Enter number"
        decimalPlaces={2}
        onChange={onChange}
      />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '123.456')
    expect(onChange).toHaveBeenCalledWith(123.45)
  })

  it('handles negative numbers when allowed', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput
        placeholder="Enter number"
        allowNegative
        onChange={onChange}
      />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '-123')
    expect(onChange).toHaveBeenCalledWith(-123)
  })

  it('removes negative sign when not allowed', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput
        placeholder="Enter number"
        allowNegative={false}
        onChange={onChange}
      />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '-123')
    expect(onChange).toHaveBeenCalledWith(123)
  })

  it('enforces minimum value', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" min={0} onChange={onChange} />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '-123')
    expect(onChange).toHaveBeenCalledWith(0)
  })

  it('enforces maximum value', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" max={100} onChange={onChange} />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '123')
    expect(onChange).toHaveBeenCalledWith(100)
  })

  it('handles empty input', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" onChange={onChange} />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '')
    expect(onChange).toHaveBeenCalledWith(null)
  })

  it('removes non-numeric characters', () => {
    const onChange = jest.fn()
    const { getByPlaceholderText } = render(
      <NumberInput placeholder="Enter number" onChange={onChange} />
    )
    const input = getByPlaceholderText('Enter number')
    fireEvent.changeText(input, '123abc')
    expect(onChange).toHaveBeenCalledWith(123)
  })
})
