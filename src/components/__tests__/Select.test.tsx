import React from 'react'
import { fireEvent, render, waitFor } from '@testing-library/react-native'

import { Select } from '../Select'

// Mock the useDict hook
jest.mock('@/store/dict', () => ({
  useDict: () => ({
    getDictItems: jest.fn(() =>
      Promise.resolve([
        { label: 'Option 1', code: 'opt1' },
        { label: 'Option 2', code: 'opt2' }
      ])
    )
  })
}))

// Mock the useSafeAreaInsets hook
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({
    top: 44,
    bottom: 34,
    left: 0,
    right: 0
  })
}))

describe('Select', () => {
  it('renders with options and changes value', async () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    await waitFor(() => {
      expect(getByTestId('select-picker')).toBeTruthy()
    })
  })

  it('renders as disabled when disabled prop is true', async () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        disabled={true}
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    const selectButton = getByTestId('select-picker')
    expect(selectButton).toBeTruthy()

    // The button should have disabled styling (we can check for the presence of disabled class or style)
    // Since we're using className for styling, we can verify the component renders without errors
    expect(selectButton.props.onPress).toBeUndefined()
  })

  it('does not open modal when disabled', async () => {
    const onChange = jest.fn()
    const { getByTestId, queryByText } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        disabled={true}
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    const selectButton = getByTestId('select-picker')

    // Try to press the disabled select
    fireEvent.press(selectButton)

    // Modal should not appear
    await waitFor(() => {
      expect(queryByText('Select an option')).toBeNull()
    })
  })

  it('opens modal when not disabled', async () => {
    const onChange = jest.fn()
    const { getByTestId, getByText } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        disabled={false}
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    const selectButton = getByTestId('select-picker')

    // Press the enabled select
    fireEvent.press(selectButton)

    // Modal should appear
    await waitFor(() => {
      expect(getByText('Select an option')).toBeTruthy()
    })
  })

  it('shows placeholder when no value is selected', () => {
    const { getByText } = render(
      <Select
        value=""
        placeholder="Choose an option"
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    expect(getByText('Choose an option')).toBeTruthy()
  })

  it('shows selected value when value is provided', () => {
    const { getByText } = render(
      <Select
        value="a"
        placeholder="Choose an option"
        options={[
          { label: 'Option A', value: 'a' },
          { label: 'Option B', value: 'b' }
        ]}
      />
    )

    expect(getByText('Option A')).toBeTruthy()
  })

  it('works with dictType instead of options', async () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        dictType="GENDER"
      />
    )

    await waitFor(() => {
      expect(getByTestId('select-picker')).toBeTruthy()
    })
  })
})
