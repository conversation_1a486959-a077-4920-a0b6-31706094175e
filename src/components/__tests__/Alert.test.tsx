import React from 'react'
import { act, render } from '@testing-library/react-native'

import Alert from '../Alert'

describe('Alert', () => {
  it('renders correctly with default props', () => {
    const { getByText } = render(<Alert message="Default message" />)
    expect(getByText('Default message')).toBeTruthy()
  })

  it('renders with title and message', () => {
    const { getByText } = render(
      <Alert title="Alert Title" message="Alert message" />
    )

    expect(getByText('Alert Title')).toBeTruthy()
    expect(getByText('Alert message')).toBeTruthy()
  })

  it('renders with different types', () => {
    const { getByTestId, rerender } = render(
      <Alert type="info" message="Info message" testID="info-alert" />
    )
    const infoAlert = getByTestId('info-alert')
    expect(infoAlert.props.className).toContain('bg-info-light')

    rerender(
      <Alert type="tenant" message="Tenant message" testID="tenant-alert" />
    )
    const tenantAlert = getByTestId('tenant-alert')
    expect(tenantAlert.props.className).toContain('bg-tenant-light')
  })

  it('renders with icon', async () => {
    const { getByTestId } = render(
      <Alert
        leftIcon="info-circle"
        message="Message with icon"
        testID="alert-with-icon"
      />
    )

    await act(async () => {
      const alert = getByTestId('alert-with-icon')
      expect(alert.props.className).toContain('flex-row')
    })
  })

  it('applies custom className to title and message', () => {
    const { getByText } = render(
      <Alert
        title="Custom Title"
        titleClassName="custom-title-class"
        message="Custom Message"
        messageClassName="custom-message-class"
      />
    )

    const title = getByText('Custom Title')
    expect(title.props.className).toContain('custom-title-class')

    const message = getByText('Custom Message')
    expect(message.props.className).toContain('custom-message-class')
  })

  it('renders with custom icon component', async () => {
    const CustomIcon = () => <div data-testid="custom-icon">Custom Icon</div>
    const { getByTestId } = render(
      <Alert
        leftIcon={<CustomIcon />}
        message="Message with custom icon"
        testID="alert-with-custom-icon"
      />
    )

    await act(async () => {
      const alert = getByTestId('alert-with-custom-icon')
      expect(alert).toBeTruthy()
    })
  })
})
