import React from 'react'
import { render, waitFor } from '@testing-library/react-native'

import { PasswordInput } from '../PasswordInput'

describe('PasswordInput', () => {
  it('renders and is secure by default', async () => {
    const { getByPlaceholderText } = render(
      <PasswordInput placeholder="Password" value="123" />
    )

    await waitFor(() => {
      const input = getByPlaceholderText('Password')
      expect(input.props.secureTextEntry).toBe(true)
    })
  })
})
