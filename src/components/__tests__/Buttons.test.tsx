describe('Buttons Component Props', () => {
  it('should accept required and optional props', () => {
    const validProps = {
      onSave: jest.fn(),
      onLogout: jest.fn(),
      onDelete: jest.fn(),
      isSaving: false
    }

    expect(validProps).toEqual({
      onSave: expect.any(Function),
      onLogout: expect.any(Function),
      onDelete: expect.any(Function),
      isSaving: expect.any(Boolean)
    })
  })
})
