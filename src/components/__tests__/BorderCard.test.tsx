import React from 'react'
import { Text } from 'react-native'
import { render } from '@testing-library/react-native'

import { Colors } from '@/theme/colors'

import { BorderCard } from '../BorderCard'

/**
 * BorderCard Component Test Suite
 * Testing various functionalities and edge cases of the BorderCard component
 */
describe('BorderCard Component', () => {
  // Test default props
  it('should render with default props', () => {
    const { getByTestId } = render(
      <BorderCard testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderLeftWidth).toBe(4)
    expect(styles.borderLeftColor).toBe(Colors.primary)
  })

  // Test different border positions
  it('should render with right border position', () => {
    const { getByTestId } = render(
      <BorderCard position="right" testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderRightWidth).toBe(4)
    expect(styles.borderRightColor).toBe(Colors.primary)
  })

  it('should render with top border position', () => {
    const { getByTestId } = render(
      <BorderCard position="top" testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderTopWidth).toBe(4)
    expect(styles.borderTopColor).toBe(Colors.primary)
  })

  it('should render with bottom border position', () => {
    const { getByTestId } = render(
      <BorderCard position="bottom" testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderBottomWidth).toBe(4)
    expect(styles.borderBottomColor).toBe(Colors.primary)
  })

  // Test custom color
  it('should apply custom border color', () => {
    const customColor = '#FF0000'
    const { getByTestId } = render(
      <BorderCard color={customColor} testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderLeftColor).toBe(customColor)
  })

  // Test custom className
  it('should apply custom className', () => {
    const { getByTestId } = render(
      <BorderCard className="custom-class" testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    expect(card.props.className).toContain('custom-class')
  })

  // Test children rendering
  it('should render children correctly', () => {
    const { getByText } = render(
      <BorderCard>
        <Text>Test Content</Text>
      </BorderCard>
    )
    expect(getByText('Test Content')).toBeTruthy()
  })

  // Test default border color for non-active borders
  it('should apply default border color to non-active borders', () => {
    const { getByTestId } = render(
      <BorderCard position="left" testID="border-card">
        <Text>Test Content</Text>
      </BorderCard>
    )
    const card = getByTestId('border-card')
    const styles = card.props.style[0]
    expect(styles.borderRightColor).toBe('#eee')
    expect(styles.borderTopColor).toBe('#eee')
    expect(styles.borderBottomColor).toBe('#eee')
  })
})
