import { render, waitFor } from '@testing-library/react-native'
import React from 'react'

import { Select } from '../Select'

describe('Select', () => {
  it('renders options and changes value', async () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Select
        value=""
        testId="select-picker"
        onChange={onChange}
        options={[
          { label: 'A', value: 'a' },
          { label: 'B', value: 'b' }
        ]}
      />
    )

    await waitFor(() => {
      expect(getByTestId('select-picker')).toBeTruthy()
    })
  })
})
