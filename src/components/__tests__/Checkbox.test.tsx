import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { Checkbox } from '../Checkbox'

describe('Checkbox', () => {
  it('renders correctly in unchecked state', () => {
    const { getByText } = render(<Checkbox>Test Label</Checkbox>)
    const label = getByText('Test Label')
    expect(label).toBeTruthy()
  })

  it('renders correctly in checked state', () => {
    const { getByText } = render(<Checkbox value>Test Label</Checkbox>)
    const checkmark = getByText('✓')
    expect(checkmark).toBeTruthy()
  })

  it('calls onChange when pressed', () => {
    const onChange = jest.fn()
    const { getByText } = render(
      <Checkbox onChange={onChange}>Test Label</Checkbox>
    )
    fireEvent.press(getByText('Test Label'))
    expect(onChange).toHaveBeenCalledWith(true)
  })

  it('applies correct styles when checked', () => {
    const { getByTestId } = render(
      <Checkbox value testID="checkbox">
        Test Label
      </Checkbox>
    )
    const checkbox = getByTestId('checkbox')
    const checkboxView = checkbox.children[0]
    expect(checkboxView.props.className).toContain('border-primary')
    expect(checkboxView.props.className).toContain('bg-primary')
  })

  it('applies correct styles when unchecked', () => {
    const { getByTestId } = render(
      <Checkbox testID="checkbox">Test Label</Checkbox>
    )
    const checkbox = getByTestId('checkbox')
    const checkboxView = checkbox.children[0]
    expect(checkboxView.props.className).toContain('border-gray')
    expect(checkboxView.props.className).toContain('bg-white')
  })

  it('applies disabled styles when disabled', () => {
    const { getByTestId } = render(
      <Checkbox disabled testID="checkbox">
        Test Label
      </Checkbox>
    )
    const checkbox = getByTestId('checkbox')
    expect(checkbox.props.className).toContain('opacity-50')
  })

  it('does not call onChange when disabled', () => {
    const onChange = jest.fn()
    const { getByText } = render(
      <Checkbox disabled onChange={onChange}>
        Test Label
      </Checkbox>
    )
    fireEvent.press(getByText('Test Label'))
    expect(onChange).not.toHaveBeenCalled()
  })

  it('applies custom className', () => {
    const customClassName = 'custom-class'
    const { getByTestId } = render(
      <Checkbox className={customClassName} testID="checkbox">
        Test Label
      </Checkbox>
    )
    const checkbox = getByTestId('checkbox')
    expect(checkbox.props.className).toContain(customClassName)
  })

  it('applies custom labelClassName', () => {
    const customLabelClassName = 'custom-label-class'
    const { getByText } = render(
      <Checkbox labelClassName={customLabelClassName}>Test Label</Checkbox>
    )
    const label = getByText('Test Label')
    expect(label.props.className).toContain(customLabelClassName)
  })
})
