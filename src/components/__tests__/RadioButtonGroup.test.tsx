import React from 'react'
import { View } from 'react-native'
import { fireEvent, render } from '@testing-library/react-native'

import { RadioButtonGroup } from '../RadioButtonGroup'

describe('RadioButtonGroup', () => {
  const mockItems = [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
    { label: 'Option 3', value: '3' }
  ]

  it('renders correctly with basic props', () => {
    const { getByText } = render(
      <RadioButtonGroup value="1" onChange={() => {}} items={mockItems} />
    )

    expect(getByText('Option 1')).toBeTruthy()
    expect(getByText('Option 2')).toBeTruthy()
    expect(getByText('Option 3')).toBeTruthy()
  })

  it('calls onChange when an option is pressed', () => {
    const onChangeMock = jest.fn()
    const { getByText } = render(
      <RadioButtonGroup value="1" onChange={onChangeMock} items={mockItems} />
    )

    fireEvent.press(getByText('Option 2'))
    expect(onChangeMock).toHaveBeenCalledWith('2')
  })

  it('applies custom styles for active and inactive states', () => {
    const customItems = [
      {
        label: 'Custom Option',
        value: '1',
        activeColor: '#FF0000',
        activeBgColor: '#FFE5E5'
      }
    ]

    const { getByTestId } = render(
      <RadioButtonGroup value="1" onChange={() => {}} items={customItems} />
    )

    const pressableElement = getByTestId('radio-1')
    expect(pressableElement.props.style).toMatchObject({
      borderColor: '#FF0000',
      backgroundColor: '#FFE5E5'
    })
  })

  it('renders rightNode when provided', () => {
    const itemsWithRightNode = [
      {
        label: 'Option with Node',
        value: '1',
        rightNode: (active: boolean) => (
          <View testID="right-node">{active ? 'Active' : 'Inactive'}</View>
        )
      }
    ]

    const { getByTestId } = render(
      <RadioButtonGroup
        value="1"
        onChange={() => {}}
        items={itemsWithRightNode}
      />
    )

    expect(getByTestId('right-node')).toBeTruthy()
  })

  it('applies custom className to container', () => {
    const { getByTestId } = render(
      <RadioButtonGroup
        value="1"
        onChange={() => {}}
        items={mockItems}
        className="custom-class"
        testID="radio-group"
      />
    )

    const container = getByTestId('radio-group')
    expect(container.props.className).toContain('custom-class')
  })

  it('handles ScrollView props correctly', () => {
    const { getByTestId } = render(
      <RadioButtonGroup
        value="1"
        onChange={() => {}}
        items={mockItems}
        showsHorizontalScrollIndicator={false}
        testID="radio-group"
      />
    )

    const container = getByTestId('radio-group')
    expect(container.props.showsHorizontalScrollIndicator).toBe(false)
  })
})
