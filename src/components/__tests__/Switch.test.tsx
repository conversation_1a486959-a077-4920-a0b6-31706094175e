import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { Switch } from '../Switch'

describe('Switch', () => {
  it('renders correctly with default props', () => {
    const { getByTestId } = render(<Switch testID="switch" />)
    const switchElement = getByTestId('switch')
    expect(switchElement).toBeTruthy()
  })

  it('renders in off state by default', () => {
    const { getByTestId } = render(<Switch testID="switch" />)
    const switchElement = getByTestId('switch')
    expect(switchElement.props.className).toContain('bg-[#ccc]')
  })

  it('renders in on state when value is true', () => {
    const { getByTestId } = render(<Switch testID="switch" value={true} />)
    const switchElement = getByTestId('switch')
    expect(switchElement.props.className).toContain('bg-primary')
  })

  it('calls onChange when pressed', () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Switch testID="switch" onChange={onChange} />
    )
    const switchElement = getByTestId('switch')
    fireEvent.press(switchElement)
    expect(onChange).toHaveBeenCalledWith(true)
  })

  it('does not call onChange when disabled', () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Switch testID="switch" onChange={onChange} disabled />
    )
    const switchElement = getByTestId('switch')
    fireEvent.press(switchElement)
    expect(onChange).not.toHaveBeenCalled()
  })

  it('applies custom className', () => {
    const { getByTestId } = render(
      <Switch testID="switch" className="custom-class" />
    )
    const switchElement = getByTestId('switch')
    expect(switchElement.props.className).toContain('custom-class')
  })
})
