describe('Personal Component Props', () => {
  it('should accept required and optional props', () => {
    const validProps = {
      name: '<PERSON>',
      role: 'Property Owner',
      verified: true,
      avatar: 'https://example.com/avatar.jpg',
      onChangePhoto: jest.fn(),
      onBackPress: jest.fn()
    }

    expect(validProps).toEqual({
      name: expect.any(String),
      role: expect.any(String),
      verified: expect.any(<PERSON><PERSON><PERSON>),
      avatar: expect.any(String),
      onChangePhoto: expect.any(Function),
      onBackPress: expect.any(Function)
    })
  })
})
