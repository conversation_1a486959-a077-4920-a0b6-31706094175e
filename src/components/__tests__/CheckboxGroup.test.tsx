import React from 'react'
import { act, fireEvent, render } from '@testing-library/react-native'

import { CheckboxGroup } from '../CheckboxGroup'

const options = [
  { label: 'A', value: 'a' },
  { label: 'B', value: 'b' }
]

describe('CheckboxGroup', () => {
  it('renders all options', () => {
    const { getByText } = render(<CheckboxGroup options={options} value={[]} />)
    expect(getByText('A')).toBeTruthy()
    expect(getByText('B')).toBeTruthy()
  })

  it('calls onChange when option is pressed', () => {
    const onChange = jest.fn()
    const { getByText } = render(
      <CheckboxGroup options={options} value={[]} onChange={onChange} />
    )
    fireEvent.press(getByText('A'))
    expect(onChange).toHaveBeenCalledWith(['a'])
  })

  it('toggles checked state', () => {
    let value: string[] = []
    const onChange = jest.fn((v: string[]) => {
      value = v
    })

    const { getByText, rerender } = render(
      <CheckboxGroup options={options} value={value} onChange={onChange} />
    )

    act(() => {
      fireEvent.press(getByText('A'))
    })

    rerender(
      <CheckboxGroup options={options} value={value} onChange={onChange} />
    )

    expect(value).toContain('a')

    act(() => {
      fireEvent.press(getByText('A'))
    })

    rerender(
      <CheckboxGroup options={options} value={value} onChange={onChange} />
    )

    expect(value).not.toContain('a')
  })
})
