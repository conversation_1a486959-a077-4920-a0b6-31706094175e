import React from 'react'
import { act, fireEvent, render } from '@testing-library/react-native'

import { DatePicker } from '../DatePicker'

// Mock DateTimePicker
jest.mock('@react-native-community/datetimepicker', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(({ onChange }) => {
    // Simulate date selection after a short delay
    setTimeout(() => {
      onChange({ type: 'set' }, new Date('2024-01-01'))
    }, 0)
    return null
  })
}))

describe('DatePicker', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders correctly with placeholder', () => {
    const { getByText } = render(<DatePicker />)
    expect(getByText('Select date')).toBeTruthy()
  })

  it('renders correctly with value', () => {
    const date = new Date('2024-01-01')
    const { getByText } = render(<DatePicker value={date} />)
    expect(getByText('2024/01/01')).toBeTruthy()
  })

  it('calls onChange when date is selected', async () => {
    const onChange = jest.fn()
    const { getByTestId } = render(<DatePicker onChange={onChange} />)

    // First press to show the picker
    fireEvent.press(getByTestId('date-picker-container'))

    // Wait for the mock to trigger onChange
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(onChange).toHaveBeenCalledWith(expect.any(Date))
  })

  it('handles clear button correctly', () => {
    const onChange = jest.fn()
    const date = new Date('2024-01-01')
    const { getByTestId } = render(
      <DatePicker value={date} onChange={onChange} clearable />
    )

    // Create a mock event object
    const mockEvent = {
      stopPropagation: jest.fn()
    }

    // Fire the press event with our mock event
    fireEvent(getByTestId('clear-button'), 'press', mockEvent)

    // Verify that stopPropagation was called
    expect(mockEvent.stopPropagation).toHaveBeenCalled()
    // Verify that onChange was called with null
    expect(onChange).toHaveBeenCalledWith(null)
  })

  it('disables interaction when disabled prop is true', () => {
    const onChange = jest.fn()
    const { getByTestId } = render(<DatePicker disabled onChange={onChange} />)

    fireEvent.press(getByTestId('date-picker-container'))
    expect(onChange).not.toHaveBeenCalled()
  })

  it('respects minDate and maxDate constraints', () => {
    const minDate = new Date('2024-01-01')
    const maxDate = new Date('2024-12-31')
    const { getByTestId } = render(
      <DatePicker minDate={minDate} maxDate={maxDate} />
    )

    // Press to show the picker
    fireEvent.press(getByTestId('date-picker-container'))

    // Check if DateTimePicker was rendered with correct props
    const mockDateTimePicker = jest.requireMock(
      '@react-native-community/datetimepicker'
    ).default
    const lastCall = mockDateTimePicker.mock.calls[0][0]
    expect(lastCall.minimumDate).toEqual(minDate)
    expect(lastCall.maximumDate).toEqual(maxDate)
  })

  it('formats date according to format prop', () => {
    const date = new Date('2024-01-01')
    const { getByText } = render(
      <DatePicker value={date} format="dd/MM/yyyy" />
    )
    expect(getByText('01/01/2024')).toBeTruthy()
  })
})
