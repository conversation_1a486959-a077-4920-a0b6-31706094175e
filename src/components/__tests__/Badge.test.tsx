import React from 'react'
import { Text } from 'react-native'
import { render, screen } from '@testing-library/react-native'

import { Badge } from '../Badge'

/**
 * Badge Component Test Suite
 * Testing various functionalities and edge cases of the Badge component
 */
describe('Badge Component', () => {
  // Test basic number display
  it('should display number correctly', () => {
    render(<Badge number={5} trigger={<Text>Test</Text>} />)
    expect(screen.getByText('5')).toBeTruthy()
  })

  // Test maximum value limit
  it('should display max+ when number exceeds maximum', () => {
    render(<Badge number={100} max={99} trigger={<Text>Test</Text>} />)
    expect(screen.getByText('99+')).toBeTruthy()
  })

  // Test zero value handling
  it('should not display badge when number is 0', () => {
    const { queryByText } = render(
      <Badge number={0} trigger={<Text>Test</Text>} />
    )
    expect(queryByText('0')).toBeNull()
  })

  // Test custom styling
  it('should apply custom styles correctly', () => {
    const { getByTestId } = render(
      <Badge
        number={5}
        trigger={<Text>Test</Text>}
        className="custom-class"
        testID="badge-container"
      />
    )
    const container = getByTestId('badge-container')
    expect(container.props.className).toContain('custom-class')
  })

  // Test edge cases
  it('should handle negative numbers correctly', () => {
    const { queryByText } = render(
      <Badge number={-1} trigger={<Text>Test</Text>} />
    )
    expect(queryByText('-1')).toBeNull()
  })

  // Test custom maximum value
  it('should support custom maximum value', () => {
    render(<Badge number={10} max={5} trigger={<Text>Test</Text>} />)
    expect(screen.getByText('5+')).toBeTruthy()
  })
})
