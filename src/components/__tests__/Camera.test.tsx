import React from 'react'
import { render } from '@testing-library/react-native'
import * as ImagePicker from 'expo-image-picker'

import { Camera } from '../Camera'

// Mock expo-image-picker
jest.mock('expo-image-picker', () => ({
  requestCameraPermissionsAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  MediaTypeOptions: {
    Images: 'Images'
  }
}))

// Mock expo-image
jest.mock('expo-image', () => ({
  Image: 'Image'
}))

// Mock useAppTheme hook
jest.mock('@/utils/useAppTheme', () => ({
  useAppTheme: () => ({
    theme: {
      colors: {
        text: '#000000',
        background: '#ffffff'
      }
    },
    themeContext: 'light'
  })
}))

// Mock Icon component
jest.mock('@/components/Icon', () => ({
  Icon: 'Icon'
}))

describe('Camera', () => {
  const mockOnPhotoTaken = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders camera interface correctly', () => {
    const { getByText } = render(
      <Camera onPhotoTaken={mockOnPhotoTaken} onClose={mockOnClose} />
    )

    expect(getByText('Take Photo')).toBeTruthy()
    expect(getByText('Tap the button below to take a photo')).toBeTruthy()
  })

  it('renders with correct props', () => {
    const { getByText } = render(
      <Camera onPhotoTaken={mockOnPhotoTaken} onClose={mockOnClose} />
    )

    expect(getByText('Take Photo')).toBeTruthy()
  })

  it('handles permission request function', () => {
    const mockRequestPermission = jest
      .fn()
      .mockResolvedValue({ status: 'granted' })
    ;(
      ImagePicker.requestCameraPermissionsAsync as jest.Mock
    ).mockImplementation(mockRequestPermission)

    expect(mockRequestPermission).toBeDefined()
  })

  it('handles camera launch function', () => {
    const mockLaunchCamera = jest.fn().mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'test-image-uri' }]
    })
    ;(ImagePicker.launchCameraAsync as jest.Mock).mockImplementation(
      mockLaunchCamera
    )

    expect(mockLaunchCamera).toBeDefined()
  })
})
