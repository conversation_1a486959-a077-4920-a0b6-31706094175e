import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { Star } from '../Star'

jest.mock('@expo/vector-icons', () => {
  return new Proxy(
    {},
    {
      get: () => () => null
    }
  )
})

describe('Star Component', () => {
  it('renders correctly with default props', () => {
    const { getAllByTestId } = render(<Star value={3} />)
    const stars = getAllByTestId(/^star-\d$/)
    expect(stars).toHaveLength(5)
  })

  it('renders with custom size and gap', () => {
    const { getAllByTestId } = render(<Star value={3} size={24} gap={4} />)
    const stars = getAllByTestId(/^star-\d$/)
    expect(stars).toHaveLength(5)
  })

  it('handles onChange correctly', () => {
    const onChange = jest.fn()
    const { getByTestId } = render(<Star value={0} onChange={onChange} />)

    fireEvent.press(getByTestId('star-2'))
    expect(onChange).toHaveBeenCalledWith(3)
  })

  it('handles half stars correctly when allowHalf is true', () => {
    const { getAllByTestId } = render(<Star value={3.5} allowHalf />)
    const stars = getAllByTestId(/^star-\d$/)
    const icons = stars.map(star => star.children[0])

    // First 3 stars should be solid
    expect(icons[0].props.solid).toBe(true)
    expect(icons[1].props.solid).toBe(true)
    expect(icons[2].props.solid).toBe(true)

    // 4th star should be half
    expect(icons[3].props.name).toBe('star-half-stroke')

    // Last star should be empty
    expect(icons[4].props.solid).toBe(false)
  })

  it('shows full stars for values between 3 and 3.4 when allowHalf is true', () => {
    const { getAllByTestId } = render(<Star value={3.4} allowHalf />)
    const stars = getAllByTestId(/^star-\d$/)
    const icons = stars.map(star => star.children[0])

    // First 3 stars should be solid
    expect(icons[0].props.solid).toBe(true)
    expect(icons[1].props.solid).toBe(true)
    expect(icons[2].props.solid).toBe(true)

    // 4th star should be empty (not half)
    expect(icons[3].props.name).toBe('star')
    expect(icons[3].props.solid).toBe(false)

    // Last star should be empty
    expect(icons[4].props.solid).toBe(false)
  })

  it('shows half star for values between 3.5 and 3.9 when allowHalf is true', () => {
    const { getAllByTestId } = render(<Star value={3.7} allowHalf />)
    const stars = getAllByTestId(/^star-\d$/)
    const icons = stars.map(star => star.children[0])

    // First 3 stars should be solid
    expect(icons[0].props.solid).toBe(true)
    expect(icons[1].props.solid).toBe(true)
    expect(icons[2].props.solid).toBe(true)

    // 4th star should be half
    expect(icons[3].props.name).toBe('star-half-stroke')

    // Last star should be empty
    expect(icons[4].props.solid).toBe(false)
  })

  it('rounds down when allowHalf is false', () => {
    const { getAllByTestId } = render(<Star value={3.7} />)
    const stars = getAllByTestId(/^star-\d$/)
    const icons = stars.map(star => star.children[0])

    // Only first 3 stars should be solid
    expect(icons[0].props.solid).toBe(true)
    expect(icons[1].props.solid).toBe(true)
    expect(icons[2].props.solid).toBe(true)
    expect(icons[3].props.solid).toBe(false)
    expect(icons[4].props.solid).toBe(false)
  })

  it('does not trigger onChange when disabled', () => {
    const onChange = jest.fn()
    const { getByTestId } = render(
      <Star value={0} onChange={onChange} disabled />
    )

    fireEvent.press(getByTestId('star-2'))
    expect(onChange).not.toHaveBeenCalled()
  })
})
