import React from 'react'
import { fireEvent, render } from '@testing-library/react-native'

import { CheckboxButtonGroup } from '../CheckboxButtonGroup'

const options = [
  { label: 'A', value: 'a' },
  { label: 'B', value: 'b' }
]

describe('CheckboxButtonGroup', () => {
  it('renders all options', () => {
    const { getByText } = render(
      <CheckboxButtonGroup options={options} value={[]} />
    )
    expect(getByText('A')).toBeTruthy()
    expect(getByText('B')).toBeTruthy()
  })

  it('calls onChange when option is pressed', () => {
    const onChange = jest.fn()
    const { getByText } = render(
      <CheckboxButtonGroup options={options} value={[]} onChange={onChange} />
    )
    fireEvent.press(getByText('A'))
    expect(onChange).toHaveBeenCalledWith(['a'])
  })
})
