import React from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import {
  act,
  fireEvent,
  render,
  renderHook,
  screen,
  waitFor
} from '@testing-library/react-native'
import type { ReactNode } from 'react'

import { Form } from '@/components/Form'
import { FormItem } from '@/components/FormItem'
import { Input } from '@/components/Input'
import { Switch } from '@/components/Switch'

interface TestFormValues {
  name: string
  email: string
  enabled: boolean
  threshold: number
}

const TestForm = ({ children }: { children: ReactNode }) => {
  const methods = useForm<TestFormValues>({
    defaultValues: {
      name: '',
      email: '',
      enabled: false,
      threshold: 0
    }
  })

  return (
    <FormProvider {...methods}>
      <Form form={methods}>{children}</Form>
    </FormProvider>
  )
}

describe('FormItem', () => {
  it('renders with label and input', () => {
    render(
      <TestForm>
        <FormItem name="name" label="Name">
          <Input placeholder="Enter name" />
        </FormItem>
      </TestForm>
    )

    expect(screen.getByText('Name')).toBeTruthy()
    expect(screen.getByPlaceholderText('Enter name')).toBeTruthy()
  })

  it.skip('renders with function children', async () => {
    render(
      <TestForm>
        <FormItem name="name">
          {(field: any) => (
            <Input {...field} placeholder="Enter name" testID="name-input" />
          )}
        </FormItem>
      </TestForm>
    )

    let input = screen.getByTestId('name-input')
    expect(input).toBeTruthy()
    await act(async () => {
      fireEvent.changeText(input, 'John')
    })
    input = screen.getByTestId('name-input')
    expect(input.props.value || input.props.defaultValue).toBe('John')
  })

  it.skip('renders with dependencies', async () => {
    render(
      <TestForm>
        <FormItem name="enabled" noStyle>
          <Switch testID="enabled-switch" />
        </FormItem>
        <FormItem name="threshold" dependencies={['enabled']}>
          {({ enabled }: { enabled: boolean }) =>
            enabled && (
              <Input placeholder="Enter threshold" testID="threshold-input" />
            )
          }
        </FormItem>
      </TestForm>
    )

    const switchInput = screen.getByTestId('enabled-switch')
    expect(switchInput).toBeTruthy()
    expect(screen.queryByTestId('threshold-input')).toBeNull()

    await act(async () => {
      fireEvent.press(switchInput)
    })

    await waitFor(() => {
      expect(screen.getByTestId('threshold-input')).toBeTruthy()
    })
  })

  it('shows error message when validation fails', async () => {
    render(
      <TestForm>
        <FormItem
          name="email"
          label="Email"
          rules={{
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Invalid email address'
            }
          }}
        >
          <Input placeholder="Enter email" testID="email-input" />
        </FormItem>
      </TestForm>
    )

    const input = screen.getByTestId('email-input')
    await act(async () => {
      fireEvent(input, 'blur')
    })
    expect(screen.getByText('Email is required')).toBeTruthy()

    await act(async () => {
      fireEvent.changeText(input, 'invalid-email')
      fireEvent(input, 'blur')
    })
    expect(screen.getByText('Invalid email address')).toBeTruthy()
  })

  it('renders with noStyle prop', () => {
    render(
      <TestForm>
        <FormItem name="name" noStyle>
          <Input placeholder="Enter name" testID="name-input" />
        </FormItem>
      </TestForm>
    )

    const input = screen.getByTestId('name-input')
    expect(input).toBeTruthy()
    expect(screen.queryByText('Name')).toBeNull()
  })

  it.skip('does not render container when dependencies children returns null', async () => {
    const { result } = renderHook(() => useForm<TestFormValues>())
    const { getByTestId, queryByTestId } = render(
      <FormProvider {...result.current}>
        <FormItem<TestFormValues>
          name="threshold"
          dependencies={['enabled']}
          children={({ enabled }: { enabled: boolean }) => {
            if (!enabled) return null
            return <Input testID="threshold-input" />
          }}
        />
      </FormProvider>
    )

    // Initial state: enabled is false, threshold-input should not be rendered
    expect(queryByTestId('threshold-input')).toBeNull()

    // Set enabled to true
    act(() => {
      result.current.setValue('enabled', true)
    })

    // Wait and verify threshold-input appears
    await waitFor(() => {
      expect(getByTestId('threshold-input')).toBeTruthy()
    })

    // Set enabled to false
    act(() => {
      result.current.setValue('enabled', false)
    })

    // Wait and verify threshold-input disappears
    await waitFor(() => {
      expect(queryByTestId('threshold-input')).toBeNull()
    })
  })
})
