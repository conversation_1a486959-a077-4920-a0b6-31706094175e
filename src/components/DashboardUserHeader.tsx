import { Image, Text, View } from 'react-native'

interface DashboardUserHeaderProps {
  title: string
  description: string
  userAvatar: string
}

export function DashboardUserHeader({
  title,
  description,
  userAvatar
}: DashboardUserHeaderProps) {
  console.log(userAvatar, 'userAvatar')
  return (
    <View className="flex-row">
      <View className="flex-1">
        <Text className="text-xl font-bold">{title}</Text>
        <Text className="text-sm text-gray">{description}</Text>
      </View>
      <Image
        source={{ uri: userAvatar }}
        className="h-[50px] w-[50px] rounded-full object-cover"
      />
    </View>
  )
}
