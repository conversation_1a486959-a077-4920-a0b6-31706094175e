import type { StyleProp, ViewStyle } from 'react-native'
import type { SymbolViewProps, SymbolWeight } from 'expo-symbols'
import { SymbolView } from 'expo-symbols'

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
  weight = 'regular'
}: {
  name: SymbolViewProps['name']
  size?: number
  color: string
  style?: StyleProp<ViewStyle>
  weight?: SymbolWeight
}) {
  return (
    <SymbolView
      weight={weight}
      tintColor={color}
      resizeMode="scaleAspectFit"
      name={name}
      style={[
        {
          width: size,
          height: size
        },
        style
      ]}
    />
  )
}
