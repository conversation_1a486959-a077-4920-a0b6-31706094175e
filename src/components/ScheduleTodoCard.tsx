import { useMemo } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { differenceInDays, format, getDate, getMonth } from 'date-fns'
import { Link } from 'expo-router'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import { CardWrapper } from './CardWrapper'

interface ScheduleTodoCardProps {
  projectId: string
  deadline: number
  title: string
  progress: string
}

export function ScheduleTodoCard({
  projectId,
  deadline,
  title,
  progress
}: ScheduleTodoCardProps) {
  const currentTime = new Date()
  const deadlineTime = new Date(deadline)
  const month = getMonth(deadlineTime)
  const monthName = format(deadlineTime, 'MMM')
  const date = getDate(deadlineTime)
  const diffDays = differenceInDays(deadlineTime, currentTime)

  const scheduleStyleByDiffdays = useMemo(() => {
    if (diffDays < 3) {
      return {
        border: 'border-l-warning',
        bg: 'bg-[#fff8f0]',
        text: 'text-warning'
      }
    }

    return {
      border: 'border-l-success',
      bg: 'bg-[#f0fff0]',
      text: 'text-success'
    }
  }, [diffDays])

  return (
    <CardWrapper borderLeftColor={scheduleStyleByDiffdays?.border}>
      <View className="h-[106px] flex-row items-center">
        <View
          className={classNames(
            'flex h-full w-[72px] items-center justify-center bg-[#fff8f0]',
            scheduleStyleByDiffdays.bg
          )}
        >
          <Text className="text-2xl font-bold">{date}</Text>
          <Text className="text-xs font-medium">{month}</Text>
          <Text className="mt-1 text-[10px]">Tomorrow</Text>
        </View>
        <View className="flex flex-1 justify-center pl-4">
          <Text className="text-lg font-bold">{title}</Text>
          <Text className="text-sm text-gray">{`${progress} Complete - Due ${monthName} ${date} `}</Text>
          <View className="flex flex-row items-center">
            <FontAwesome6
              name={diffDays > 3 ? 'check-circle' : 'exclamation-circle'}
              className={scheduleStyleByDiffdays.text}
            />
            <Text
              className={classNames(
                'ml-1 text-sm',
                scheduleStyleByDiffdays.text
              )}
            >
              {diffDays < 3 ? 'Priority: High' : 'On Schedule'}
            </Text>
          </View>
        </View>
        <View className="flex items-center p-4">
          <Link href={`/vendor/project-detail/${projectId}`}>
            <View className="h-9 w-9 flex-row items-center justify-center rounded-full bg-[#f2f2f2]">
              <FontAwesome6 name={'chevron-right'} color={Colors.gray} />
            </View>
          </Link>
        </View>
      </View>
    </CardWrapper>
  )
}
