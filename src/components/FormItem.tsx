import React, { cloneElement, isValidElement, useEffect, useRef } from 'react'
import type {
  ControllerFieldState,
  ControllerRenderProps,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormStateReturn
} from 'react-hook-form'
import { Controller, useFormContext } from 'react-hook-form'
import { Platform, Text, View } from 'react-native'
import clsx from 'clsx'
import type { ReactElement, ReactNode } from 'react'

import classNames from '@/utils/classname'

import { useFormContext as useCustomFormContext } from './Form'

type BaseFormItemProps<T extends FieldValues = FieldValues> = {
  layout?: 'vertical' | 'horizontal'
  label?: ReactNode
  extra?: ReactNode
  rules?: RegisterOptions<T, Path<T>>
  dependencies?: Path<T>[]
  onValuesChange?: (name: Path<T>, value: T[Path<T>]) => void
  className?: string
  name?: Path<T> // allow name in both modes for compatibility
  children?: ReactNode | ((values: { [K in Path<T>]: T[K] }) => ReactNode)
  noStyle?: boolean
}

export type FormItemProps<T extends FieldValues = FieldValues> =
  | (BaseFormItemProps<T> & {
      dependencies: Path<T>[]
      children: (values: { [K in Path<T>]: T[K] }) => ReactNode
    })
  | (BaseFormItemProps<T> & {
      dependencies?: undefined
      children: (field: ControllerRenderProps<T, Path<T>>) => ReactNode
    })
  | (BaseFormItemProps<T> & {
      children?: ReactNode
    })

export const FormItem = <T extends FieldValues>(props: FormItemProps<T>) => {
  const {
    layout: _itemLayout,
    label,
    extra,
    rules,
    dependencies,
    onValuesChange,
    className,
    name,
    children,
    noStyle
  } = props

  const { control, formState, trigger, watch } = useFormContext<T>()
  const {
    layout: _formLayout,
    labelWidth,
    registerFieldRef
  } = useCustomFormContext()
  const layout = _itemLayout || _formLayout
  const fieldRef = useRef<View>(null)

  const isTouched = name
    ? formState.touchedFields[name as keyof typeof formState.touchedFields]
    : false
  const isDirty = name
    ? formState.dirtyFields[name as keyof typeof formState.dirtyFields]
    : false

  useEffect(() => {
    if (Platform.OS !== 'web' && name) {
      // Ensure ref points to a native component
      registerFieldRef(name as string, fieldRef)
    }
  }, [name, registerFieldRef])
  // Watch dependencies if provided
  const dependencyValues = dependencies
    ? dependencies.reduce(
        (acc, dep) => ({
          ...acc,
          [dep]: watch(dep)
        }),
        {} as { [K in Path<T>]: T[K] }
      )
    : undefined

  // Early check: if in dependencies scenario and children returns empty, do not render the entire component
  if (
    typeof children === 'function' &&
    Array.isArray(dependencies) &&
    dependencies.length > 0 &&
    dependencyValues
  ) {
    const node = (children as (values: { [K in Path<T>]: T[K] }) => ReactNode)(
      dependencyValues
    )
    if (!node) {
      return null
    }
  }

  const renderField = ({
    field,
    // eslint-disable-next-line unused-imports/no-unused-vars
    fieldState
  }: {
    field: ControllerRenderProps<T, Path<T>>
    fieldState: ControllerFieldState
    formState: UseFormStateReturn<T>
  }) => {
    const wrapperClassName = layout === 'horizontal' ? '' : 'flex-1'
    if (
      typeof children === 'function' &&
      Array.isArray(dependencies) &&
      dependencies.length > 0 &&
      dependencyValues
    ) {
      // children is (deps) => ReactNode
      const node = (
        children as (values: { [K in Path<T>]: T[K] }) => ReactNode
      )(dependencyValues)
      if (!node) {
        return <></>
      }
      if (isValidElement(node)) {
        return (
          <View ref={fieldRef} className={wrapperClassName}>
            {cloneElement(node as ReactElement<Record<string, unknown>>, {
              name: field.name,
              value: field.value,
              onChange: (value?: T[Path<T>]) => {
                // Handle both undefined and defined values
                field.onChange(value)
                onValuesChange?.(name as Path<T>, value as T[Path<T>])
              },
              onBlur: () => {
                field.onBlur()
                trigger(name as Path<T>)
              }
            })}
          </View>
        )
      }
      return (
        <View ref={fieldRef} className={wrapperClassName}>
          {node}
        </View>
      )
    }
    if (
      typeof children === 'function' &&
      (!dependencies || dependencies.length === 0)
    ) {
      // children is (field) => ReactNode
      const node = (
        children as (field: ControllerRenderProps<T, Path<T>>) => ReactNode
      )({
        name: field.name,
        value: field.value,
        ref: field.ref,
        onChange: (value?: T[Path<T>]) => {
          // Handle both undefined and defined values
          field.onChange(value)
          onValuesChange?.(name as Path<T>, value as T[Path<T>])
        },
        onBlur: () => {
          field.onBlur()
          trigger(name as Path<T>)
        }
      })
      return (
        <View ref={fieldRef} className={wrapperClassName}>
          {node}
        </View>
      )
    }
    if (children && isValidElement(children)) {
      const child = cloneElement(
        children as ReactElement<Record<string, unknown>>,
        {
          name: field.name,
          value: field.value,
          onChange: (value?: T[Path<T>]) => {
            // Handle both undefined and defined values
            field.onChange(value)
            onValuesChange?.(name as Path<T>, value as T[Path<T>])
          },
          onBlur: () => {
            field.onBlur()
            trigger(name as Path<T>)
          }
        }
      )
      return (
        <View ref={fieldRef} className={wrapperClassName}>
          {child}
        </View>
      )
    }
    return (
      <View ref={fieldRef} className={wrapperClassName}>
        {typeof children !== 'function' ? children : null}
      </View>
    )
  }

  const errors = name ? formState.errors[name]?.message : undefined
  const errorMessages = errors
    ? Array.isArray(errors)
      ? errors
      : [errors]
    : []

  // Show errors when field is touched, modified, or form is submitted
  const shouldShowError =
    (isTouched || isDirty || formState.isSubmitted) && errorMessages.length > 0

  // Check if required
  const isRequired = !!(
    rules &&
    ((typeof rules.required === 'boolean' && rules.required) ||
      (typeof rules.required === 'object' && rules.required.value))
  )

  const labelLine = (label || extra) && (
    <View
      className={clsx(
        layout === 'horizontal' ? 'mr-4' : 'mb-2',
        extra && 'flex-row items-center justify-between'
      )}
      style={labelWidth ? { width: labelWidth } : undefined}
    >
      <Text>
        {label}
        {isRequired && <Text className="text-danger"> *</Text>}
      </Text>
      {extra}
    </View>
  )

  if (noStyle) {
    return (
      <View className={className}>
        {labelLine}
        {typeof children === 'function' ? null : children}
      </View>
    )
  }

  if (!name) {
    return null
  }

  return (
    <View className={classNames(noStyle ? '' : 'mb-4', className)}>
      <View
        className={`flex ${
          layout === 'horizontal'
            ? 'flex-row items-center justify-between'
            : 'flex-col'
        }`}
      >
        {labelLine}
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={renderField}
        />
      </View>
      {shouldShowError && (
        <View className="mt-1">
          {errorMessages.map((message, index) => (
            <Text key={index} className="text-sm text-danger">
              {message}
            </Text>
          ))}
        </View>
      )}
    </View>
  )
}
