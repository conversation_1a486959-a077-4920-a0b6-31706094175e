import { useState } from 'react'
import { Image, Pressable } from 'react-native'
import { SwiperFlatList } from 'react-native-swiper-flatlist'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

import PhotoPreview from './common/PhotoPreview'

const PhotoSwiper = ({
  photos,
  imageClassName,
  imageHeight
}: {
  photos: string[]
  imageClassName?: string
  imageHeight?: number
}) => {
  const [previewIndex, setPreviewIndex] = useState(-1)
  return (
    <>
      <SwiperFlatList
        autoplay
        autoplayDelay={3}
        autoplayLoop
        contentContainerClassName="w-full [&_div]:w-full [&_div]:h-full"
        showPagination={photos.length > 1}
        paginationDefaultColor={Colors.white}
        paginationActiveColor={Colors.primary}
        // pagingEnabled={photos.length > 1}
        paginationStyleItem={{ width: 6, height: 6 }}
        data={photos}
        renderItem={({ item, index }) => (
          <Pressable
            className="h-full w-full"
            onPress={() => setPreviewIndex(index)}
          >
            <Image
              source={{ uri: item }}
              className={classNames('w-full', imageClassName)}
              resizeMode="cover"
              height={imageHeight}
            />
          </Pressable>
        )}
        style={{
          minHeight: imageHeight
        }}
      />
      {previewIndex > -1 && (
        <PhotoPreview
          index={previewIndex}
          visible={previewIndex > -1}
          onVisibleChange={() => setPreviewIndex(-1)}
          urls={photos?.map(i => ({ url: i }))}
        />
      )}
    </>
  )
}

export default PhotoSwiper
