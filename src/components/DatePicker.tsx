import React, { forwardRef, useRef, useState } from 'react'
import type { GestureResponderEvent } from 'react-native'
import { Modal, Platform, Pressable, Text, View } from 'react-native'
import type { DateTimePickerEvent } from '@react-native-community/datetimepicker'
import DateTimePicker from '@react-native-community/datetimepicker'
import { FontAwesome6 } from '@expo/vector-icons'
import type { ChangeEvent } from 'react'

import { colors } from '@/theme/colors'

export interface DatePickerProps {
  /**
   * The selected date value
   */
  value?: Date | string
  /**
   * Callback when date changes
   */
  onChange?: (date: Date | null) => void
  /**
   * The placeholder text to display
   */
  placeholder?: string
  /**
   * Minimum date allowed
   */
  minDate?: Date
  /**
   * Maximum date allowed
   */
  maxDate?: Date
  /**
   * Whether the picker is disabled
   */
  disabled?: boolean
  /**
   * The format to display the date
   * @default 'yyyy/MM/dd'
   */
  format?: string
  /**
   * Whether to show clear button
   */
  clearable?: boolean
  /**
   * Test ID for testing purposes
   */
  testID?: string
}

/**
 * A date picker component that matches the Input component's style
 */
export const DatePicker = forwardRef<View, DatePickerProps>(
  (
    {
      value,
      onChange,
      placeholder = 'Select date',
      minDate,
      maxDate,
      disabled,
      format = 'yyyy/MM/dd',
      clearable = true,
      testID
    },
    ref
  ) => {
    const [show, setShow] = useState(false)
    const containerRef = useRef<View>(null)

    const formatDate = (_date: Date | string) => {
      const date = _date instanceof Date ? _date : new Date(_date)
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const year = date.getFullYear()
      return format
        .replace('MM', month)
        .replace('dd', day)
        .replace('yyyy', String(year))
    }

    const formatDateForInput = (date: Date | string) => {
      return new Date(date).toISOString().split('T')[0]
    }

    const handlePress = () => {
      if (!disabled) {
        setShow(true)
      }
    }

    const handleChange = (_: DateTimePickerEvent, selectedDate?: Date) => {
      if (Platform.OS === 'android') {
        setShow(false)
      }
      if (selectedDate) {
        onChange?.(selectedDate)
      }
    }

    const handleWebChange = (e: ChangeEvent<HTMLInputElement>) => {
      const date = e.target.value ? new Date(e.target.value) : null
      onChange?.(date)
    }

    const handleConfirm = () => {
      setShow(false)
    }

    const handleClear = (e: GestureResponderEvent) => {
      e.stopPropagation()
      onChange?.(null)
    }

    if (Platform.OS === 'web') {
      return (
        <View
          ref={ref}
          testID={testID}
          className={`relative h-12 flex-row items-center rounded-lg border border-border px-4 ${
            disabled ? 'bg-gray-100' : 'bg-white'
          }`}
        >
          <input
            type="date"
            value={value ? formatDateForInput(value) : ''}
            onChange={handleWebChange}
            min={minDate ? formatDateForInput(minDate) : undefined}
            max={maxDate ? formatDateForInput(maxDate) : undefined}
            className="absolute inset-0 z-[2] h-full w-full cursor-pointer px-3"
          />
        </View>
      )
    }

    return (
      <>
        <Pressable
          ref={node => {
            if (typeof ref === 'function') {
              ref(node)
            } else if (ref) {
              ref.current = node
            }
            containerRef.current = node
          }}
          testID={testID || 'date-picker-container'}
          onPress={handlePress}
          disabled={disabled}
          className={`h-12 flex-row items-center rounded-lg border border-border px-4 ${
            disabled ? 'bg-gray-100' : 'bg-white'
          }`}
        >
          <Text
            className={`flex-1 truncate text-base ${value ? 'text-dark' : 'text-gray'}`}
            numberOfLines={1}
          >
            {value ? formatDate(value) : placeholder}
          </Text>
          {clearable && value && !disabled && (
            <Pressable
              testID="clear-button"
              onPress={handleClear}
              className="mr-2 p-1"
              hitSlop={8}
            >
              <FontAwesome6
                name="xmark"
                size={14}
                color={colors.palette.neutral400}
              />
            </Pressable>
          )}
          <FontAwesome6
            name="calendar"
            size={16}
            color={colors.palette.neutral400}
          />
        </Pressable>
        {Platform.OS === 'ios' ? (
          <Modal
            visible={show}
            transparent
            animationType="slide"
            onRequestClose={() => setShow(false)}
          >
            <Pressable
              className="flex-1 bg-black"
              onPress={() => setShow(false)}
            >
              <View className="mt-auto rounded-t-xl bg-white p-4">
                <View className="mb-4 flex-row justify-end">
                  <Pressable
                    onPress={handleConfirm}
                    className="rounded-lg bg-primary px-4 py-2"
                  >
                    <Text className="font-medium text-white">Done</Text>
                  </Pressable>
                </View>
                <DateTimePicker
                  value={
                    value
                      ? value instanceof Date
                        ? value
                        : new Date(value)
                      : new Date()
                  }
                  mode="date"
                  display="spinner"
                  onChange={handleChange}
                  minimumDate={minDate}
                  maximumDate={maxDate}
                />
              </View>
            </Pressable>
          </Modal>
        ) : (
          show && (
            <DateTimePicker
              value={
                value
                  ? value instanceof Date
                    ? value
                    : new Date(value)
                  : new Date()
              }
              mode="date"
              display="default"
              onChange={handleChange}
              minimumDate={minDate}
              maximumDate={maxDate}
            />
          )
        )}
      </>
    )
  }
)

DatePicker.displayName = 'DatePicker'
