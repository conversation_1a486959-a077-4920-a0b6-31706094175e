import React, { useCallback, useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import type { FC } from 'react'

import { type LocalPhoto, useChoosePhoto } from '@/hooks/useChoosePhoto'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'

import { Button } from './Button'
import PhotoGallery from './tenant/PhotoGallery'

type AddPhotosProps = {
  title?: string
  description?: string
  value?: components['schemas']['FileInfo'][]
  // onChange?: (photos: string[]) => void
  maxCount?: number
  // uploadPhotos?: boolean
  onChange?: (photos: components['schemas']['FileInfo'][]) => void
}

const MAX_PHOTOS = 9

export const AddPhotos: FC<AddPhotosProps> = ({
  title,
  description = 'Add photos to help us better understand the issue',
  value = [],
  onChange,
  maxCount = MAX_PHOTOS
}) => {
  const [localPhotos, setLocalPhotos] = useState<LocalPhoto[]>([])

  const onLocalPhotosSelected = useCallback((newLocalPhotos: LocalPhoto[]) => {
    setLocalPhotos(prev => [...prev, ...newLocalPhotos])
  }, [])

  const onUploadProgress = useCallback((photoId: string, progress: number) => {
    setLocalPhotos(prev =>
      prev.map(photo =>
        photo.id === photoId ? { ...photo, uploadProgress: progress } : photo
      )
    )
  }, [])

  const onUploaded = useCallback(
    (
      fileKeys: components['schemas']['FileInfo'][],
      uploadedPhotoIds: string[]
    ) => {
      // After upload completes, remove only the uploaded local photos and add to remote photos
      setLocalPhotos(prev =>
        prev.filter(photo => !uploadedPhotoIds.includes(photo.id))
      )
      onChange?.([...value, ...fileKeys])
    },
    [value, onChange]
  )

  const { chooseFromCamera, chooseFromLibrary } = useChoosePhoto({
    maxCount: () => maxCount - (value.length + localPhotos.length),
    onLocalPhotosSelected,
    onUploadProgress,
    onUploaded
  })

  const handleRemove = useCallback(
    (index: number) => {
      const totalLocalPhotos = localPhotos.length

      if (index < totalLocalPhotos) {
        // remove local photos
        const newLocalPhotos = localPhotos.filter((_, i) => i !== index)
        setLocalPhotos(newLocalPhotos)
      } else {
        // remove remote photos
        const remoteIndex = index - totalLocalPhotos
        const newRemotePhotos = value.filter((_, i) => i !== remoteIndex)
        onChange?.(newRemotePhotos)
      }
    },
    [localPhotos, value, onChange]
  )

  return (
    <View className="rounded-default border border-dashed border-border bg-white px-3 pb-2 pt-3">
      {title && (
        <Text className="absolute -top-5 left-0 z-10 px-1 text-xs font-semibold text-dark">
          {title}
        </Text>
      )}
      <View className="min-h-[160px] items-center justify-center">
        <FontAwesome6
          name="camera"
          size={36}
          color={Colors.gray}
          style={{ marginTop: 8 }}
        />
        <Text className="mb-3 mt-2 text-center text-xs text-gray">
          {description}
        </Text>
        <View className="mb-2 w-full flex-row items-center justify-center gap-[10px] px-2 py-2">
          <Button
            className="flex-1"
            onPress={chooseFromCamera}
            disabled={value.length + localPhotos.length >= maxCount}
            variant="cancel"
            leftIcon={
              <FontAwesome6 name="camera" size={14} color={Colors.dark} />
            }
          >
            Take Photo
          </Button>
          <Button
            className="flex-1"
            onPress={chooseFromLibrary}
            disabled={value.length + localPhotos.length >= maxCount}
            variant="cancel"
            leftIcon={
              <FontAwesome6 name="image" size={14} color={Colors.dark} />
            }
          >
            Upload
          </Button>
        </View>
        {(value.length > 0 || localPhotos.length > 0) && (
          <PhotoGallery
            className="w-full"
            remotePhotos={value}
            localPhotos={localPhotos}
            removable
            onRemove={handleRemove}
          />
        )}
      </View>
    </View>
  )
}
