import { View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import classNames from '@/utils/classname'

/**
 * Rating component, rating is a number between 0 and 5
 */
export function Rating({
  className,
  rating,
  size = 16
}: {
  className?: string
  rating: number
  size?: number
}) {
  const ratingSolid = Math.round(rating)
  const ratingEmpty = 5 - ratingSolid
  return (
    <View className={classNames('flex-row items-center', className)}>
      {Array.from({ length: ratingSolid }).map((_, index) => (
        <FontAwesome6 size={size} key={index} name="star" color="gold" solid />
      ))}
      {Array.from({ length: ratingEmpty }).map((_, index) => (
        <FontAwesome6
          size={size}
          key={`empty-${index}`}
          name="star"
          color="gold"
        />
      ))}
    </View>
  )
}
