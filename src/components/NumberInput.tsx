import React, { forwardRef, useRef } from 'react'

import type { InputHandle, InputProps } from './Input'
import { Input } from './Input'

export interface NumberInputProps extends Omit<InputProps, 'onChange'> {
  /**
   * Minimum value allowed
   */
  min?: number
  /**
   * Maximum value allowed
   */
  max?: number
  /**
   * Number of decimal places allowed
   */
  decimalPlaces?: number
  /**
   * Callback when value changes
   */
  onChange?: (value: number | null) => void
  /**
   * Whether to allow negative numbers
   */
  allowNegative?: boolean
}

/**
 * A specialized input component for numeric values
 */
export const NumberInput = forwardRef<InputHandle, NumberInputProps>(
  (
    { min, max, decimalPlaces = 0, onChange, allowNegative = true, ...props },
    ref
  ) => {
    const inputRef = useRef<InputHandle>(null)

    const handleChange = (text: string) => {
      // Allow empty input
      if (text === '') {
        onChange?.(null)
        return
      }

      // Remove any non-numeric characters except decimal point and minus sign
      let cleanedText = text.replace(/[^0-9.-]/g, '')

      // Handle negative numbers
      if (!allowNegative) {
        cleanedText = cleanedText.replace(/-/g, '')
      }

      // Ensure only one decimal point
      const parts = cleanedText.split('.')
      if (parts.length > 2) {
        cleanedText = parts[0] + '.' + parts.slice(1).join('')
      }

      // Limit decimal places
      if (decimalPlaces === 0) {
        cleanedText = parts[0] || '0'
      } else if (parts.length > 1) {
        cleanedText = parts[0] + '.' + parts[1]!.slice(0, decimalPlaces)
      }

      // Convert to number and validate range
      const num = parseFloat(cleanedText)
      if (!isNaN(num)) {
        let targetNum: number = num
        if (min !== undefined && num < min) {
          targetNum = min
        } else if (max !== undefined && num > max) {
          targetNum = max
        }

        // Update input state directly
        const displayValue = targetNum.toString()
        inputRef.current?.setText(displayValue)
        onChange?.(targetNum)
      }
    }

    return (
      <Input
        ref={node => {
          // Handle both refs
          if (typeof ref === 'function') {
            ref(node)
          } else if (ref) {
            ref.current = node
          }
          inputRef.current = node
        }}
        keyboardType="numeric"
        onChange={handleChange}
        {...props}
        placeholder={props.placeholder ?? 'Please enter a number'}
      />
    )
  }
)

NumberInput.displayName = 'NumberInput'
