import React, { useEffect, useState } from 'react'
import {
  ActivityIndicator,
  Keyboard,
  Modal,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  View
} from 'react-native'
import {
  ChevronDownIcon,
  ChevronUpIcon,
  MagnifyingGlassIcon
} from 'react-native-heroicons/outline'

import classNames from '@/utils/classname'

export interface SearchableDropdownOption {
  label: string
  value: string
  [key: string]: any
}

export interface SearchableDropdownProps {
  options: SearchableDropdownOption[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  loading?: boolean
  error?: string
  disabled?: boolean
  className?: string
  renderOption?: (
    option: SearchableDropdownOption,
    isSelected: boolean
  ) => React.ReactNode
  filterOptions?: (
    options: SearchableDropdownOption[],
    searchText: string
  ) => SearchableDropdownOption[]
  modal?: boolean
}

export function SearchableDropdown({
  options,
  value,
  onValueChange,
  placeholder = 'Select an option',
  searchPlaceholder = 'Search...',
  loading = false,
  error,
  disabled = false,
  className,
  renderOption,
  filterOptions,
  modal = false
}: SearchableDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [selectedOption, setSelectedOption] =
    useState<SearchableDropdownOption | null>(null)

  // Find selected option based on value
  useEffect(() => {
    if (value) {
      const option = options.find(opt => opt.value === value)
      setSelectedOption(option || null)
    } else {
      setSelectedOption(null)
    }
  }, [value, options])

  // Filter options based on search text
  const filteredOptions = filterOptions
    ? filterOptions(options, searchText)
    : options.filter(option =>
        option.label.toLowerCase().includes(searchText.toLowerCase())
      )

  // Handle option selection
  const handleSelectOption = (option: SearchableDropdownOption) => {
    setSelectedOption(option)
    onValueChange(option.value)
    setIsOpen(false)
    setSearchText('')
  }

  // Handle dropdown toggle
  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen)
      if (!isOpen) {
        setSearchText('')
      }
    }
  }

  // Default option renderer
  const defaultRenderOption = (
    option: SearchableDropdownOption,
    isSelected: boolean
  ) => (
    <View
      className={classNames(
        'border-b border-gray-100 px-4 py-3',
        isSelected ? 'bg-blue-50' : 'bg-white'
      )}
    >
      <Text
        className={classNames(
          'text-sm',
          isSelected ? 'font-medium text-blue-600' : 'text-gray-900'
        )}
      >
        {option.label}
      </Text>
    </View>
  )

  // --- Modal 模式 ---
  if (modal) {
    return (
      <>
        <Pressable
          onPress={handleToggle}
          className={classNames(
            'flex-row items-center justify-between rounded-lg border px-4 py-3',
            'bg-white',
            error ? 'border-red-300' : 'border-gray-300',
            disabled ? 'opacity-50' : '',
            isOpen ? 'border-blue-500' : '',
            className
          )}
          disabled={disabled}
        >
          <View className="flex-1">
            {selectedOption ? (
              <Text className="text-sm text-gray-900">
                {selectedOption.label}
              </Text>
            ) : (
              <Text className="text-gray-500 text-sm">{placeholder}</Text>
            )}
          </View>
          <View className="ml-2 flex-row items-center">
            {loading && <ActivityIndicator size="small" className="mr-2" />}
            <ChevronDownIcon size={20} className="text-gray-400" />
          </View>
        </Pressable>
        <Modal
          visible={isOpen}
          animationType="slide"
          transparent
          onRequestClose={() => setIsOpen(false)}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View
              style={{
                flex: 1,
                justifyContent: 'flex-end',
                backgroundColor: 'rgba(0,0,0,0.2)'
              }}
            >
              <View
                style={{
                  backgroundColor: '#fff',
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  paddingBottom: Platform.OS === 'ios' ? 32 : 16,
                  maxHeight: '70%'
                }}
              >
                <View className="flex-row items-center border-b border-gray-200 p-4">
                  <MagnifyingGlassIcon
                    size={18}
                    className="text-gray-400 mr-2"
                  />
                  <TextInput
                    value={searchText}
                    onChangeText={setSearchText}
                    placeholder={searchPlaceholder}
                    className="flex-1 bg-transparent text-base text-gray-900"
                  />
                </View>
                <ScrollView style={{ maxHeight: 320 }}>
                  {filteredOptions.length > 0 ? (
                    filteredOptions.map((option, index) => (
                      <Pressable
                        key={`${option.value}-${index}`}
                        onPress={() => handleSelectOption(option)}
                      >
                        {renderOption
                          ? renderOption(option, option.value === value)
                          : defaultRenderOption(option, option.value === value)}
                      </Pressable>
                    ))
                  ) : (
                    <View className="px-4 py-3">
                      <Text className="text-gray-500 text-center text-sm">
                        No options found
                      </Text>
                    </View>
                  )}
                </ScrollView>
                <Pressable onPress={() => setIsOpen(false)} className="p-4">
                  <Text className="text-center text-base font-medium text-blue-600">
                    Cancel
                  </Text>
                </Pressable>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
        {error && <Text className="mt-1 text-xs text-red-500">{error}</Text>}
      </>
    )
  }

  // --- 普通下拉模式 ---
  return (
    <View className={classNames('relative', className)}>
      {/* Main dropdown button */}
      <Pressable
        onPress={handleToggle}
        className={classNames(
          'flex-row items-center justify-between rounded-lg border px-4 py-3',
          'bg-white',
          error ? 'border-red-300' : 'border-gray-300',
          disabled ? 'opacity-50' : '',
          isOpen ? 'border-blue-500' : ''
        )}
        disabled={disabled}
      >
        <View className="flex-1">
          {selectedOption ? (
            <Text className="text-sm text-gray-900">
              {selectedOption.label}
            </Text>
          ) : (
            <Text className="text-gray-500 text-sm">{placeholder}</Text>
          )}
        </View>

        <View className="ml-2 flex-row items-center">
          {loading && <ActivityIndicator size="small" className="mr-2" />}
          {isOpen ? (
            <ChevronUpIcon size={20} className="text-gray-400" />
          ) : (
            <ChevronDownIcon size={20} className="text-gray-400" />
          )}
        </View>
      </Pressable>

      {/* Error message */}
      {error && <Text className="mt-1 text-xs text-red-500">{error}</Text>}

      {/* Dropdown menu */}
      {isOpen && (
        <View
          style={{ zIndex: 999 }}
          className="absolute left-0 right-0 top-full z-50 mt-1 max-h-60 rounded-lg border border-gray-300 bg-white shadow-lg"
        >
          {/* Search input */}
          <View className="border-b border-gray-200 p-3">
            <View className="bg-gray-50 flex-row items-center rounded-lg px-3 py-2">
              <MagnifyingGlassIcon size={16} className="text-gray-400 mr-2" />
              <TextInput
                value={searchText}
                onChangeText={setSearchText}
                placeholder={searchPlaceholder}
                className="flex-1 bg-transparent text-base text-gray-900"
              />
            </View>
          </View>

          {/* Options list */}
          <ScrollView className="max-h-48">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <Pressable
                  key={`${option.value}-${index}`}
                  onPress={() => handleSelectOption(option)}
                >
                  {renderOption
                    ? renderOption(option, option.value === value)
                    : defaultRenderOption(option, option.value === value)}
                </Pressable>
              ))
            ) : (
              <View className="px-4 py-3">
                <Text className="text-gray-500 text-center text-sm">
                  No options found
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      )}
    </View>
  )
}
