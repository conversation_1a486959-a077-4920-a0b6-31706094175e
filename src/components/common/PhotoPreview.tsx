// import Gallery from 'react-native-awesome-gallery'
import ImageView from 'react-native-image-viewing'

export type PhotoPreviewProps = {
  visible?: boolean
  urls: { url: string; alt?: string }[]
  index?: number
  onVisibleChange?: (v: boolean) => void
}

const PhotoPreview = ({
  urls,
  index,
  visible = false,
  onVisibleChange
}: PhotoPreviewProps) => {
  return (
    // <Gallery
    //   data={urls.map(i => i.url)}
    //   initialIndex={index}
    //   onIndexChange={newIndex => {
    //     console.log(newIndex)
    //   }}
    // />
    <ImageView
      images={urls.map(i => ({ uri: i.url }))}
      imageIndex={index || 0}
      visible={visible}
      onRequestClose={() => onVisibleChange?.(false)}
    />
  )
}

export default PhotoPreview
