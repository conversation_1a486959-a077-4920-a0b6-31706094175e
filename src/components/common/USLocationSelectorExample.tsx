import React, { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'

import { USLocationSelector } from './USLocationSelector'

export function USLocationSelectorExample() {
  const [location, setLocation] = useState({
    state: '',
    city: '',
    zipcode: ''
  })

  const handleLocationChange = (newLocation: {
    state: string
    city: string
    zipcode: string
  }) => {
    setLocation(newLocation)
  }

  return (
    <ScrollView className="bg-gray-50 flex-1 p-4">
      <View className="rounded-lg bg-white p-6 shadow-sm">
        <Text className="mb-6 text-xl font-semibold text-gray-900">
          US Location Selector Example
        </Text>

        <USLocationSelector
          selectedState={location.state}
          selectedCity={location.city}
          selectedZipcode={location.zipcode}
          onLocationChange={handleLocationChange}
          className="mb-6"
        />

        {/* Display selected values */}
        <View className="bg-gray-50 rounded-lg p-4">
          <Text className="mb-2 text-sm font-medium text-gray-700">
            Selected Location:
          </Text>
          <Text className="text-sm text-gray-600">
            State: {location.state || 'Not selected'}
          </Text>
          <Text className="text-sm text-gray-600">
            City: {location.city || 'Not selected'}
          </Text>
          <Text className="text-sm text-gray-600">
            Zipcode: {location.zipcode || 'Not entered'}
          </Text>
        </View>

        {/* Instructions */}
        <View className="mt-6 rounded-lg bg-blue-50 p-4">
          <Text className="mb-2 text-sm font-medium text-blue-900">
            How to use:
          </Text>
          <Text className="mb-1 text-sm text-blue-800">
            • Select a state from the dropdown (supports search)
          </Text>
          <Text className="mb-1 text-sm text-blue-800">
            • Choose a city from the filtered list
          </Text>
          <Text className="mb-1 text-sm text-blue-800">
            • The first zipcode will be auto-filled, but you can edit it
          </Text>
          <Text className="text-sm text-blue-800">
            • All fields support both selection and manual input
          </Text>
        </View>
      </View>
    </ScrollView>
  )
}
