import React, { useEffect, useMemo, useState } from 'react'
import { Text, TextInput, View } from 'react-native'

import { useUSStatesCities } from '@/hooks/useUSStatesCities'
import classNames from '@/utils/classname'

import { SearchableDropdown } from './SearchableDropdown'

export interface USLocationSelectorProps {
  selectedState?: string
  selectedCity?: string
  selectedZipcode?: string
  onStateChange?: (stateCode: string) => void
  onCityChange?: (cityName: string) => void
  onZipcodeChange?: (zipcode: string) => void
  onLocationChange?: (location: {
    state: string
    city: string
    zipcode: string
  }) => void
  className?: string
  error?: string
  disabled?: boolean
}

export function USLocationSelector({
  selectedState,
  selectedCity,
  selectedZipcode,
  onStateChange,
  onCityChange,
  onZipcodeChange,
  onLocationChange,
  className,
  error,
  disabled = false
}: USLocationSelectorProps) {
  const { data: statesData, loading, error: apiError } = useUSStatesCities()
  const [customZipcode, setCustomZipcode] = useState(selectedZipcode || '')

  // Convert states data to dropdown options
  const stateOptions = useMemo(() => {
    return statesData.map(state => ({
      label: `${state.stateCode} - ${state.stateName}`,
      value: state.stateCode || '',
      stateName: state.stateName,
      cities: state.cities || []
    }))
  }, [statesData])

  // Get cities for selected state
  const cityOptions = useMemo(() => {
    if (!selectedState) return []

    const selectedStateData = statesData.find(
      state => state.stateCode === selectedState
    )
    if (!selectedStateData?.cities) return []

    return selectedStateData.cities.map(city => ({
      label: city.cityName || '',
      value: city.cityName || '',
      zipcodes: city.zipcodes || []
    }))
  }, [selectedState, statesData])

  // Get first zipcode for selected city
  const firstZipcode = useMemo(() => {
    if (!selectedCity) return ''

    const selectedCityData = cityOptions.find(
      city => city.value === selectedCity
    )
    return selectedCityData?.zipcodes?.[0] || ''
  }, [selectedCity, cityOptions])

  // Handle state selection
  const handleStateChange = (stateCode: string) => {
    onStateChange?.(stateCode)
    onLocationChange?.({
      state: stateCode,
      city: '',
      zipcode: ''
    })
  }

  // Handle city selection
  const handleCityChange = (cityName: string) => {
    onCityChange?.(cityName)

    const selectedCityData = cityOptions.find(city => city.value === cityName)
    const zipcode = selectedCityData?.zipcodes?.[0] || ''

    onZipcodeChange?.(zipcode)
    setCustomZipcode(zipcode)
    onLocationChange?.({
      state: selectedState || '',
      city: cityName,
      zipcode
    })
  }

  // Handle zipcode change
  const handleZipcodeChange = (zipcode: string) => {
    setCustomZipcode(zipcode)
    onZipcodeChange?.(zipcode)
    onLocationChange?.({
      state: selectedState || '',
      city: selectedCity || '',
      zipcode
    })
  }

  // Update custom zipcode when selected zipcode changes
  useEffect(() => {
    if (selectedZipcode !== undefined) {
      setCustomZipcode(selectedZipcode)
    }
  }, [selectedZipcode])

  return (
    <View className={classNames('space-y-3', className)}>
      {/* State and City Row */}
      <View className="flex-row space-x-3">
        {/* State Selector */}
        <View className="flex-1" style={{ zIndex: 30 }}>
          <Text className="mb-1 text-sm font-medium text-gray-700">State</Text>
          <SearchableDropdown
            options={stateOptions}
            value={selectedState}
            onValueChange={handleStateChange}
            placeholder="Select state"
            searchPlaceholder="Search states..."
            loading={loading}
            error={apiError || undefined}
            disabled={disabled}
            modal
          />
        </View>

        {/* City Selector */}
        <View className="flex-1" style={{ zIndex: 20 }}>
          <Text className="mb-1 text-sm font-medium text-gray-700">City</Text>
          <SearchableDropdown
            options={cityOptions}
            value={selectedCity}
            onValueChange={handleCityChange}
            placeholder="Select city"
            searchPlaceholder="Search cities..."
            disabled={disabled || !selectedState || cityOptions.length === 0}
            modal
          />
        </View>
      </View>

      {/* Zipcode Row */}
      <View style={{ zIndex: 10 }}>
        <Text className="mb-1 text-sm font-medium text-gray-700">Zipcode</Text>
        <TextInput
          value={customZipcode}
          onChangeText={handleZipcodeChange}
          placeholder="Enter zipcode"
          className={classNames(
            'rounded-lg border px-4 py-3 text-sm',
            'bg-white',
            error ? 'border-red-300' : 'border-gray-300',
            disabled ? 'opacity-50' : ''
          )}
          editable={!disabled}
          keyboardType="numeric"
          maxLength={10}
        />
        {firstZipcode && firstZipcode !== customZipcode && (
          <Text className="text-gray-500 mt-1 text-xs">
            Suggested: {firstZipcode}
          </Text>
        )}
      </View>

      {/* Error message */}
      {error && <Text className="text-xs text-red-500">{error}</Text>}
    </View>
  )
}
