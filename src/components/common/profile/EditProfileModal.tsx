import React, { useState } from 'react'
import type { Path, RegisterOptions } from 'react-hook-form'
import { Pressable, Text, View } from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'

import type { DatePickerProps, InputProps } from '@/components'
import { DatePicker, FormItem, Input } from '@/components'
import { Button } from '@/components/Button'
import { Form } from '@/components/Form'
import type { NumberInputProps } from '@/components/NumberInput'
import { NumberInput } from '@/components/NumberInput'

import type { PhoneNumberInputProps } from '../PhoneNumberInput'
import { PhoneNumberInput } from '../PhoneNumberInput'

export function EditProfileModal<
  T extends Record<string, string | number | Date>
>({
  visible = true,
  initialValues,
  columns,
  onSave,
  onClose
}: {
  visible?: boolean
  initialValues: T
  columns: ({
    label: string
    name: keyof T
    maxLength?: number
    required?: boolean
    rules?: RegisterOptions<T, Path<T>>
  } & (
    | {
        type: 'date'
        pickerProps: Omit<DatePickerProps, 'value' | 'onChange'>
      }
    | {
        type: 'number'
        inputProps?: Omit<NumberInputProps, 'value' | 'onChange'>
      }
    | {
        type: 'phone'
        inputProps?: Omit<PhoneNumberInputProps, 'value' | 'onChange'>
      }
    | {
        inputProps?: Omit<InputProps, 'maxLength' | 'value' | 'onChange'>
      }
    | {
        type: 'text'
        inputProps?: Omit<InputProps, 'maxLength' | 'value' | 'onChange'>
      }
  ))[]
  onSave?: (values: T) => Promise<boolean>
  onClose?: () => void
}) {
  const form = Form.useForm()
  const [loading, setLoading] = useState(false)

  if (!visible) return null
  return (
    <View className="absolute inset-0 z-50 items-center justify-start bg-black/20 px-2 pt-8">
      <View className="w-full max-w-md rounded-2xl bg-white shadow-xl">
        {/* Header */}
        <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
          <Text className="text-lg font-bold text-dark">Edit Profile</Text>
          <Pressable onPress={onClose} className="p-1">
            <XMarkIcon size={22} color="#6b7280" />
          </Pressable>
        </View>
        {/* Form */}
        <Form
          className="p-4"
          form={form}
          initialValues={initialValues}
          onFinish={values => {
            setLoading(true)
            onSave?.(values as T).then(success => {
              if (success) {
                onClose?.()
              }
              setLoading(false)
            })
          }}
        >
          {columns.map((item, index) => (
            <FormItem
              key={index}
              label={item.label}
              name={item.name as Path<T>}
              rules={
                item.rules
                  ? item.rules
                  : item.required
                    ? {
                        required: { value: true, message: 'This is required' }
                      }
                    : undefined
              }
            >
              {!('type' in item) || item.type === 'text' ? (
                <Input
                  placeholder={item.label}
                  maxLength={item.maxLength}
                  {...item.inputProps}
                />
              ) : item.type === 'number' ? (
                <NumberInput
                  placeholder={item.label}
                  maxLength={item.maxLength}
                  {...item.inputProps}
                />
              ) : item.type === 'date' ? (
                <DatePicker placeholder={item.label} {...item.pickerProps} />
              ) : item.type === 'phone' ? (
                <PhoneNumberInput
                  placeholder={item.label}
                  {...item.inputProps}
                />
              ) : null}
            </FormItem>
          ))}
        </Form>
        {/* Footer */}
        <View className="flex-row items-center gap-2 border-t border-t-border bg-white px-6 py-4">
          <Button variant="cancel" className="flex-1" onPress={onClose}>
            Cancel
          </Button>
          <Button
            variant="primary"
            className="flex-1"
            loading={loading}
            onPress={form.submit}
          >
            Save Changes
          </Button>
        </View>
      </View>
    </View>
  )
}
