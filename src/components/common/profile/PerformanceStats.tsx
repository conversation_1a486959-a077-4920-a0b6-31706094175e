import React from 'react'
import { Text, View } from 'react-native'
import { Bars3BottomLeftIcon } from 'react-native-heroicons/outline'

import { Colors, ShadowStyles } from '@/theme/colors'

export function PerformanceStats({
  items
}: {
  items: {
    label: string
    value: number
    color: string
  }[]
}) {
  return (
    <View
      className="mb-4 flex-col rounded-2xl border border-border bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="mb-3 flex-row items-center">
        <Bars3BottomLeftIcon size={20} color={Colors.primary} />
        <Text className="ml-2 text-base font-bold text-dark">
          Performance Stats
        </Text>
      </View>
      <View className="flex-row items-center justify-between">
        {items.map((item, index) => (
          <View key={index} className="flex-1 flex-row items-center">
            <View className="flex-1 items-center">
              <Text className="text-lg font-bold" style={{ color: item.color }}>
                {item.value}
              </Text>
              <Text className="text-gray-400 mt-1 text-xs">{item.label}</Text>
            </View>
            {index < items.length - 1 && (
              <View className="mx-2 h-8 w-px bg-gray-200" />
            )}
          </View>
        ))}
      </View>
    </View>
  )
}
