import React from 'react'
import { Text, View } from 'react-native'
import { UserIcon } from 'react-native-heroicons/solid'

import { Colors, ShadowStyles } from '@/theme/colors'

export function PersonalInformation({
  items
}: {
  items: { label: string; value: string }[]
}) {
  return (
    <View
      className="mb-4 rounded-2xl border border-border bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="mb-3 flex-row items-center">
        <UserIcon size={20} color={Colors.primary} />
        <Text className="ml-2 text-base font-bold text-dark">
          Personal Information
        </Text>
      </View>
      <View>
        {items.map((item, index) => (
          <View
            className="flex-row items-center justify-between border-b border-gray-100 py-2"
            key={index}
          >
            <Text className="text-sm font-semibold text-dark">
              {item.label}
            </Text>
            <Text className="text-sm text-gray">{item.value}</Text>
          </View>
        ))}
      </View>
    </View>
  )
}
