import React from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { CameraIcon } from 'react-native-heroicons/solid'
import { useActionSheet } from '@expo/react-native-action-sheet'

import { useChoosePhoto } from '@/hooks/useChoosePhoto'
import { client } from '@/services/api'
import { useAuth } from '@/store'
import { ShadowStyles } from '@/theme/colors'

export interface PersonalProps {
  avatar?: string
  name: string
  email: string
  role: string
}

export function Personal({ avatar, name, email, role }: PersonalProps) {
  const { showActionSheetWithOptions } = useActionSheet()
  const { refreshMe } = useAuth()
  const { chooseFromCamera, chooseFromLibrary } = useChoosePhoto({
    maxCount: () => 1,
    onUploaded: async fileKeys => {
      const { error } = await client.PATCH('/api/v1/admin/user/me/avatar', {
        body: {
          fileKey: fileKeys[0]!.fileKey
        }
      })
      if (!error) {
        refreshMe()
      }
    }
  })
  const onSelectPhoto = () => {
    showActionSheetWithOptions(
      {
        options: ['Take Photo', 'Choose from Library', 'Cancel'],
        cancelButtonIndex: 2,
        title: 'Select Photo',
        message: 'Please select a photo from your library or take a new one',
        userInterfaceStyle: 'light',
        tintColor: '#000',
        destructiveButtonIndex: 2
      },
      selectedIndex => {
        if (selectedIndex === 0) {
          chooseFromCamera()
        } else if (selectedIndex === 1) {
          chooseFromLibrary()
        }
      }
    )
  }

  return (
    <View
      className="mb-4 flex-row items-center rounded-md border border-border bg-white p-4"
      style={ShadowStyles.default}
    >
      <View className="relative mr-4">
        <View className="h-16 w-16 items-center justify-center rounded-full bg-indigo-600">
          {avatar ? (
            <Image
              source={{ uri: avatar }}
              className="h-full w-full rounded-full"
            />
          ) : (
            <Text className="text-2xl font-bold text-white">
              {name
                ?.split(' ')
                .map(n => n[0])
                .join('')
                .toUpperCase() ?? ''}
            </Text>
          )}
        </View>
        <Pressable
          onPress={onSelectPhoto}
          className="absolute -bottom-1 -right-1 h-7 w-7 items-center justify-center rounded-full bg-gray-900"
        >
          <CameraIcon size={16} color="#fff" />
        </Pressable>
      </View>
      <View className="flex-1 justify-center">
        <Text className="text-base font-bold text-gray-900">{name}</Text>
        <Text className="text-gray-500 text-sm">{email}</Text>
        <Text className="mt-1 text-sm font-medium text-indigo-600">{role}</Text>
      </View>
    </View>
  )
}
