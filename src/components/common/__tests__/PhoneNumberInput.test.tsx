import React from 'react'
import { fireEvent, render, waitFor } from '@testing-library/react-native'

import { PhoneNumberInput } from '../PhoneNumberInput'

describe('PhoneNumberInput', () => {
  const mockOnChangeText = jest.fn()
  const mockOnValidationChange = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render phone number input with default country', () => {
    const { getByText, getByPlaceholderText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} />
    )

    expect(getByText('🇺🇸')).toBeTruthy()
    expect(getByText('+1')).toBeTruthy()
    expect(getByPlaceholderText('Phone Number')).toBeTruthy()
  })

  it('should call onChangeText with full phone number when input changes', () => {
    const { getByPlaceholderText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} />
    )

    const input = getByPlaceholderText('Phone Number')
    fireEvent.changeText(input, '1234567890')

    expect(mockOnChangeText).toHaveBeenCalledWith('+11234567890')
  })

  it('should parse existing phone number with country code', () => {
    const { getByDisplayValue } = render(
      <PhoneNumberInput value="+8612345678901" onChange={mockOnChangeText} />
    )

    expect(getByDisplayValue('12345678901')).toBeTruthy()
  })

  it('should validate phone number and call onValidationChange', async () => {
    render(
      <PhoneNumberInput
        value=""
        onChange={mockOnChangeText}
        onValidationChange={mockOnValidationChange}
      />
    )

    await waitFor(() => {
      expect(mockOnValidationChange).toHaveBeenCalledWith(false)
    })
  })

  it('should show validation message for valid phone number', () => {
    const { getByPlaceholderText, getByText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} showValidate />
    )

    const input = getByPlaceholderText('Phone Number')
    fireEvent.changeText(input, '1234567890')

    expect(getByText('✓ Valid phone number')).toBeTruthy()
  })

  it('should show validation message for invalid phone number', () => {
    const { getByPlaceholderText, getByText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} showValidate />
    )

    const input = getByPlaceholderText('Phone Number')
    fireEvent.changeText(input, '123')

    expect(getByText('⚠ Please check the phone number')).toBeTruthy()
  })

  it('should show error message when error prop is provided', () => {
    const { getByText } = render(
      <PhoneNumberInput
        value=""
        onChange={mockOnChangeText}
        error="Invalid phone number"
        showValidate
      />
    )

    expect(getByText('Invalid phone number')).toBeTruthy()
  })

  it('should be disabled when disabled prop is true', () => {
    const { getByPlaceholderText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} disabled={true} />
    )

    const input = getByPlaceholderText('Phone Number')
    expect(input.props.editable).toBe(false)
  })

  it('should clean non-digit characters from input', () => {
    const { getByPlaceholderText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} />
    )

    const input = getByPlaceholderText('Phone Number')
    fireEvent.changeText(input, '************')

    expect(mockOnChangeText).toHaveBeenCalledWith('+11234567890')
  })

  it('should handle country code change', async () => {
    const { getByText, getByPlaceholderText } = render(
      <PhoneNumberInput value="" onChange={mockOnChangeText} />
    )

    // Open country picker
    const picker = getByText('🇺🇸').parent?.parent
    fireEvent.press(picker!)

    await waitFor(() => {
      expect(getByText('China')).toBeTruthy()
    })

    // Select China
    fireEvent.press(getByText('China'))

    // Change phone number
    const input = getByPlaceholderText('Phone Number')
    fireEvent.changeText(input, '12345678901')

    expect(mockOnChangeText).toHaveBeenCalledWith('+8612345678901')
  })
})
