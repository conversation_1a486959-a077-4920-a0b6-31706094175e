import React from 'react'
import { fireEvent, render, waitFor } from '@testing-library/react-native'

import { CountryCodePicker } from '../CountryCodePicker'

const mockCountry = {
  code: 'US',
  name: 'United States',
  dialCode: '+1',
  flag: '🇺🇸'
}

describe('CountryCodePicker', () => {
  const mockOnSelectCountry = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render country code picker with selected country', () => {
    const { getByText } = render(
      <CountryCodePicker
        selectedCountry={mockCountry}
        onSelectCountry={mockOnSelectCountry}
      />
    )

    expect(getByText('🇺🇸')).toBeTruthy()
    expect(getByText('+1')).toBeTruthy()
  })

  it('should open modal when pressed', async () => {
    const { getByText } = render(
      <CountryCodePicker
        selectedCountry={mockCountry}
        onSelectCountry={mockOnSelectCountry}
      />
    )

    const picker = getByText('🇺🇸').parent?.parent
    fireEvent.press(picker!)

    await waitFor(() => {
      expect(getByText('Select Country')).toBeTruthy()
    })
  })

  it('should call onSelectCountry when a country is selected', async () => {
    const { getByText } = render(
      <CountryCodePicker
        selectedCountry={mockCountry}
        onSelectCountry={mockOnSelectCountry}
      />
    )

    const picker = getByText('🇺🇸').parent?.parent
    fireEvent.press(picker!)

    await waitFor(() => {
      expect(getByText('China')).toBeTruthy()
    })

    fireEvent.press(getByText('China'))

    expect(mockOnSelectCountry).toHaveBeenCalledWith({
      code: 'CN',
      name: 'China',
      dialCode: '+86',
      flag: '🇨🇳'
    })
  })

  it('should be disabled when disabled prop is true', () => {
    const { getByText } = render(
      <CountryCodePicker
        selectedCountry={mockCountry}
        onSelectCountry={mockOnSelectCountry}
        disabled={true}
      />
    )

    const picker = getByText('🇺🇸').parent?.parent
    fireEvent.press(picker!)

    // Modal should not open when disabled
    expect(() => getByText('Select Country')).toThrow()
  })

  it('should close modal when cancel is pressed', async () => {
    const { getByText } = render(
      <CountryCodePicker
        selectedCountry={mockCountry}
        onSelectCountry={mockOnSelectCountry}
      />
    )

    const picker = getByText('🇺🇸').parent?.parent
    fireEvent.press(picker!)

    await waitFor(() => {
      expect(getByText('Cancel')).toBeTruthy()
    })

    fireEvent.press(getByText('Cancel'))

    await waitFor(() => {
      expect(() => getByText('Select Country')).toThrow()
    })
  })
})
