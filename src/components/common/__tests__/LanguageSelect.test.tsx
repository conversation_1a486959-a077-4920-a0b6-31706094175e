import React from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { fireEvent, render } from '@testing-library/react-native'

import { useLanguage } from '@/hooks/useLanguage'
import { useThemeProvider } from '@/utils/useAppTheme'

import { LanguageSelect } from '../LanguageSelect'

// Mock the useLanguage hook
jest.mock('@/hooks/useLanguage', () => ({
  useLanguage: jest.fn()
}))

jest.mock('@/components/Icon', () => ({
  Icon: () => null,
  PressableIcon: () => null
}))

const mockUseLanguage = useLanguage as jest.MockedFunction<typeof useLanguage>

// Helper function to render with theme provider
const renderWithTheme = (component: React.ReactElement) => {
  const TestThemeProvider = () => {
    const { ThemeProvider } = useThemeProvider()
    return (
      <ThemeProvider
        value={{ themeScheme: 'light', setThemeContextOverride: jest.fn() }}
      >
        {component}
      </ThemeProvider>
    )
  }

  return render(
    <NavigationContainer>
      <TestThemeProvider />
    </NavigationContainer>
  )
}

describe('LanguageSelect', () => {
  const mockLanguages = [
    { type: 'LANGUAGE', code: 'en', label: 'English', sort: 1 },
    { type: 'LANGUAGE', code: 'zh', label: '中文', sort: 2 },
    { type: 'LANGUAGE', code: 'es', label: 'Español', sort: 3 }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render loading state', () => {
    mockUseLanguage.mockReturnValue({
      languages: [],
      loading: true,
      error: null,
      refetch: jest.fn()
    })

    const { getByText } = renderWithTheme(<LanguageSelect />)

    expect(getByText('Loading languages...')).toBeTruthy()
  })

  it('should render error state', () => {
    const errorMessage = 'Failed to fetch languages'
    mockUseLanguage.mockReturnValue({
      languages: [],
      loading: false,
      error: errorMessage,
      refetch: jest.fn()
    })

    const { getByText } = renderWithTheme(<LanguageSelect />)

    expect(getByText(`Failed to load languages: ${errorMessage}`)).toBeTruthy()
  })

  it('should render language options when data is loaded', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const { getByText } = renderWithTheme(<LanguageSelect />)

    expect(getByText('Available languages:')).toBeTruthy()
    expect(getByText('English')).toBeTruthy()
    expect(getByText('中文')).toBeTruthy()
    expect(getByText('Español')).toBeTruthy()
  })

  it('should display selected language in text field', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const { getByDisplayValue } = renderWithTheme(<LanguageSelect value="zh" />)

    expect(getByDisplayValue('中文')).toBeTruthy()
  })

  it('should call onValueChange when language is selected', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const onValueChange = jest.fn()
    const { getByText } = renderWithTheme(
      <LanguageSelect onValueChange={onValueChange} />
    )

    fireEvent.press(getByText('English'))
    expect(onValueChange).toHaveBeenCalledWith('en')
  })

  it('should display custom label', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const { getByText } = renderWithTheme(
      <LanguageSelect label="Preferred Language" />
    )

    expect(getByText('Preferred Language')).toBeTruthy()
  })

  it('should display custom placeholder', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const { getByPlaceholderText } = renderWithTheme(
      <LanguageSelect placeholder="Choose your language" />
    )

    expect(getByPlaceholderText('Choose your language')).toBeTruthy()
  })

  it('should display error message when provided', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const { getByText } = renderWithTheme(
      <LanguageSelect error="Please select a language" />
    )

    expect(getByText('Please select a language')).toBeTruthy()
  })

  it('should be disabled when disabled prop is true', () => {
    mockUseLanguage.mockReturnValue({
      languages: mockLanguages,
      loading: false,
      error: null,
      refetch: jest.fn()
    })

    const onValueChange = jest.fn()
    const { getByText } = renderWithTheme(
      <LanguageSelect disabled={true} onValueChange={onValueChange} />
    )

    // The TextField should not be editable when disabled
    const textField = getByText('Available languages:').parent?.parent
      ?.children[1]
    expect(textField).toBeTruthy()
  })
})
