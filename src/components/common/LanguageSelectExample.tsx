import React, { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'

import { Button } from '@/components/Button'

import { LanguageSelect } from './LanguageSelect'

/**
 * Example component showing how to use useLanguage hook in a form
 */
export function LanguageSelectExample() {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('')
  const [formError, setFormError] = useState<string>('')

  const handleSubmit = () => {
    if (!selectedLanguage) {
      setFormError('Please select a language')
      return
    }

    setFormError('')
    console.log('Selected language:', selectedLanguage)
    // Here you would typically submit the form data
  }

  const handleLanguageChange = (value: string) => {
    setSelectedLanguage(value)
    if (formError) {
      setFormError('')
    }
  }

  return (
    <ScrollView className="bg-gray-50 flex-1">
      <View className="p-4">
        <Text className="mb-6 text-2xl font-bold text-gray-900">
          Language Selection Example
        </Text>

        <View className="rounded-lg bg-white p-4 shadow-sm">
          <Text className="mb-4 text-lg font-semibold text-gray-800">
            User Profile Form
          </Text>

          <LanguageSelect
            value={selectedLanguage}
            onValueChange={handleLanguageChange}
            label="Preferred Language"
            placeholder="Select your preferred language"
            error={formError}
          />

          <View className="mt-6">
            <Button onPress={handleSubmit} className="w-full">
              Submit Form
            </Button>
          </View>

          {selectedLanguage && (
            <View className="mt-4 rounded-lg bg-blue-50 p-3">
              <Text className="text-blue-800">
                Selected: {selectedLanguage}
              </Text>
            </View>
          )}
        </View>

        <View className="mt-6 rounded-lg bg-white p-4 shadow-sm">
          <Text className="mb-4 text-lg font-semibold text-gray-800">
            Usage Instructions
          </Text>

          <Text className="mb-2 text-gray-600">
            • The useLanguage hook automatically fetches language data from the
            API
          </Text>
          <Text className="mb-2 text-gray-600">
            • It handles loading states and error handling
          </Text>
          <Text className="mb-2 text-gray-600">
            • The component provides a clean interface for language selection
          </Text>
          <Text className="mb-2 text-gray-600">
            • Supports form validation and error display
          </Text>
          <Text className="text-gray-600">
            • Can be easily integrated into any form component
          </Text>
        </View>
      </View>
    </ScrollView>
  )
}
