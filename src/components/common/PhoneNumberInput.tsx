import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Text, TextInput, View } from 'react-native'

import classNames from '@/utils/classname'

import { CountryCodePicker } from './CountryCodePicker'

interface CountryCode {
  code: string
  name: string
  dialCode: string
  flag: string
}

export type PhoneNumberInputProps = {
  showValidate?: boolean
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  error?: string
  disabled?: boolean
  onValidationChange?: (isValid: boolean) => void
} & React.ComponentProps<typeof TextInput>

const defaultCountry: CountryCode = {
  code: 'US',
  name: 'United States',
  dialCode: '+1',
  flag: '🇺🇸'
}

function PhoneNumberInputComponent({
  value,
  onChange,
  showValidate = false,
  placeholder = 'Phone Number',
  error,
  disabled = false,
  onValidationChange,
  ...props
}: PhoneNumberInputProps) {
  const [selectedCountry, setSelectedCountry] =
    useState<CountryCode>(defaultCountry)
  const [phoneNumber, setPhoneNumber] = useState('')
  const [isValid, setIsValid] = useState(false)
  const isUserInputRef = useRef(false)

  const findCountryByDialCode = useCallback(
    (phoneNumber: string): CountryCode | null => {
      const countries = [
        { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸' },
        { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳' },
        { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦' },
        { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧' },
        { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺' },
        { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪' },
        { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷' },
        { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵' },
        { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷' },
        { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬' },
        { code: 'HK', name: 'Hong Kong', dialCode: '+852', flag: '🇭🇰' },
        { code: 'TW', name: 'Taiwan', dialCode: '+886', flag: '🇹🇼' },
        { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳' },
        { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷' },
        { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽' }
      ]

      for (const country of countries) {
        if (phoneNumber.startsWith(country.dialCode)) {
          return country
        }
      }
      return null
    },
    []
  )

  // Parse initial value to separate country code and phone number
  useEffect(() => {
    // Only update from external value changes, not from user input
    if (!isUserInputRef.current) {
      if (value) {
        // Try to find country code in the value
        const country = findCountryByDialCode(value)
        if (country) {
          setSelectedCountry(country)
          const phone = value.replace(country.dialCode, '').trim()
          setPhoneNumber(phone)
        } else {
          setPhoneNumber(value)
        }
      } else {
        // Reset to default when value is empty
        setPhoneNumber('')
        setSelectedCountry(defaultCountry)
      }
    }
    // Reset the flag after processing
    isUserInputRef.current = false
  }, [value, findCountryByDialCode])

  // Memoize the full phone number to prevent unnecessary updates
  const fullPhoneNumber = useMemo(() => {
    return selectedCountry.dialCode + phoneNumber
  }, [selectedCountry.dialCode, phoneNumber])

  const validatePhoneNumber = useCallback(
    (phoneNumber: string): boolean => {
      // Remove all spaces
      const raw = phoneNumber.replace(/\s+/g, '')
      // Match country code
      let numberWithoutCode = raw
      if (
        selectedCountry.dialCode &&
        raw.startsWith(selectedCountry.dialCode)
      ) {
        numberWithoutCode = raw.slice(selectedCountry.dialCode.length)
      } else {
        // Compatible with +086 format
        numberWithoutCode = raw.replace(/^\+0*86/, '')
      }
      numberWithoutCode = numberWithoutCode.trim()

      // console.log('dialCode:', selectedCountry.dialCode, 'number:', numberWithoutCode)

      if (selectedCountry.dialCode === '+86') {
        // Must be 11 digits starting with 1
        return /^1\d{10}$/.test(numberWithoutCode)
      }
      // Other countries: 7-15 digits
      if (numberWithoutCode.length < 7 || numberWithoutCode.length > 15) {
        return false
      }
      if (!/^\d+$/.test(numberWithoutCode)) {
        return false
      }
      return true
    },
    [selectedCountry.dialCode]
  )

  // Validate phone number
  useEffect(() => {
    if (phoneNumber) {
      const valid = validatePhoneNumber(fullPhoneNumber)
      setIsValid(valid)
      onValidationChange?.(valid)
    } else {
      setIsValid(false)
      onValidationChange?.(false)
    }
  }, [fullPhoneNumber, phoneNumber, onValidationChange, validatePhoneNumber])

  // Update parent when user changes the phone number (not when value prop changes)
  useEffect(() => {
    // Only call onChange when the user is actively changing the phone number
    // Don't call it during initial value parsing to avoid infinite loops
    if (phoneNumber && fullPhoneNumber !== value) {
      onChange?.(fullPhoneNumber)
    }
  }, [selectedCountry.dialCode, phoneNumber]) // Remove fullPhoneNumber, onChange, and value from deps

  const handlePhoneNumberChange = useCallback((text: string) => {
    // Mark as user input to prevent infinite loops
    isUserInputRef.current = true
    // Remove any non-digit characters except +
    const cleaned = text.replace(/[^\d]/g, '')
    setPhoneNumber(cleaned)
  }, [])

  const handleCountryChange = useCallback((country: CountryCode) => {
    // Mark as user input to prevent infinite loops
    isUserInputRef.current = true
    setSelectedCountry(country)
  }, [])

  return (
    <View>
      <View className="flex-row">
        <CountryCodePicker
          selectedCountry={selectedCountry}
          onSelectCountry={handleCountryChange}
          disabled={disabled}
        />
        <TextInput
          className={classNames(
            'bg-gray-50 flex-1 rounded-r-md border border-l-0 border-gray-200 px-3 py-2 text-base',
            error ? 'border-red-500' : '',
            disabled ? 'opacity-50' : ''
          )}
          value={phoneNumber}
          onChangeText={handlePhoneNumberChange}
          placeholder={placeholder}
          placeholderTextColor="#9ca3af"
          keyboardType="phone-pad"
          editable={!disabled}
          {...props}
        />
      </View>

      {showValidate ? (
        error ? (
          <Text className="mt-1 text-sm text-red-500">{error}</Text>
        ) : phoneNumber ? (
          <Text
            className={classNames(
              'mt-1 text-sm',
              isValid ? 'text-green-600' : 'text-orange-500'
            )}
          >
            {isValid
              ? '✓ Valid phone number'
              : '⚠ Please check the phone number'}
          </Text>
        ) : null
      ) : null}
    </View>
  )
}

// Use React.memo to prevent unnecessary re-renders
export const PhoneNumberInput = React.memo(PhoneNumberInputComponent)
