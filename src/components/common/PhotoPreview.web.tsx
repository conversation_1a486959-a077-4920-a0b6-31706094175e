import { PhotoSlider } from 'react-photo-view'

import type { PhotoPreviewProps } from './PhotoPreview'

import 'react-photo-view/dist/react-photo-view.css'

const PhotoPreview = ({
  urls,
  index,
  visible = false,
  onVisibleChange
}: PhotoPreviewProps) => {
  return (
    <PhotoSlider
      images={urls.map((item, index) => ({
        src: item.url,
        key: `${item.url}${index}`
      }))}
      visible={visible}
      onClose={() => onVisibleChange?.(false)}
      index={index}
    />
  )
}

export default PhotoPreview
