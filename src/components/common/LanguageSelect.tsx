import React from 'react'
import { ActivityIndicator, Text, View } from 'react-native'

import { ListItem } from '@/components/ListItem'
import { TextField } from '@/components/TextField'
import { useLanguage } from '@/hooks/useLanguage'
import classNames from '@/utils/classname'

interface LanguageSelectProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  label?: string
  error?: string
  disabled?: boolean
}

/**
 * Language select component using useLanguage hook
 * Displays a dropdown of available languages from the API
 */
export function LanguageSelect({
  value,
  onValueChange,
  placeholder = 'Select language',
  label = 'Language',
  error,
  disabled = false
}: LanguageSelectProps) {
  const { languages, loading, error: fetchError } = useLanguage()

  if (loading) {
    return (
      <View className="flex-row items-center justify-center p-4">
        <ActivityIndicator size="small" />
        <Text className="text-gray-500 ml-2">Loading languages...</Text>
      </View>
    )
  }

  if (fetchError) {
    return (
      <View className="p-4">
        <Text className="text-red-500">
          Failed to load languages: {fetchError}
        </Text>
      </View>
    )
  }

  return (
    <View>
      {label && (
        <Text className="mb-2 text-sm font-medium text-gray-700">{label}</Text>
      )}

      <TextField
        value={
          value
            ? languages.find(lang => lang.code === value)?.label || value
            : ''
        }
        placeholder={placeholder}
        editable={!disabled}
        onPressIn={() => {
          if (!disabled) {
            // This would typically open a modal or dropdown
            // For now, we'll just show the available options
            console.log('Available languages:', languages)
          }
        }}
      />

      {error && <Text className="mt-1 text-sm text-red-500">{error}</Text>}

      {/* Display available languages for demo purposes */}
      {languages.length > 0 && (
        <View className="mt-2">
          <Text className="text-gray-500 mb-1 text-xs">
            Available languages:
          </Text>
          {languages.map(language => (
            <ListItem
              key={language.code}
              text={language.label || ''}
              leftIcon="components"
              onPress={() => onValueChange?.(language.code || '')}
              className={classNames(
                'py-2',
                value === language.code && 'bg-blue-50'
              )}
            />
          ))}
        </View>
      )}
    </View>
  )
}
