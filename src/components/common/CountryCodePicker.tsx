import React, { useCallback, useState } from 'react'
import { FlatList, Modal, Pressable, Text, View } from 'react-native'
import { ChevronDownIcon } from 'react-native-heroicons/outline'

import classNames from '@/utils/classname'

interface CountryCode {
  code: string
  name: string
  dialCode: string
  flag: string
}

const countryCodes: CountryCode[] = [
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸' },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷' },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬' },
  { code: 'HK', name: 'Hong Kong', dialCode: '+852', flag: '🇭🇰' },
  { code: 'TW', name: 'Taiwan', dialCode: '+886', flag: '🇹🇼' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳' },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷' },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽' }
]

interface CountryCodePickerProps {
  selectedCountry: CountryCode
  onSelectCountry: (country: CountryCode) => void
  disabled?: boolean
}

function CountryCodePickerComponent({
  selectedCountry,
  onSelectCountry,
  disabled = false
}: CountryCodePickerProps) {
  const [modalVisible, setModalVisible] = useState(false)

  const handleSelectCountry = useCallback(
    (country: CountryCode) => {
      onSelectCountry(country)
      setModalVisible(false)
    },
    [onSelectCountry]
  )

  return (
    <>
      <Pressable
        className={classNames(
          'bg-gray-50 flex-row items-center justify-between rounded-l-md border border-gray-200 px-3 py-2',
          disabled ? 'opacity-50' : ''
        )}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
      >
        <View className="flex-row items-center">
          <Text className="mr-2 text-lg">{selectedCountry.flag}</Text>
          <Text className="text-base text-gray-700">
            {selectedCountry.dialCode}
          </Text>
        </View>
        <ChevronDownIcon size={16} color="#6b7280" />
      </Pressable>

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-end bg-black/50">
          <View className="max-h-96 rounded-t-3xl bg-white">
            <View className="flex-row items-center justify-between border-b border-gray-200 p-4">
              <Text className="text-lg font-semibold text-gray-800">
                Select Country
              </Text>
              <Pressable onPress={() => setModalVisible(false)}>
                <Text className="text-base text-blue-500">Cancel</Text>
              </Pressable>
            </View>

            <FlatList
              data={countryCodes}
              keyExtractor={item => item.code}
              renderItem={({ item }) => (
                <Pressable
                  className="flex-row items-center border-b border-gray-100 p-4"
                  onPress={() => handleSelectCountry(item)}
                >
                  <Text className="mr-3 text-xl">{item.flag}</Text>
                  <View className="flex-1">
                    <Text className="text-base font-medium text-gray-800">
                      {item.name}
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      {item.dialCode}
                    </Text>
                  </View>
                  {selectedCountry.code === item.code && (
                    <Text className="text-lg text-blue-500">✓</Text>
                  )}
                </Pressable>
              )}
            />
          </View>
        </View>
      </Modal>
    </>
  )
}

// Use React.memo to prevent unnecessary re-renders
export const CountryCodePicker = React.memo(CountryCodePickerComponent)
