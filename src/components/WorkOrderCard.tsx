import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { format } from 'date-fns'
import { Link } from 'expo-router'

import { Colors } from '@/theme/colors'

import { CardWrapper } from './CardWrapper'

interface IWorkOrderCardProps {
  orderType: string
  orderId: number
  title: string
  deadline: string
  pm: string
}

export function WorkOrderCard({
  orderType = 'hammer',
  orderId,
  title,
  deadline,
  pm
}: IWorkOrderCardProps) {
  return (
    <CardWrapper>
      <View
        className={`h-[106px] flex-row items-center gap-3 rounded-lg border-l-4 border-[#4f46e5] p-4`}
      >
        <View className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#e1f0ff]">
          <FontAwesome6 name={orderType} size={16} color={Colors.primary} />
        </View>
        <View className="flex-1">
          <Text className="text-lg font-bold">{title}</Text>
          <Text className="text-sm text-gray">PM: {pm}</Text>
          {deadline && (
            <Text className="text-sm text-gray">
              Deadline: {format(new Date(deadline), 'MMM d')}
            </Text>
          )}
        </View>
        <Link href={`/vendor/project-detail/${orderId}`}>
          <View className="flex items-center justify-center rounded-lg bg-primary px-3 py-1.5">
            <Text className="text-[16px] text-white">Quote</Text>
          </View>
        </Link>
      </View>
    </CardWrapper>
  )
}

export default WorkOrderCard
