import { Dimensions, View } from 'react-native'
import { BarChart as Chart } from 'react-native-chart-kit'

import { Colors } from '@/theme/colors'

const screenWidth = Dimensions.get('window').width

const data = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [
    {
      data: [30, 50, 45, 70, 60, 0],
      colors: [
        (opacity = 1) => `rgba(74, 51, 255, ${opacity})`, // Jan
        (opacity = 1) => `rgba(74, 51, 255, ${opacity})`, // Feb
        (opacity = 1) => `rgba(74, 51, 255, ${opacity})`, // Mar
        (opacity = 1) => `rgba(74, 51, 255, ${opacity})`, // Apr
        (opacity = 1) => `rgba(255, 159, 10, ${opacity})`, // May (highlighted)
        (opacity = 0) => `rgba(0, 0, 0, ${opacity})` // Jun (invisible)
      ]
    }
  ]
}

const chartConfig = {
  backgroundGradientFrom: 'transparent',
  backgroundGradientTo: 'transparent',
  fillShadowGradientFrom: Colors.primary,
  fillShadowGradientTo: Colors.primary,
  fillShadowGradientOpacity: 1,
  color: () => 'transparent',
  labelColor: () => `#6b728`,
  barPercentage: 1
}

export default function BarChart() {
  return (
    <View>
      <Chart
        data={data}
        width={screenWidth - 16}
        height={220}
        chartConfig={chartConfig}
        flatColor={true}
        fromZero
        withInnerLines={false}
        withHorizontalLabels={false}
        showValuesOnTopOfBars={false}
        style={{
          borderRadius: 8,
          alignSelf: 'center'
        }}
        yAxisLabel=""
        yAxisSuffix=""
      />
    </View>
  )
}
