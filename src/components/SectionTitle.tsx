import { Text, View } from 'react-native'
import { cva, type VariantProps } from 'class-variance-authority'
import { Link, type LinkProps } from 'expo-router'
import type { ReactNode } from 'react'

import classNames from '@/utils/classname'

const titleVariants = cva('text-dark', {
  variants: {
    size: {
      normal: 'text-lg font-semibold',
      lg: 'text-2xl font-bold'
    }
  }
})

const linkVariants = cva('text-sm', {
  variants: {
    size: {
      normal: '',
      lg: 'underline'
    },
    variant: {
      default: 'text-primary',
      tenant: 'text-tenant'
    }
  }
})

const SectionTitle = ({
  className,
  title,
  size = 'normal',
  href,
  linkText,
  leftIcon,
  variant = 'default',
  right
}: {
  className?: string
  variant?: 'default' | 'tenant'
  size?: VariantProps<typeof titleVariants>['size']
  title: string
  href?: LinkProps['href']
  linkText?: string
  leftIcon?: ReactNode
  right?: ReactNode
}) => {
  const isShowLink = href && linkText

  return (
    <View
      className={classNames(
        'mb-3 flex-row items-center justify-between',
        className
      )}
    >
      <View className="flex-row items-center">
        {leftIcon && <View className="mr-2">{leftIcon}</View>}
        <Text className={titleVariants({ size })}>{title}</Text>
      </View>
      {isShowLink && (
        <Link href={href}>
          <Text className={linkVariants({ size, variant })}>{linkText}</Text>
        </Link>
      )}
      {right}
    </View>
  )
}

export default SectionTitle
