import {
  Pressable,
  ScrollView,
  type ScrollViewProps,
  type StyleProp,
  Text,
  type ViewStyle
} from 'react-native'
import type { ReactNode } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

export type RadioButtonGroupProps<T extends string | number> = {
  value: T
  onChange: (value: T) => void
  items: {
    label: string
    value: T
    activeColor?: string
    activeBgColor?: string
    rightNode?: (active: boolean) => ReactNode
  }[]
  className?: string
  style?: StyleProp<ViewStyle>
}

const RadioButtonGroup = <T extends string | number>({
  value,
  onChange,
  items,
  ...props
}: RadioButtonGroupProps<T> & ScrollViewProps) => {
  return (
    <ScrollView
      className={classNames(props.className)}
      horizontal
      contentContainerClassName="flex flex-row items-center gap-[10px]"
      {...props}
    >
      {items.map(item => {
        const isActive = item.value === value
        return (
          <Pressable
            key={item.value}
            testID={`radio-${item.value}`}
            className="flex flex-row items-center justify-center rounded-lg border border-solid px-[14px] py-2"
            style={{
              borderColor: isActive ? item.activeColor : Colors.border,
              backgroundColor: isActive ? item.activeBgColor : 'white'
            }}
            onPress={() => onChange?.(item.value)}
          >
            <Text
              className="text-sm"
              style={{
                color: isActive ? item.activeColor : Colors.dark
              }}
            >
              {item.label}
            </Text>
            {item.rightNode?.(isActive)}
          </Pressable>
        )
      })}
    </ScrollView>
  )
}

export { RadioButtonGroup }
