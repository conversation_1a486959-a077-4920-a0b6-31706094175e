import React from 'react'
import { Pressable, Text, View } from 'react-native'
import clsx from 'clsx'
import type { FC } from 'react'

import classNames from '@/utils/classname'

export interface CheckboxGroupItemOption {
  label: string
  value: string
  disabled?: boolean
}

export interface CheckboxGroupProps {
  value?: string[]
  onChange?: (value: string[]) => void
  options: CheckboxGroupItemOption[]
  disabled?: boolean
  className?: string
  itemClassName?: string
  labelClassName?: string
}

export const CheckboxGroup: FC<CheckboxGroupProps> = ({
  value = [],
  onChange,
  options,
  disabled = false,
  className,
  itemClassName,
  labelClassName
}) => {
  const handlePress = (option: CheckboxGroupItemOption) => {
    if (disabled || option.disabled) return
    const checked = value.includes(option.value)
    let newValue: string[]
    if (checked) {
      newValue = value.filter(v => v !== option.value)
    } else {
      newValue = [...value, option.value]
    }
    onChange?.(newValue)
  }

  return (
    <View className={clsx('flex flex-col gap-y-2', className)}>
      {options.map(option => {
        const checked = value.includes(option.value)
        return (
          <Pressable
            key={option.value}
            onPress={() => handlePress(option)}
            disabled={disabled || option.disabled}
            className={clsx(
              'flex flex-row items-center gap-2',
              disabled || option.disabled ? 'opacity-50' : '',
              itemClassName
            )}
          >
            <View
              className={classNames(
                'inline-flex h-[18px] w-[18px] items-center justify-center rounded-sm border border-gray bg-white',
                checked ? 'border-primary bg-primary' : ''
              )}
            >
              {checked && (
                <Text className="text-base font-bold text-white">✓</Text>
              )}
            </View>
            <Text className={clsx('text-sm text-gray', labelClassName)}>
              {option.label}
            </Text>
          </Pressable>
        )
      })}
    </View>
  )
}
