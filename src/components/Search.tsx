import { Pressable, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

import type { InputProps } from './Input'
import { Input } from './Input'

export function Search(
  props: InputProps & { onSearch?: (v: string) => void; showFilter?: boolean }
) {
  const { showFilter = true, ...inputProps } = props

  return (
    <View className="mb-4">
      <Input
        {...inputProps}
        returnKeyLabel="Search"
        LeftAccessory={() => (
          <FontAwesome6 name="magnifying-glass" size={16} color={Colors.gray} />
        )}
        onClear={() => {
          props.onSearch?.('')
        }}
        onSubmitEditing={() => {
          props?.onSearch?.(props.value || '')
        }}
        RightAccessory={
          showFilter
            ? () => (
                <Pressable
                  className="h-10 w-10 flex-row items-center justify-center rounded-default bg-gray-200"
                  onPress={() => {
                    props?.onSearch?.(props.value || '')
                  }}
                >
                  <FontAwesome6 name="filter" size={16} color={Colors.gray} />
                </Pressable>
              )
            : undefined
        }
      />
    </View>
  )
}
