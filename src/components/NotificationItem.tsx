import React from 'react'
import { Text, View } from 'react-native'
import { AntD<PERSON>, <PERSON>ather, FontAwesome5, Ionicons } from '@expo/vector-icons'
import type { FC } from 'react'

import classNames from '@/utils/classname'

const ICON_MAP = {
  message: {
    icon: <Feather name="message-circle" size={20} color="#3B82F6" />,
    bg: 'bg-blue-50'
  },
  reminder: {
    icon: <Feather name="tool" size={20} color="#F59E42" />,
    bg: 'bg-orange-50'
  },
  approved: {
    icon: <AntDesign name="checkcircle" size={20} color="#22C55E" />,
    bg: 'bg-green-50'
  },
  schedule: {
    icon: <Feather name="calendar" size={20} color="#3B82F6" />,
    bg: 'bg-blue-50'
  },
  review: {
    icon: <FontAwesome5 name="star" size={18} color="#EC4899" />,
    bg: 'bg-pink-50'
  },
  payment: {
    icon: <Ionicons name="card" size={20} color="#8B5CF6" />,
    bg: 'bg-violet-50'
  },
  system: {
    icon: <Ionicons name="notifications" size={20} color="#2563EB" />,
    bg: 'bg-blue-50'
  }
}

export type NotificationItemProps = {
  type: keyof typeof ICON_MAP
  title: string
  content: string
  time: string
  unread?: boolean
}

const NotificationItem: FC<NotificationItemProps> = ({
  type,
  title,
  content,
  time,
  unread = false
}) => {
  const iconInfo = ICON_MAP[type] || ICON_MAP['message']
  return (
    <View className="relative mb-2 flex-row items-start rounded-xl bg-white px-4 py-3">
      <View
        className={classNames(
          'mr-3 mt-1 h-9 w-9 items-center justify-center rounded-full',
          iconInfo.bg
        )}
      >
        {iconInfo.icon}
      </View>
      <View className="flex-1">
        <Text className="mb-0.5 text-base font-semibold text-gray-900">
          {title}
        </Text>
        <Text className="mb-2 text-sm text-gray-700" numberOfLines={2}>
          {content}
        </Text>
        <Text className="text-gray-400 text-xs">{time}</Text>
      </View>
      {unread && (
        <View className="absolute right-2 top-2 h-2 w-2 rounded-full bg-blue-500" />
      )}
    </View>
  )
}

export default NotificationItem
