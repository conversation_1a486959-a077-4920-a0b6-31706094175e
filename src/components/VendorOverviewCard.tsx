import { useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { Link } from 'expo-router'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

async function fetchOverview() {
  const { data, error } = await client.GET('/api/v1/vendor/overview')
  if (!error) {
    return { success: true, result: data }
  }
  return { success: false, result: null }
}

export const VendorOverviewCard = () => {
  const [overview, setOverview] = useState<
    components['schemas']['VendorOverViewDTO'] | null
  >()

  useEffect(() => {
    init()
  }, [])

  const init = async () => {
    const { success, result } = await fetchOverview()
    console.log(result, 'vendor overview')
    if (success) {
      // FIXME
      // @ts-expect-error
      setOverview(result)
    }
  }

  // if (!overview) {
  //   return null
  // }

  return (
    <View className="mb-4">
      <LinearGradient
        className="w-full rounded-lg p-4"
        colors={['#0074e4', '#005bb7']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View className="text-white">
          <View className="mb-1 flex-row items-center justify-between">
            <Text className="font-bold text-white">Bussiness Overview</Text>
            <Text className="text-xs text-white">this month</Text>
          </View>
          <View className="mb-4">
            <Text className="text-xl font-bold text-white">
              ${overview?.income}
            </Text>
            <View className="flex-row items-baseline gap-1">
              {overview?.incomeSr && (
                <FontAwesome6
                  name={overview.incomeSr > 0 ? 'arrow-up' : 'arrow-down'}
                  size={12}
                  color={'#fff'}
                />
              )}
              <Text className="text-white">
                {overview?.incomeSr}% from last month
              </Text>
            </View>
          </View>
          <View className="mb-4 flex-row">
            <View className="flex-1 border-r border-white/20 pb-3">
              <Text className="text-xs text-white opacity-80">
                Active Projects
              </Text>
              <Text className="text-lg font-bold text-white">
                {overview?.activeProjectCount || 0}
              </Text>
            </View>
            <View className="flex-1 pl-3 pr-3">
              <Text className="text-xs text-white opacity-80">
                Quotes Pending
              </Text>
              <Text className="text-lg font-bold text-white">
                {overview?.pendingQuotesCount || 0}
              </Text>
            </View>
            <View className="flex-1 border-l border-white/20 pl-3">
              <Text className="text-xs text-white opacity-80">
                Completion Rate
              </Text>
              <Text className="text-lg font-bold text-white">
                {overview?.completeRate}%
              </Text>
            </View>
          </View>
          <View className="flex-row items-center justify-center">
            <Link href="/vendor/analytics">
              <Text className="mr-1 text-white">View detailed analytics</Text>
              <FontAwesome6 name="chevron-right" size={10} color={'#fff'} />
            </Link>
          </View>
        </View>
      </LinearGradient>
    </View>
  )
}

export default VendorOverviewCard
