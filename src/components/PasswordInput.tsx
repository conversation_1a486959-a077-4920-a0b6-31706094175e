import { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import type { TextInput } from 'react-native'
import { Pressable } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import type { Ref } from 'react'

import { Colors } from '@/theme/colors'

import type { InputProps } from './Input'
import { Input } from './Input'

export const PasswordInput = forwardRef(function PasswordInput(
  props: Omit<InputProps, 'RightAccessory' | 'secureTextEntry'>,
  ref: Ref<TextInput>
) {
  const [secureTextEntry, setSecureTextEntry] = useState(true)
  const input = useRef<TextInput>(null)

  useImperativeHandle(ref, () => input.current as TextInput)

  return (
    <Input
      {...props}
      secureTextEntry={secureTextEntry}
      RightAccessory={() =>
        props.value ? (
          <Pressable
            onPress={() => {
              setSecureTextEntry(!secureTextEntry)
              input.current?.focus()
            }}
          >
            <FA
              name={secureTextEntry ? 'eye-slash' : 'eye'}
              size={16}
              color={Colors.gray}
            />
          </Pressable>
        ) : null
      }
    />
  )
})
