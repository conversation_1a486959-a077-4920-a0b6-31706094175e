import type { StyleProp, ViewProps, ViewStyle } from 'react-native'
import { View } from 'react-native'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

export type ProgressProps = {
  percentage: number
  color?: string
  className?: string
  radius?: number
  style?: StyleProp<ViewStyle>
}

const Progress = ({
  percentage,
  color = Colors.primary,
  className,
  radius = 2,
  ...props
}: ProgressProps & ViewProps) => {
  // Clamp percentage between 0 and 100
  const clampedPercentage = Math.min(Math.max(percentage, 0), 100)

  return (
    <View
      className={classNames(
        'relative h-1 w-full bg-[rgba(0,0,0,0.1)]',
        className
      )}
      style={{
        borderRadius: radius
      }}
      {...props}
    >
      <View
        className="h-full"
        style={{
          backgroundColor: color,
          width: `${clampedPercentage}%`,
          borderRadius: radius
        }}
      />
    </View>
  )
}

export { Progress }
