import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import type {
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollViewProps
} from 'react-native'
import {
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Text,
  View
} from 'react-native'
import { useUpdateEffect } from 'ahooks'
import type { ForwardedRef, ReactElement, ReactNode } from 'react'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

export interface InfiniteScrollProps<T, RequestArgs> extends ScrollViewProps {
  testID?: string
  initialData?: T[]
  initialLoad?: boolean
  renderItem: (item: T, index: number) => ReactNode
  requestArgs?: RequestArgs
  onRequest: (
    page: number,
    pageSize: number,
    requestArgs?: RequestArgs
  ) => Promise<{
    data: T[]
    hasMore: boolean
  }>
  pageSize?: number
  emptyComponent?: ReactNode
  loadingComponent?: ReactNode
  endComponent?: ReactNode
  className?: string
  containerClassName?: string
  itemClassName?: string
  numColumns?: number
  scrollEventThrottle?: number
  paddingToBottom?: number
  emptyText?: string
  onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void
}

export interface InfiniteScrollRef {
  refresh: () => Promise<void>
}

function InfiniteScrollBase<T, RequestArgs>(
  {
    testID,
    initialData = [],
    initialLoad = true,
    renderItem,
    onRequest,
    requestArgs,
    pageSize = 10,
    emptyComponent,
    loadingComponent,
    endComponent,
    containerClassName,
    itemClassName,
    numColumns = 1,
    scrollEventThrottle = 15,
    paddingToBottom = 50,
    onScroll,
    emptyText = 'No data available',
    ...props
  }: InfiniteScrollProps<T, RequestArgs>,
  ref: ForwardedRef<InfiniteScrollRef>
) {
  const [data, setData] = useState<T[]>(initialData)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const scrollViewRef = useRef<ScrollView>(null)

  const loadData = useCallback(
    async (pageNum: number, isRefresh = false) => {
      setLoading(true)
      try {
        const { data: newData, hasMore: newHasMore } = await onRequest(
          pageNum,
          pageSize,
          requestArgs
        )

        if (isRefresh) {
          setData(newData)
        } else {
          setData(prev => [...prev, ...newData])
        }

        setHasMore(newHasMore)
        setPage(pageNum)
      } catch (error) {
        console.error('Failed to load data:', error)
      } finally {
        setLoading(false)
      }
    },
    [onRequest, pageSize, requestArgs]
  )

  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await loadData(1, true)
    } finally {
      setRefreshing(false)
    }
  }, [loadData])

  const handleLoadMore = useCallback(async () => {
    if (loading || !hasMore) return
    await loadData(page + 1)
  }, [loading, hasMore, page, loadData])

  useImperativeHandle(
    ref,
    () => ({
      refresh: handleRefresh
    }),
    [handleRefresh]
  )

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent
      const isCloseToBottom =
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom
      if (isCloseToBottom) {
        handleLoadMore()
      }

      onScroll?.(event)
    },
    [handleLoadMore, onScroll, paddingToBottom]
  )

  const renderContent = () => {
    if (data.length === 0 && !loading && !refreshing) {
      return (
        emptyComponent || (
          <Text className="py-4 text-center text-gray">{emptyText}</Text>
        )
      )
    }

    return (
      <View
        className={classNames(
          'flex-wrap',
          numColumns > 1 ? 'flex-row justify-between' : '',
          containerClassName
        )}
      >
        {data.map((item, index) => (
          <View
            key={index}
            className={classNames(
              numColumns > 1 ? `w-[${100 / numColumns - 2}%]` : 'w-full',
              itemClassName
            )}
          >
            {renderItem(item, index)}
          </View>
        ))}
      </View>
    )
  }

  useEffect(() => {
    if (initialLoad) {
      loadData(1, true)
    }
  }, [initialLoad])

  useUpdateEffect(() => {
    if (requestArgs) {
      loadData(1, true)
    }
  }, [requestArgs])

  return (
    <ScrollView
      ref={scrollViewRef}
      testID={testID}
      className={classNames('flex-1', props.className)}
      contentContainerStyle={[
        props.contentContainerStyle,
        {
          flexGrow: 1,
          minHeight: '100%'
        }
      ]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      onScroll={handleScroll}
      scrollEventThrottle={scrollEventThrottle}
      {...props}
    >
      {renderContent()}

      {loading &&
        (loadingComponent || (
          <View className="py-4">
            <ActivityIndicator
              testID="loading-indicator"
              color={Colors.primary}
            />
          </View>
        ))}

      {!hasMore &&
        data.length > 0 &&
        (endComponent || (
          <Text className="py-4 text-center text-gray">
            No more data to load
          </Text>
        ))}
    </ScrollView>
  )
}

export const InfiniteScroll = forwardRef(InfiniteScrollBase) as <
  T,
  RequestArgs
>(
  props: InfiniteScrollProps<T, RequestArgs> & {
    ref?: ForwardedRef<InfiniteScrollRef>
  }
) => ReactElement
