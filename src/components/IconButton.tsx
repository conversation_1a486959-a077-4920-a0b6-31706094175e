import type { TouchableWithoutFeedbackProps } from 'react-native'
import { TouchableOpacity } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

export type IconButtonProps = {
  name: string
  size?: number
  color?: string
  className?: string
}

const IconButton = ({
  name,
  size = 18,
  color = Colors.dark,
  className,
  ...props
}: IconButtonProps & TouchableWithoutFeedbackProps) => {
  return (
    <TouchableOpacity
      className={classNames(
        'inline-flex items-center justify-center rounded-full',
        className
      )}
      style={{
        width: size + 8,
        height: size + 8
      }}
      {...props}
    >
      <FontAwesome6 name={name} size={size} color={color} />
    </TouchableOpacity>
  )
}

export { IconButton }
