import React, { useEffect, useState } from 'react'
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  View
} from 'react-native'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useRouter } from 'expo-router'
import type { FC } from 'react'

import { AutoImage, Text } from '@/components'
import { chatApi, type ChatSessionDTO } from '@/services/api/chat'
import { useAuth } from '@/store'

interface ChatListProps {
  onRefresh?: () => void
}

const ChatList: FC<ChatListProps> = ({ onRefresh }) => {
  const router = useRouter()
  // eslint-disable-next-line unused-imports/no-unused-vars
  const { user } = useAuth()
  const [sessions, setSessions] = useState<ChatSessionDTO[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchSessions = async () => {
    try {
      setLoading(true)
      const response = await chatApi.getUserChatSessions()
      if (response.data?.data) {
        setSessions(response.data.data)
        console.log('Fetched sessions:', response.data.data.length)
      } else {
        console.log('No sessions data in response')
        setSessions([])
      }
    } catch (error) {
      console.error('Failed to fetch chat sessions:', error)
      setSessions([])
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchSessions()
    onRefresh?.()
    setRefreshing(false)
  }

  useEffect(() => {
    fetchSessions()
  }, [])

  const handleSessionPress = (session: ChatSessionDTO) => {
    if (session.sessionId) {
      console.log('Navigating to chat session:', session.sessionId)
      // @ts-ignore - Route type definition may be incomplete
      router.push(`/property-manager/chat/${session.sessionId}`)
    } else {
      console.log('No session ID available for navigation')
    }
  }

  const renderSessionItem = ({ item }: { item: ChatSessionDTO }) => {
    const lastMessage = item.lastMessage
    const unreadCount = item.unreadCount || 0
    const isUnread = unreadCount > 0

    console.log('Rendering session item:', item.sessionId, item.sessionName)

    return (
      <TouchableOpacity
        onPress={() => {
          console.log('Session item pressed:', item.sessionId)
          handleSessionPress(item)
        }}
        className="flex-row items-center border-b border-gray-200 bg-white p-4"
        activeOpacity={0.7}
        accessibilityLabel={`Chat session ${item.sessionName || 'Unnamed'}`}
        accessibilityRole="button"
      >
        {/* Avatar */}
        <View className="relative">
          <AutoImage
            source={require('assets/images/demo/avatar-placeholder.png')}
            maxWidth={40}
            maxHeight={40}
            className="rounded-full"
          />
          {isUnread && (
            <View className="absolute -right-1 -top-1 h-4 w-4 items-center justify-center rounded-full bg-red-500">
              <Text className="text-xs font-bold text-white">
                {unreadCount > 99 ? '99+' : unreadCount}
              </Text>
            </View>
          )}
        </View>

        {/* Session info */}
        <View className="ml-3 flex-1">
          <View className="flex-row items-center justify-between">
            <Text
              className={`text-base font-medium ${
                isUnread ? 'text-gray-900' : 'text-gray-700'
              }`}
              numberOfLines={1}
            >
              {item.sessionName || 'Unnamed Session'}
            </Text>
            {lastMessage?.sentTime && (
              <Text className="text-gray-500 text-xs">
                {formatDistanceToNow(new Date(lastMessage.sentTime), {
                  addSuffix: true,
                  locale: zhCN
                })}
              </Text>
            )}
          </View>

          <View className="mt-1 flex-row items-center justify-between">
            <Text
              className={`flex-1 text-sm ${
                isUnread ? 'font-medium text-gray-900' : 'text-gray-600'
              }`}
              numberOfLines={1}
            >
              {lastMessage?.content || 'No messages yet'}
            </Text>
          </View>

          {/* Session type label */}
          <View className="mt-1 flex-row items-center">
            <View className="rounded-full bg-blue-100 px-2 py-1">
              <Text className="text-xs text-blue-600">
                {item.sessionType === 'group' ? 'Group' : 'Private'}
              </Text>
            </View>
            {item.members && item.members.length > 0 && (
              <Text className="text-gray-500 ml-2 text-xs">
                {item.members.length} members
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    )
  }

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center py-20">
      <AutoImage
        source={require('assets/images/demo/avatar-placeholder.png')}
        maxWidth={64}
        maxHeight={64}
        className="mb-4 opacity-50"
      />
      <Text className="text-gray-500 mb-2 text-base">No chat sessions</Text>
      <Text className="text-gray-400 px-8 text-center text-sm">
        Start communicating with team members or vendors
      </Text>
    </View>
  )

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className="text-gray-500 mt-4">Loading...</Text>
      </View>
    )
  }

  return (
    <View className="bg-gray-50 flex-1">
      <FlatList
        data={sessions}
        keyExtractor={item =>
          item.sessionId?.toString() || Math.random().toString()
        }
        renderItem={renderSessionItem}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={sessions.length === 0 ? { flex: 1 } : undefined}
      />
    </View>
  )
}

export default ChatList
