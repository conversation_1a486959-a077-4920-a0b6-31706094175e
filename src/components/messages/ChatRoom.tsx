import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  View
} from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import type { ComponentType, FC, ReactNode } from 'react'

import { Text } from '@/components'
import {
  chatApi,
  type ChatMessageDTO,
  type ChatSessionDTO
} from '@/services/api/chat'
import { useAuth } from '@/store'

// Import react-native-gifted-chat components using require
const GiftedChat = require('react-native-gifted-chat').GiftedChat
const Send = require('react-native-gifted-chat').Send
const SystemMessage = require('react-native-gifted-chat').SystemMessage

// Define type interfaces
interface IMessage {
  _id: string | number
  text: string
  createdAt: Date | number
  user: {
    _id: string | number
    name?: string
    avatar?: string | number
  }
  image?: string
  system?: boolean
  sent?: boolean
  received?: boolean
  pending?: boolean
  messageType?: string
  isOwnMessage?: boolean
}

interface SendProps<TMessage extends IMessage = IMessage> {
  text?: string
  onSend?: (messages: TMessage[]) => void
  label?: string
  containerStyle?: Record<string, unknown>
  textStyle?: Record<string, unknown>
  children?: ReactNode
}

interface SystemMessageProps<TMessage extends IMessage = IMessage> {
  currentMessage?: TMessage
  containerStyle?: Record<string, unknown>
  textStyle?: Record<string, unknown>
  children?: ReactNode
}

interface ChatRoomProps {
  sessionId?: number
  messages?: IMessage[]
  onSend?: (messages: IMessage[]) => void
  onLoadEarlier?: () => void
  loadEarlier?: boolean
  isLoadingEarlier?: boolean

  onPressAvatar?: (user: any) => void

  onLongPress?: (context: any, message: any) => void
  isTyping?: boolean
  bottomOffset?: number
}

const ChatRoom: FC<ChatRoomProps> = ({
  sessionId: propSessionId,
  messages: externalMessages,
  onSend: externalOnSend,
  onLoadEarlier: externalOnLoadEarlier,
  loadEarlier: externalLoadEarlier,
  isLoadingEarlier: externalIsLoadingEarlier,
  onPressAvatar,
  onLongPress,
  isTyping: externalIsTyping,
  bottomOffset
}) => {
  const params = useLocalSearchParams()
  // eslint-disable-next-line unused-imports/no-unused-vars
  const router = useRouter()
  const { user } = useAuth()
  const [messages, setMessages] = useState<IMessage[]>(externalMessages || [])
  // eslint-disable-next-line unused-imports/no-unused-vars
  const [session, setSession] = useState<ChatSessionDTO | null>(null)
  const [loading, setLoading] = useState(true)
  // eslint-disable-next-line unused-imports/no-unused-vars
  const [sending, setSending] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  const sessionId = propSessionId || Number(params.id)
  const chatRef = useRef<ComponentType | null>(null)

  // Use external messages if provided, otherwise use internal state
  const displayMessages = externalMessages || messages
  const displayLoadEarlier =
    externalLoadEarlier !== undefined ? externalLoadEarlier : hasMore
  const displayIsLoadingEarlier =
    externalIsLoadingEarlier !== undefined ? externalIsLoadingEarlier : loading
  const displayIsTyping =
    externalIsTyping !== undefined ? externalIsTyping : false

  // Convert API message to GiftedChat format
  const convertToGiftedMessage = (apiMessage: ChatMessageDTO): IMessage => {
    return {
      _id: apiMessage.messageId?.toString() || Math.random().toString(),
      text: apiMessage.content || '',
      createdAt: apiMessage.sentTime
        ? new Date(apiMessage.sentTime)
        : new Date(),
      user: {
        _id: apiMessage.senderId?.toString() || 'unknown',
        name: apiMessage.senderName || 'Unknown',
        avatar: apiMessage.senderAvatar || undefined
      },
      system: apiMessage.messageType === 'system',
      image:
        apiMessage.messageType === 'image' ? apiMessage.content : undefined,
      // Add custom properties
      messageType: apiMessage.messageType,
      isOwnMessage: apiMessage.isOwnMessage
    }
  }

  // Fetch session details
  const fetchSession = async () => {
    if (!sessionId) return

    try {
      const response = await chatApi.getChatSession(sessionId)
      if (response.data?.data) {
        setSession(response.data.data)
      }
    } catch (error) {
      console.error('Failed to fetch session:', error)
      Alert.alert('Error', 'Failed to get session information')
    }
  }

  // Fetch message list
  const fetchMessages = async (pageNum = 1, append = false) => {
    if (!sessionId) return

    try {
      setLoading(true)
      const response = await chatApi.getSessionMessages(sessionId, pageNum, 20)

      if (response.data?.data) {
        const newMessages = response.data.data.map(convertToGiftedMessage)

        if (append) {
          setMessages(prev => [...prev, ...newMessages])
        } else {
          setMessages(newMessages)
        }

        setHasMore(response.data.data.length === 20)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('Failed to fetch messages:', error)
      Alert.alert('Error', 'Failed to get messages')
    } finally {
      setLoading(false)
    }
  }

  // Send message
  const onSend = useCallback(
    async (newMessages: IMessage[] = []) => {
      if (!sessionId || !user?.userId) return

      const message = newMessages[0]
      if (!message || !message.text.trim()) return

      try {
        setSending(true)

        // Add to local first
        const sentMessages = [{ ...message, sent: true, received: true }]
        const updatedMessages = GiftedChat.append(
          messages,
          sentMessages,
          Platform.OS !== 'web'
        )
        setMessages(updatedMessages)

        // Send to server
        const response = await chatApi.sendMessage({
          sessionId,
          content: message.text,
          messageType: 'text'
        })

        if (response.data?.data) {
          // Update message status
          const sentMessage = convertToGiftedMessage(response.data.data)
          setMessages(prev =>
            prev.map(msg => (msg._id === message._id ? sentMessage : msg))
          )
        }
      } catch (error) {
        console.error('Failed to send message:', error)
        Alert.alert('Error', 'Failed to send message')

        // Remove failed message
        if (message) {
          setMessages(prev => prev.filter(msg => msg._id !== message._id))
        }
      } finally {
        setSending(false)
      }
    },
    [sessionId, user?.userId, messages]
  )

  // Load more messages
  const onLoadEarlier = useCallback(() => {
    if (hasMore && !loading) {
      fetchMessages(page + 1, true)
    }
  }, [hasMore, loading, page])

  // Mark messages as read
  const markAsRead = useCallback(async () => {
    if (!sessionId) return

    try {
      await chatApi.markMessagesAsRead(sessionId)
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }, [sessionId])

  useEffect(() => {
    if (sessionId) {
      fetchSession()
      fetchMessages(1, false)
    }
  }, [sessionId])

  useEffect(() => {
    // Mark as read when entering chat room
    if (sessionId && messages.length > 0) {
      markAsRead()
    }
  }, [sessionId, messages.length, markAsRead])

  // Custom system message
  const renderSystemMessage = useCallback(
    (props: SystemMessageProps<IMessage>) => {
      return (
        <SystemMessage
          {...props}
          containerStyle={{
            marginBottom: 15
          }}
          textStyle={{
            fontSize: 12,
            color: '#8e8e93',
            fontWeight: '400'
          }}
        />
      )
    },
    []
  )

  // Custom send button
  const renderSend = useCallback((props: SendProps<IMessage>) => {
    return (
      <Send
        {...props}
        containerStyle={{ justifyContent: 'center', paddingHorizontal: 10 }}
      >
        <Text className="font-medium text-blue-500">Send</Text>
      </Send>
    )
  }, [])

  if (loading && messages.length === 0) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className="text-gray-500 mt-4">Loading...</Text>
      </View>
    )
  }

  return (
    <KeyboardAvoidingView
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <GiftedChat
        ref={chatRef}
        messages={displayMessages}
        onSend={externalOnSend || onSend}
        user={{
          _id: user?.userId?.toString() || 'current-user',
          name: user?.userName || 'Me',
          avatar: user?.avatar || undefined
        }}
        renderSystemMessage={renderSystemMessage}
        renderSend={renderSend}
        onLoadEarlier={externalOnLoadEarlier || onLoadEarlier}
        loadEarlier={displayLoadEarlier}
        isLoadingEarlier={displayIsLoadingEarlier}
        onPressAvatar={onPressAvatar}
        onLongPress={onLongPress}
        isTyping={displayIsTyping}
        infiniteScroll={true}
        showUserAvatar={true}
        showAvatarForEveryMessage={false}
        alwaysShowSend={true}
        placeholder="Type a message..."
        textInputStyle={{
          fontSize: 16,
          lineHeight: 20
        }}
        maxComposerHeight={100}
        minComposerHeight={40}
        maxInputLength={1000}
        renderAvatarOnTop={true}
        scrollToBottom={true}
        inverted={Platform.OS !== 'web'}
        keyboardShouldPersistTaps="never"
        bottomOffset={bottomOffset}
      />
    </KeyboardAvoidingView>
  )
}

export default ChatRoom
