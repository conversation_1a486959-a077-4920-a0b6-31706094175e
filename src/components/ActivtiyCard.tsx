import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

import { CardWrapper } from './CardWrapper'

const ActivityTypeConfig = {
  Approved: {
    title: 'Quote Approved',
    icon: 'check',
    iconBackground: '#e1f0ff',
    iconColor: Colors.primary
  },
  Message: {
    title: 'New Message',
    icon: 'comment',
    iconBackground: '#fff2e3',
    iconColor: '#ff8800'
  },
  Rating: {
    title: 'New Rating',
    icon: 'star',
    iconBackground: '#e3f7e3',
    iconColor: '#28a745'
  }
}

interface ActivityCardProps {
  type?: 'Approved' | 'Message' | 'Rating'
}

export function ActivityCard({ type = 'Rating' }: ActivityCardProps) {
  return (
    <CardWrapper>
      <View className="flex-row gap-4 p-3">
        <View
          className={`flex h-9 w-9 items-center justify-center rounded-full`}
          style={{
            backgroundColor: ActivityTypeConfig[type].iconBackground
          }}
        >
          <FontAwesome6
            name={ActivityTypeConfig[type].icon}
            size={18}
            color={ActivityTypeConfig[type].iconColor}
          />
        </View>
        <View className="flex-1">
          <Text className="text-lg font-medium">
            {ActivityTypeConfig[type].title}
          </Text>
          <Text className="text-sm text-gray">xxxxx</Text>
          <Text className="text-xs text-gray">Today, 10:24 AM</Text>
        </View>
      </View>
    </CardWrapper>
  )
}

export default ActivityCard
