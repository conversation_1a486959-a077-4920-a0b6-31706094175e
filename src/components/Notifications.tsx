import React, { useCallback, useMemo, useState } from 'react'
import { ActivityIndicator, Pressable, Text, View } from 'react-native'
import {
  ArrowLeftIcon,
  EllipsisVerticalIcon
} from 'react-native-heroicons/outline'
import { FlashList } from '@shopify/flash-list'

import type { NotificationItemProps } from '@/components/NotificationItem'
import NotificationItem from '@/components/NotificationItem'
import classNames from '@/utils/classname'

const TABS = [
  { key: 'all', label: 'All' },
  { key: 'projects', label: 'Projects' },
  { key: 'messages', label: 'Messages' },
  { key: 'system', label: 'System' }
]

const GROUPED_DATA: { group: string; data: NotificationItemProps[] }[] = [
  {
    group: 'Today',
    data: [
      {
        type: 'message',
        title: 'New message from <PERSON>',
        content:
          '"Can you provide an update on the kitchen renovation project?"',
        time: '10:42 AM',
        unread: true
      },
      {
        type: 'reminder',
        title: 'Project Reminder',
        content:
          'Your scheduled task "Repaint Living Room" is due tomorrow at 9:00 AM.',
        time: '9:15 AM',
        unread: true
      },
      {
        type: 'approved',
        title: 'Quote Approved',
        content:
          '<PERSON> has approved your quote for the deck repair project.',
        time: '8:32 AM',
        unread: true
      }
    ]
  },
  {
    group: 'Yesterday',
    data: [
      {
        type: 'schedule',
        title: 'Schedule Update',
        content:
          'A new appointment has been added to your schedule: "Bathroom Plumbing" on July 4.',
        time: 'Yesterday, 3:45 PM',
        unread: false
      },
      {
        type: 'message',
        title: 'New message from Sarah Johnson',
        content:
          '"Thanks for the quick response! I\'ll let you know when I have the materials."',
        time: 'Yesterday, 1:20 PM',
        unread: false
      }
    ]
  },
  {
    group: 'Earlier this week',
    data: [
      {
        type: 'review',
        title: 'New Review',
        content:
          'Emma Davis gave you a 5-star review for the bathroom remodel project.',
        time: 'Jun 29, 2023',
        unread: false
      },
      {
        type: 'payment',
        title: 'Payment Received',
        content:
          'You received a payment of $1,250.00 for the kitchen sink installation.',
        time: 'Jun 28, 2023',
        unread: false
      },
      {
        type: 'system',
        title: 'System Notification',
        content:
          'Your vendor profile has been verified! You can now receive job requests from all users.',
        time: 'Jun 25, 2023',
        unread: false
      }
    ]
  }
]

const PAGE_SIZE = 1

const filterMap: Record<string, (n: NotificationItemProps) => boolean> = {
  all: () => true,
  projects: n => ['reminder', 'approved', 'schedule'].includes(n.type),
  messages: n => n.type === 'message',
  system: n => ['system', 'payment', 'review'].includes(n.type)
}

type NotificationListItem =
  | { itemType: 'header'; title: string }
  | (NotificationItemProps & { itemType: 'item' })

function flattenGroupedData(
  groups: { group: string; data: NotificationItemProps[] }[]
): NotificationListItem[] {
  const result: NotificationListItem[] = []
  for (const group of groups) {
    result.push({ itemType: 'header', title: group.group })
    for (const item of group.data) {
      result.push({ ...(item as NotificationItemProps), itemType: 'item' })
    }
  }
  return result
}

function filterGroupedData(
  groups: { group: string; data: NotificationItemProps[] }[],
  tab: string
) {
  const filterFn = filterMap[tab] ?? (() => true)
  if (tab === 'all') return groups
  return groups
    .map(g => ({ ...g, data: g.data.filter(filterFn) }))
    .filter(g => g.data.length > 0)
}

export default function NotificationsScreen({
  showHeader = true
}: {
  showHeader?: boolean
}) {
  const [tab, setTab] = useState('all')
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)

  const filteredGroups = useMemo(
    () => filterGroupedData(GROUPED_DATA, tab),
    [tab]
  )
  const hasMore = page * PAGE_SIZE < filteredGroups.length
  const data = useMemo(
    () => flattenGroupedData(filteredGroups.slice(0, page * PAGE_SIZE)),
    [filteredGroups, page]
  )

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return
    setLoading(true)
    await new Promise(res => setTimeout(res, 600))
    setPage(p => p + 1)
    setLoading(false)
  }, [loading, hasMore])

  const handleTab = (key: string) => {
    setTab(key)
    setPage(1)
  }

  return (
    <View className="bg-gray-50 flex-1">
      {/* Header */}
      {showHeader && (
        <View className="flex-row items-center border-b border-gray-100 bg-white px-4 pb-2 pt-4">
          <Pressable
            className="h-10 w-10 items-center justify-center rounded-xl"
            hitSlop={12}
          >
            <ArrowLeftIcon size={22} color="#222" />
          </Pressable>
          <Text className="flex-1 text-center text-lg font-semibold text-gray-900">
            Notifications
          </Text>
          <Pressable
            className="h-10 w-10 items-center justify-center rounded-xl"
            hitSlop={12}
          >
            <EllipsisVerticalIcon size={22} color="#222" />
          </Pressable>
        </View>
      )}
      {/* Tabs */}
      <View className="flex-row border-b border-gray-100 bg-white px-4 pb-2 pt-3">
        {TABS.map(t => (
          <Pressable
            key={t.key}
            onPress={() => handleTab(t.key)}
            className={classNames(
              'mr-2 rounded-lg px-4 py-1.5',
              tab === t.key
                ? 'bg-indigo-600'
                : 'border border-gray-200 bg-white'
            )}
          >
            <Text
              className={classNames(
                'text-sm font-medium',
                tab === t.key ? 'text-white' : 'text-gray-700'
              )}
            >
              {t.label}
            </Text>
          </Pressable>
        ))}
      </View>
      {/* List */}
      <FlashList
        data={data}
        renderItem={({ item, index }) => {
          if (item.itemType === 'header') {
            return (
              <View className={index === 0 ? 'pt-4' : 'pt-8'}>
                <Text className="text-gray-400 px-4 pb-2 text-xs font-semibold uppercase tracking-wider">
                  {item.title}
                </Text>
                {index !== 0 && <View className="mx-4 mb-2 h-px bg-gray-100" />}
              </View>
            )
          }
          return <NotificationItem {...item} />
        }}
        estimatedItemSize={100}
        keyExtractor={(item, idx) =>
          item.itemType === 'header'
            ? `header-${item.title}`
            : `${item.title}-${idx}`
        }
        contentContainerStyle={{ paddingBottom: 24 }}
        showsVerticalScrollIndicator={false}
        onEndReached={loadMore}
        onEndReachedThreshold={0.2}
        ListFooterComponent={
          loading ? <ActivityIndicator className="my-4" /> : null
        }
      />
    </View>
  )
}
