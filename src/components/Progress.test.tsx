import React from 'react'
import { render } from '@testing-library/react-native'

import { Progress } from './Progress'

/**
 * Progress Component Test Suite
 * Testing various functionalities and edge cases of the Progress component
 */
describe('Progress Component', () => {
  // Test basic percentage display
  it('should render with correct percentage', () => {
    const { getByTestId } = render(
      <Progress percentage={50} testID="progress-container" />
    )
    const progressBar = getByTestId('progress-container').children[0]
    expect(progressBar.props.style.width).toBe('50%')
  })

  // Test custom color
  it('should apply custom color', () => {
    const customColor = '#FF0000'
    const { getByTestId } = render(
      <Progress
        percentage={50}
        color={customColor}
        testID="progress-container"
      />
    )
    const progressBar = getByTestId('progress-container').children[0]
    expect(progressBar.props.style.backgroundColor).toBe(customColor)
  })

  // Test custom radius
  it('should apply custom border radius', () => {
    const customRadius = 8
    const { getByTestId } = render(
      <Progress
        percentage={50}
        radius={customRadius}
        testID="progress-container"
      />
    )
    const container = getByTestId('progress-container')
    const progressBar = container.children[0]
    expect(container.props.style.borderRadius).toBe(customRadius)
    expect(progressBar.props.style.borderRadius).toBe(customRadius)
  })

  // Test custom className
  it('should apply custom className', () => {
    const { getByTestId } = render(
      <Progress
        percentage={50}
        className="custom-class"
        testID="progress-container"
      />
    )
    const container = getByTestId('progress-container')
    expect(container.props.className).toContain('custom-class')
  })

  // Test edge cases
  it('should handle 0 percentage', () => {
    const { getByTestId } = render(
      <Progress percentage={0} testID="progress-container" />
    )
    const progressBar = getByTestId('progress-container').children[0]
    expect(progressBar.props.style.width).toBe('0%')
  })

  it('should handle 100 percentage', () => {
    const { getByTestId } = render(
      <Progress percentage={100} testID="progress-container" />
    )
    const progressBar = getByTestId('progress-container').children[0]
    expect(progressBar.props.style.width).toBe('100%')
  })

  it('should clamp percentage between 0 and 100', () => {
    const { getByTestId } = render(
      <Progress percentage={150} testID="progress-container" />
    )
    const progressBar = getByTestId('progress-container').children[0]
    expect(progressBar.props.style.width).toBe('100%')
  })
})
