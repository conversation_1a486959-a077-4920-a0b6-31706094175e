import React from 'react'
import { View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import type { FC } from 'react'

import { Colors } from '@/theme/colors'
import type { ProjectWorkType } from '@/types'

const Configs = {
  REHAB: {
    icon: 'hammer',
    iconBackground: ['#28c76f', '#1f9d57']
  },
  WORK_ORDER: {
    icon: 'screwdriver-wrench',
    iconBackground: ['#4f6df5', '#3b5fe2']
  }
} as const

export interface ProjectIconProps {
  type: ProjectWorkType
  iconSize?: number
  className?: string
}

export const ProjectIcon: FC<ProjectIconProps> = ({
  type,
  iconSize = 20,
  className
}) => {
  const config = Configs[type]

  return (
    <LinearGradient
      colors={config.iconBackground}
      className={className}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View className="flex h-12 w-12 flex-row items-center justify-center">
        <FontAwesome6 name={config.icon} size={iconSize} color={Colors.white} />
      </View>
    </LinearGradient>
  )
}
