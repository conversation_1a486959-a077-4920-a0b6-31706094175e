import React from 'react'
import { Pressable, Text, View } from 'react-native'
import clsx from 'clsx'
import type { FC } from 'react'

import classNames from '@/utils/classname'

import type {
  CheckboxGroupItemOption,
  CheckboxGroupProps
} from './CheckboxGroup'

export const CheckboxButtonGroup: FC<CheckboxGroupProps> = ({
  value = [],
  onChange,
  options,
  disabled = false,
  className,
  itemClassName,
  labelClassName
}) => {
  const handlePress = (option: CheckboxGroupItemOption) => {
    if (disabled || option.disabled) return
    const checked = value.includes(option.value)
    let newValue: string[]
    if (checked) {
      newValue = value.filter(v => v !== option.value)
    } else {
      newValue = [...value, option.value]
    }
    onChange?.(newValue)
  }

  return (
    <View className={clsx('flex flex-col gap-y-2', className)}>
      {options.map(option => {
        const checked = value.includes(option.value)
        return (
          <Pressable
            key={option.value}
            onPress={() => handlePress(option)}
            disabled={disabled || option.disabled}
            className={classNames(
              'flex flex-row items-center justify-between rounded-default border px-4 py-3',
              checked
                ? 'border-tenant bg-tenant-light'
                : 'border-border bg-white',
              disabled || option.disabled ? 'opacity-50' : 'active:opacity-80',
              itemClassName
            )}
          >
            <Text
              className={clsx(
                'text-sm font-semibold',
                checked ? 'text-tenant' : 'text-dark',
                labelClassName
              )}
            >
              {option.label}
            </Text>
            <View
              className={classNames(
                'flex h-[28px] w-[28px] items-center justify-center rounded-full',
                checked ? 'bg-tenant' : 'bg-tenant-light'
              )}
            >
              {checked ? (
                <Text className="text-base text-white">✓</Text>
              ) : (
                <Text className="text-base text-tenant">＋</Text>
              )}
            </View>
          </Pressable>
        )
      })}
    </View>
  )
}
