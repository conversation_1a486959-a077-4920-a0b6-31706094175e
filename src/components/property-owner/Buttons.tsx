import React from 'react'
import { View } from 'react-native'

import { Button } from './Button'

export interface ButtonsProps {
  onSave?: () => void
  onLogout?: () => void
  onDelete?: () => void
  isSaving?: boolean
}

export const Buttons = ({
  onSave,
  onLogout,
  onDelete,
  isSaving = false
}: ButtonsProps) => {
  return (
    <View className="space-y-3 p-4">
      {/* Save Changes Button */}
      <Button
        label={isSaving ? 'Saving...' : 'Save Changes'}
        iconName="save-outline"
        variant="primary"
        onPress={onSave}
        disabled={isSaving}
      />

      {/* Log Out Button */}
      <Button
        label="Log Out"
        iconName="log-out-outline"
        variant="secondary"
        onPress={onLogout}
      />

      {/* Delete Account Button */}
      <Button
        label="Delete Account"
        iconName="trash-outline"
        variant="danger"
        onPress={onDelete}
      />
    </View>
  )
}
