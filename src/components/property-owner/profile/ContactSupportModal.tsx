import React from 'react'
import { Linking, Modal, Pressable, Text, View } from 'react-native'
import {
  ArrowTopRightOnSquareIcon,
  EnvelopeIcon,
  PhoneIcon,
  XMarkIcon
} from 'react-native-heroicons/outline'

export interface ContactSupportProps {
  visible: boolean
  onClose: () => void
}

export function ContactSupportModal({ visible, onClose }: ContactSupportProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-center bg-black/40">
        <View
          className="relative w-11/12 max-w-md rounded-2xl bg-white p-0 shadow-lg"
          style={{ overflow: 'hidden' }}
        >
          <Pressable
            className="absolute right-4 top-4 z-10 p-2"
            onPress={onClose}
            hitSlop={10}
          >
            <XMarkIcon size={22} color="#6b7280" />
          </Pressable>
          <View className="px-6 pb-2 pt-6">
            <Text className="mb-1 text-center text-lg font-bold text-gray-900">
              Contact Support
            </Text>
            <Text className="mb-1 text-base font-semibold text-gray-800">
              Get in Touch
            </Text>
            <Text className="mb-5 text-center text-sm text-gray-600">
              We're here to help! Choose your preferred way to contact us.
            </Text>
          </View>
          <View className="flex-row items-start px-6 pb-4">
            <View className="mr-3 mt-1 rounded-full bg-green-100 p-2">
              <PhoneIcon size={24} color="#22c55e" />
            </View>
            <View className="flex-1">
              <Text className="mb-0.5 text-sm font-semibold text-gray-900">
                Call Us
              </Text>
              <Text className="text-xs leading-5 text-gray-600">
                1-800-PROPREHAB (Mon-Fri 9AM-6PM EST)
              </Text>
            </View>
            <Pressable
              onPress={() => Linking.openURL('tel:1-800-PROPREHAB')}
              className="ml-2 mt-1"
              hitSlop={10}
            >
              <ArrowTopRightOnSquareIcon size={18} color="#6b7280" />
            </Pressable>
          </View>
          <View className="mx-6 h-px bg-gray-200" />
          <View className="flex-row items-start px-6 pb-6 pt-4">
            <View className="mr-3 mt-1 rounded-full bg-emerald-100 p-2">
              <EnvelopeIcon size={24} color="#10b981" />
            </View>
            <View className="flex-1">
              <Text className="mb-0.5 text-sm font-semibold text-gray-900">
                Email Support
              </Text>
              <Text className="text-xs leading-5 text-gray-600">
                <EMAIL>
              </Text>
            </View>
            <Pressable
              onPress={() =>
                Linking.openURL('mailto:<EMAIL>')
              }
              className="ml-2 mt-1"
              hitSlop={10}
            >
              <ArrowTopRightOnSquareIcon size={18} color="#6b7280" />
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}
