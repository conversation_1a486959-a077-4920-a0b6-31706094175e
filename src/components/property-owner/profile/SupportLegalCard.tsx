import React, { Fragment, useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { ChevronRightIcon } from 'react-native-heroicons/outline'
import {
  ArrowLeftOnRectangleIcon,
  DocumentTextIcon,
  InformationCircleIcon,
  LifebuoyIcon,
  QuestionMarkCircleIcon,
  UserGroupIcon
} from 'react-native-heroicons/solid'

import { useAuth } from '@/store'
import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'
import { confirm } from '@/utils/confirm'

import { ContactSupportModal } from './ContactSupportModal'
import { HelpCenterModal } from './HelpCenterModal'
import { PrivacyPolicyModal } from './PrivacyPolicyModal'
import { TermsOfServiceModal } from './TermsOfServiceModal'

const items = [
  {
    key: 'help-center',
    icon: <QuestionMarkCircleIcon size={24} color="#fbbf24" />,
    iconBg: 'bg-yellow-100',
    title: 'Help Center',
    subtitle: 'FAQs and support articles',
    onPress: undefined
  },
  {
    key: 'contact-support',
    icon: <LifebuoyIcon size={24} color="#f59e42" />,
    iconBg: 'bg-orange-100',
    title: 'Contact Support',
    subtitle: 'Get help from our team',
    onPress: undefined
  },
  {
    key: 'terms',
    icon: <DocumentTextIcon size={24} color="#34d399" />,
    iconBg: 'bg-emerald-100',
    title: 'Terms of Service',
    subtitle: 'View platform terms',
    onPress: undefined
  },
  {
    key: 'privacy',
    icon: <UserGroupIcon size={24} color="#10b981" />,
    iconBg: 'bg-green-100',
    title: 'Privacy Policy',
    subtitle: 'How we protect your data',
    onPress: undefined
  },
  {
    key: 'sign-out',
    icon: <ArrowLeftOnRectangleIcon size={24} color="#f87171" />,
    iconBg: 'bg-red-100',
    title: 'Sign Out',
    subtitle: 'Logout from your account',
    onPress: undefined
  }
]

export function SupportLegalCard() {
  const logout = useAuth(state => state.logout)
  const [showHelpCenter, setShowHelpCenter] = useState(false)
  const [showContactSupport, setShowContactSupport] = useState(false)
  const [showTerms, setShowTerms] = useState(false)
  const [showPrivacy, setShowPrivacy] = useState(false)
  const itemsWithHandler = items.map(item => {
    if (item.key === 'help-center') {
      return { ...item, onPress: () => setShowHelpCenter(true) }
    }
    if (item.key === 'contact-support') {
      return { ...item, onPress: () => setShowContactSupport(true) }
    }
    if (item.key === 'terms') {
      return { ...item, onPress: () => setShowTerms(true) }
    }
    if (item.key === 'privacy') {
      return { ...item, onPress: () => setShowPrivacy(true) }
    }
    if (item.key === 'sign-out') {
      return {
        ...item,
        onPress: () => {
          confirm('Are you sure to log out?', logout)
        }
      }
    }
    return item
  })
  return (
    <View
      className="rounded-md bg-white p-4"
      // style={ShadowStyles.default}
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-4 flex-row items-center">
        <InformationCircleIcon size={18} color={Colors.primary} />
        <Text className="ml-2 text-base font-bold text-dark">
          Support & Legal
        </Text>
      </View>
      {itemsWithHandler.map((item, idx) => (
        <Fragment key={item.key}>
          <Pressable
            className="flex-row items-center py-3"
            onPress={item.onPress}
            android_ripple={{ color: '#f3f4f6' }}
          >
            <View className={`mr-3 rounded-full p-2 ${item.iconBg}`}>
              {item.icon}
            </View>
            <View className="flex-1">
              <Text className="text-sm font-semibold text-dark">
                {item.title}
              </Text>
              <Text className="text-gray-500 mt-0.5 text-xs">
                {item.subtitle}
              </Text>
            </View>
            <ChevronRightIcon size={18} color="#9ca3af" />
          </Pressable>
          {idx !== itemsWithHandler.length - 1 && (
            <View className="ml-12 h-px bg-gray-200" />
          )}
        </Fragment>
      ))}
      <HelpCenterModal
        visible={showHelpCenter}
        onClose={() => setShowHelpCenter(false)}
      />
      <ContactSupportModal
        visible={showContactSupport}
        onClose={() => setShowContactSupport(false)}
      />
      <TermsOfServiceModal
        visible={showTerms}
        onClose={() => setShowTerms(false)}
      />
      <PrivacyPolicyModal
        visible={showPrivacy}
        onClose={() => setShowPrivacy(false)}
      />
    </View>
  )
}
