import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'
export interface DocumentsLegalItem {
  title: string
  subtitle: string
  onPress?: () => void
}

export interface DocumentsLegalProps {
  items: DocumentsLegalItem[]
}

export const DocumentsLegal = ({ items }: DocumentsLegalProps) => {
  return (
    <View
      className="mb-4 rounded-xl bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-2 flex-row items-center">
        <Ionicons
          name="document-text-outline"
          size={18}
          color="#4f46e5"
          className="mr-2"
        />
        <Text className="text-base font-semibold">Documents & Legal</Text>
      </View>
      {items.map((item, idx) => (
        <TouchableOpacity
          key={item.title}
          className={
            idx !== items.length - 1
              ? 'flex-row items-center justify-between border-b border-gray-200 py-3'
              : 'flex-row items-center justify-between py-3'
          }
          onPress={item.onPress}
        >
          <View>
            <Text className="text-sm font-medium">{item.title}</Text>
            <Text className="text-gray-500 text-xs">{item.subtitle}</Text>
          </View>
          <Ionicons name="chevron-forward" size={18} color="#9ca3af" />
        </TouchableOpacity>
      ))}
    </View>
  )
}
