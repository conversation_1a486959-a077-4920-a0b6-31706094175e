import React from 'react'
import { Text, View } from 'react-native'
import { Bars3BottomLeftIcon } from 'react-native-heroicons/outline'

import { crossPlatformShadow } from '@/theme/shadow'

export function PortfolioStatsCard({
  properties = 8,
  activeProjects = 3,
  portfolioValue = '$2.4M'
}: {
  properties?: number
  activeProjects?: number
  portfolioValue?: string
}) {
  return (
    <View
      className="mb-4 flex-col rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <Bars3BottomLeftIcon size={20} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Portfolio Stats
        </Text>
      </View>
      <View className="flex-row items-center justify-between">
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-green-600">{properties}</Text>
          <Text className="text-gray-400 mt-1 text-xs">Properties</Text>
        </View>
        <View className="mx-2 h-8 w-px bg-gray-200" />
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-green-500">
            {activeProjects}
          </Text>
          <Text className="text-gray-400 mt-1 text-xs">Active Projects</Text>
        </View>
        <View className="mx-2 h-8 w-px bg-gray-200" />
        <View className="flex-1 items-center">
          <Text className="text-lg font-bold text-amber-500">
            {portfolioValue}
          </Text>
          <Text className="text-gray-400 mt-1 text-xs">Portfolio Value</Text>
        </View>
      </View>
    </View>
  )
}
