import React from 'react'
import { Text, TextInput, View } from 'react-native'
import { Picker } from '@react-native-picker/picker'
import { Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'
export interface InvestmentPreferenceProps {
  propertyType: string
  onPropertyTypeChange: (value: string) => void
  investmentGoal: string
  onInvestmentGoalChange: (value: string) => void
  riskTolerance: string
  onRiskToleranceChange: (value: string) => void
  targetRoi: string
  onTargetRoiChange: (value: string) => void
}

export const InvestmentPreferenceCard = ({
  propertyType,
  onPropertyTypeChange,
  investmentGoal,
  onInvestmentGoalChange,
  riskTolerance,
  onRiskToleranceChange,
  targetRoi,
  onTargetRoiChange
}: InvestmentPreferenceProps) => {
  return (
    <View
      className="mb-4 rounded-xl bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-2 flex-row items-center">
        <Ionicons
          name="stats-chart-outline"
          size={18}
          color="#4f46e5"
          className="mr-2"
        />
        <Text className="text-base font-semibold">Investment Preferences</Text>
      </View>
      {/* Property Type */}
      <View className="mb-3">
        <Text className="mb-1 text-xs font-medium text-gray-700">
          Preferred Property Types
        </Text>
        <View className="rounded-md bg-gray-100">
          <Picker
            selectedValue={propertyType}
            onValueChange={onPropertyTypeChange}
            style={{ height: 40 }}
            dropdownIconColor="#4f46e5"
          >
            <Picker.Item label="All Property Types" value="all" />
            <Picker.Item label="Residential Properties" value="residential" />
            <Picker.Item label="Commercial Properties" value="commercial" />
            <Picker.Item label="Mixed Use Properties" value="mixed" />
          </Picker>
        </View>
      </View>
      {/* Investment Goal */}
      <View className="mb-3">
        <Text className="mb-1 text-xs font-medium text-gray-700">
          Investment Goal
        </Text>
        <View className="rounded-md bg-gray-100">
          <Picker
            selectedValue={investmentGoal}
            onValueChange={onInvestmentGoalChange}
            style={{ height: 40 }}
            dropdownIconColor="#4f46e5"
          >
            <Picker.Item label="Passive Income" value="passive-income" />
            <Picker.Item
              label="Capital Appreciation"
              value="capital-appreciation"
            />
            <Picker.Item label="Tax Benefits" value="tax-benefits" />
            <Picker.Item
              label="Portfolio Diversification"
              value="portfolio-diversification"
            />
          </Picker>
        </View>
      </View>
      {/* Risk Tolerance */}
      <View className="mb-3">
        <Text className="mb-1 text-xs font-medium text-gray-700">
          Risk Tolerance
        </Text>
        <View className="rounded-md bg-gray-100">
          <Picker
            selectedValue={riskTolerance}
            onValueChange={onRiskToleranceChange}
            style={{ height: 40 }}
            dropdownIconColor="#4f46e5"
          >
            <Picker.Item label="Conservative" value="conservative" />
            <Picker.Item label="Moderate" value="moderate" />
            <Picker.Item label="Aggressive" value="aggressive" />
          </Picker>
        </View>
      </View>
      {/* Target ROI */}
      <View className="mb-1">
        <Text className="mb-1 text-xs font-medium text-gray-700">
          Target ROI (%)
        </Text>
        <TextInput
          className="rounded-md bg-gray-100 px-3 py-2 text-sm"
          keyboardType="numeric"
          value={targetRoi}
          onChangeText={onTargetRoiChange}
          placeholder="8"
        />
      </View>
      <Text className="text-gray-500 mt-1 text-xs">
        Your minimum acceptable rate of return on investment
      </Text>
    </View>
  )
}
