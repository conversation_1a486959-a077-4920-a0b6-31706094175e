import React from 'react'
import { Modal, Pressable, Text, View } from 'react-native'

export interface ActionSheetModalProps {
  visible: boolean
  onClose: () => void
  onAction: (action: 'choose' | 'take' | 'cancel') => void
}

export function ActionSheetModal({
  visible,
  onClose,
  onAction
}: ActionSheetModalProps) {
  if (!visible) return null
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <Pressable className="flex-1 bg-black/30" onPress={onClose} />
      <View className="absolute bottom-0 left-0 right-0 rounded-t-2xl bg-white p-4 shadow-xl">
        <Pressable
          className="mb-2 w-full items-center justify-center rounded-lg bg-gray-100 py-3"
          onPress={() => onAction('choose')}
        >
          <Text className="text-base font-semibold text-gray-900">
            Choose from Photos
          </Text>
        </Pressable>
        <Pressable
          className="mb-2 w-full items-center justify-center rounded-lg bg-gray-100 py-3"
          onPress={() => onAction('take')}
        >
          <Text className="text-base font-semibold text-gray-900">
            Take a Photo
          </Text>
        </Pressable>
        <Pressable
          className="w-full items-center justify-center rounded-lg border border-gray-200 bg-white py-3"
          onPress={() => onAction('cancel')}
        >
          <Text className="text-gray-500 text-base font-semibold">Cancel</Text>
        </Pressable>
      </View>
    </Modal>
  )
}
