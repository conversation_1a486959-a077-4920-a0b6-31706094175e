import React, { useCallback, useEffect, useMemo, useState } from 'react'
import {
  ActivityIndicator,
  Pressable,
  Text,
  TextInput,
  View
} from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'
import { Toast } from 'toastify-react-native'

import { PhoneNumberInput } from '@/components/common/PhoneNumberInput'
import { client } from '@/services/api'
import { useAuth } from '@/store'
import classNames from '@/utils/classname'

interface EditProfileModalProps {
  visible?: boolean
  name?: string
  phone?: string
  location?: string
  focus?: string
  onChange?: (field: string, value: string) => void
  onCancel?: () => void
  onSave?: () => void
  onClose?: () => void
  onRefresh?: () => void
}

function EditProfileModalComponent({
  visible = true,
  name = '',
  phone = '',
  location = '',
  focus = '',
  onChange,
  onCancel,
  onSave,
  onClose,
  onRefresh
}: EditProfileModalProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [phoneValid, setPhoneValid] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const { user, refreshMe } = useAuth()

  // Use useMemo to prevent unnecessary re-renders
  const initialFormData = useMemo(
    () => ({
      name: name || '',
      phone: phone || '',
      location: location || '',
      focus: focus || ''
    }),
    [name, phone, location, focus]
  )

  // Local state to manage form data
  const [formData, setFormData] = useState(initialFormData)

  // Sync local state when props change, but only when modal becomes visible
  useEffect(() => {
    if (visible) {
      setFormData(initialFormData)
    }
  }, [visible, initialFormData])

  // Reset error state when modal opens
  useEffect(() => {
    if (visible) {
      setErrors({})
      setPhoneValid(false)
      setIsSaving(false)
    }
  }, [visible])

  const handleFieldChange = useCallback(
    (field: string, value: string) => {
      // Update local state
      setFormData(prev => ({ ...prev, [field]: value }))

      // Clear error
      setErrors(prev => {
        if (prev[field]) {
          return { ...prev, [field]: '' }
        }
        return prev
      })

      // Notify parent component
      onChange?.(field, value)
    },
    [onChange]
  )

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!phoneValid) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData, phoneValid])

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return
    }

    if (!user?.userId) {
      Toast.error('User information not available')
      return
    }

    setIsSaving(true)

    try {
      // Call PATCH /api/v1/property-owner/profile/{ownerId}
      const profileResponse = await client.PATCH(
        '/api/v1/property-owner/profile/{ownerId}',
        {
          params: {
            path: {
              ownerId: user.userId
            }
          },
          body: {
            address: formData.location,
            investmentGoal: formData.focus
          }
        }
      )

      if (profileResponse.error) {
        console.error('Profile update error:', profileResponse.error)
        Toast.error('Failed to update profile information')
        return
      }

      // Call PATCH /api/v1/admin/user/me
      const userResponse = await client.PATCH('/api/v1/admin/user/me', {
        body: {
          userName: formData.name,
          phoneNumber: formData.phone
        }
      })

      if (userResponse.error) {
        console.error('User update error:', userResponse.error)
        Toast.error('Failed to update user information')
        return
      }

      // Refresh user information
      const refreshedUser = await refreshMe()
      if (refreshedUser) {
        Toast.success('Profile updated successfully')
        onRefresh?.()
        onSave?.()
      } else {
        Toast.error('Profile updated but failed to refresh user information')
      }
    } catch (error) {
      console.error('Save error:', error)
      Toast.error('An unexpected error occurred')
    } finally {
      setIsSaving(false)
    }
  }, [validateForm, user?.userId, formData, refreshMe, onRefresh, onSave])

  if (!visible) return null

  return (
    <View className="absolute inset-0 z-50 items-center justify-start bg-black/20 px-2 pt-8">
      <View className="w-full max-w-md rounded-2xl bg-white shadow-xl">
        {/* Header */}
        <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
          <Text className="text-lg font-bold text-dark">Edit Profile</Text>
          <Pressable
            onPress={onClose}
            className="p-1"
            disabled={isSaving}
            testID="close-button"
          >
            <XMarkIcon size={22} color="#6b7280" />
          </Pressable>
        </View>
        {/* Form */}
        <View className="px-6 pb-4 pt-2">
          <View className="mb-3">
            <Text className="mb-1 text-sm font-semibold text-dark">
              Full Name
            </Text>
            <TextInput
              className={classNames(
                'bg-gray-50 rounded-md border px-3 py-2 text-base',
                errors.name ? 'border-red-500' : 'border-gray-200'
              )}
              value={formData.name}
              onChangeText={v => handleFieldChange('name', v)}
              placeholder="Full Name"
              placeholderTextColor="#9ca3af"
              editable={!isSaving}
            />
            {errors.name ? (
              <Text className="mt-1 text-sm text-red-500">{errors.name}</Text>
            ) : null}
          </View>
          <View className="mb-3">
            <Text className="mb-1 text-sm font-semibold text-dark">
              Phone Number
            </Text>
            <PhoneNumberInput
              value={formData.phone}
              onChange={v => handleFieldChange('phone', v as string)}
              placeholder="Enter phone number"
              error={errors.phone}
              onValidationChange={setPhoneValid}
              disabled={isSaving}
              showValidate
            />
          </View>
          <View className="mb-3">
            <Text className="mb-1 text-sm font-semibold text-dark">
              Location
            </Text>
            <TextInput
              className={classNames(
                'bg-gray-50 rounded-md border px-3 py-2 text-base',
                errors.location ? 'border-red-500' : 'border-gray-200'
              )}
              value={formData.location}
              onChangeText={v => handleFieldChange('location', v)}
              placeholder="Location"
              placeholderTextColor="#9ca3af"
              editable={!isSaving}
            />
            {errors.location ? (
              <Text className="mt-1 text-sm text-red-500">
                {errors.location}
              </Text>
            ) : null}
          </View>
          <View className="mb-3">
            <Text className="mb-1 text-sm font-semibold text-dark">
              Investment Focus
            </Text>
            <TextInput
              className="bg-gray-50 rounded-md border border-gray-200 px-3 py-2 text-base"
              value={formData.focus}
              onChangeText={v => handleFieldChange('focus', v)}
              placeholder="Investment Focus"
              placeholderTextColor="#9ca3af"
              editable={!isSaving}
            />
          </View>
        </View>
        {/* Footer */}
        <View className="flex-row items-center justify-between gap-2 rounded-b-2xl border-t border-gray-200 bg-white px-6 py-4">
          <Pressable
            className="flex-1 items-center justify-center rounded-md border border-gray-200 bg-white py-2"
            onPress={onCancel}
            disabled={isSaving}
          >
            <Text className="text-gray-500 text-base font-semibold">
              Cancel
            </Text>
          </Pressable>
          <Pressable
            className="flex-1 items-center justify-center rounded-md bg-gray-200 py-2"
            onPress={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <View className="flex-row items-center">
                <ActivityIndicator size="small" color="#374151" />
                <Text className="ml-2 text-base font-semibold text-gray-700">
                  Saving...
                </Text>
              </View>
            ) : (
              <Text className="text-base font-semibold text-gray-900">
                Save Changes
              </Text>
            )}
          </Pressable>
        </View>
      </View>
    </View>
  )
}

// Use React.memo to prevent unnecessary re-renders
export const EditProfileModal = React.memo(EditProfileModalComponent)
