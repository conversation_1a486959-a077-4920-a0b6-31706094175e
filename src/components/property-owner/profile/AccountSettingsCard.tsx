import React, { Fragment, useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { ChevronRightIcon } from 'react-native-heroicons/outline'
import {
  BellIcon,
  Cog6ToothIcon,
  ShieldCheckIcon
} from 'react-native-heroicons/solid'

import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'

import { NotificationSettingsModal } from './NotificationSettingsModal'
import { PrivacySecurityModal } from './PrivacySecurityModal'

export function AccountSettingsCard() {
  const [showNotificationModal, setShowNotificationModal] = useState(false)
  const [showPrivacySecurityModal, setShowPrivacySecurityModal] =
    useState(false)

  const items = [
    {
      key: 'notification-settings',
      icon: <BellIcon size={24} color="#22c55e" />,
      iconBg: 'bg-green-100',
      title: 'Notification Settings',
      subtitle: 'Manage your alert preferences',
      onPress: () => setShowNotificationModal(true)
    },
    {
      key: 'privacy-security',
      icon: <ShieldCheckIcon size={24} color="#22c55e" />,
      iconBg: 'bg-green-100',
      title: 'Privacy & Security',
      subtitle: 'Password, data protection',
      onPress: () => setShowPrivacySecurityModal(true)
    }
  ]

  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-4 flex-row items-center">
        <Cog6ToothIcon size={18} color={Colors.primary} />
        <Text className="ml-2 text-base font-bold text-dark">
          Account Settings
        </Text>
      </View>
      {items.map((item, idx) => (
        <Fragment key={item.key}>
          <Pressable
            className="flex-row items-center py-3"
            onPress={item.onPress}
            android_ripple={{ color: '#f3f4f6' }}
          >
            <View className={`mr-3 rounded-full p-2 ${item.iconBg}`}>
              {item.icon}
            </View>
            <View className="flex-1">
              <Text className="text-sm font-semibold text-dark">
                {item.title}
              </Text>
              <Text className="text-gray-500 mt-0.5 text-xs">
                {item.subtitle}
              </Text>
            </View>
            <ChevronRightIcon size={18} color="#9ca3af" />
          </Pressable>
          {idx !== items.length - 1 && (
            <View className="ml-12 h-px bg-gray-200" />
          )}
        </Fragment>
      ))}
      <NotificationSettingsModal
        visible={showNotificationModal}
        onClose={() => setShowNotificationModal(false)}
      />
      <PrivacySecurityModal
        visible={showPrivacySecurityModal}
        onClose={() => setShowPrivacySecurityModal(false)}
      />
    </View>
  )
}
