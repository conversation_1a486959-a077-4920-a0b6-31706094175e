import React from 'react'
import { Text, View } from 'react-native'
import { UserIcon } from 'react-native-heroicons/solid'

import { crossPlatformShadow } from '@/theme/shadow'

export function PersonalInformation({
  name = '<PERSON>',
  phone = '(*************',
  location = 'Dallas, TX',
  focus = 'Residential Properties'
}: {
  name?: string
  phone?: string
  location?: string
  focus?: string
}) {
  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <UserIcon size={20} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Personal Information
        </Text>
      </View>
      <View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-gray-900">Full Name</Text>
          <Text className="text-gray-400 text-sm">{name}</Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-gray-900">
            Phone Number
          </Text>
          <Text className="text-gray-400 text-sm">{phone}</Text>
        </View>
        <View className="flex-row items-center justify-between border-b border-gray-100 py-2">
          <Text className="text-sm font-semibold text-gray-900">Location</Text>
          <Text className="text-gray-400 text-sm">{location}</Text>
        </View>
        <View className="flex-row items-center justify-between py-2">
          <Text className="text-sm font-semibold text-gray-900">
            Investment Focus
          </Text>
          <Text className="text-gray-400 text-sm">{focus}</Text>
        </View>
      </View>
    </View>
  )
}
