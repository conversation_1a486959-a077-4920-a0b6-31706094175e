import React from 'react'
import { fireEvent, render, waitFor } from '@testing-library/react-native'
import { Toast } from 'toastify-react-native'

import { useAuth } from '@/store'

import { EditProfileModal } from '../EditProfileModal'

// Mock dependencies
jest.mock('@/services/api', () => ({
  client: {
    PATCH: jest.fn()
  }
}))

jest.mock('@/store', () => ({
  useAuth: jest.fn()
}))

jest.mock('toastify-react-native', () => ({
  Toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

// const mockClient = client as jest.Mocked<typeof client>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('EditProfileModal', () => {
  const mockUser = {
    userId: 123,
    userName: '<PERSON> Doe',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
    avatar: 'avatar.jpg',
    role: 'property_owner'
  }

  const mockRefreshMe = jest.fn()
  const mockOnRefresh = jest.fn()
  const mockOnSave = jest.fn()
  const mockOnClose = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseAuth.mockReturnValue({
      user: mockUser,
      refreshMe: mockRefreshMe,
      accessToken: 'token',
      userRole: 'property-owner',
      ready: true,
      loading: false,
      language: 'en',
      computed: {
        logged: true,
        username: 'John Doe',
        avatar: 'avatar.jpg'
      },
      init: jest.fn(),
      setUserRole: jest.fn(),
      loginByPassword: jest.fn(),
      loginByPhone: jest.fn(),
      loginByOauth: jest.fn(),
      logout: jest.fn(),
      updateMyInfo: jest.fn(),
      updateMyPassword: jest.fn()
    })
  })

  const defaultProps = {
    visible: true,
    name: 'John Doe',
    phone: '+1234567890',
    location: 'New York',
    focus: 'Residential',
    onChange: jest.fn(),
    onCancel: mockOnCancel,
    onSave: mockOnSave,
    onClose: mockOnClose,
    onRefresh: mockOnRefresh
  }

  it('renders correctly with initial data', () => {
    const { getByText, getByDisplayValue } = render(
      <EditProfileModal {...defaultProps} />
    )

    expect(getByText('Edit Profile')).toBeTruthy()
    expect(getByDisplayValue('John Doe')).toBeTruthy()
    expect(getByDisplayValue('New York')).toBeTruthy()
    expect(getByDisplayValue('Residential')).toBeTruthy()
  })

  it('validates required fields', async () => {
    const { getByText } = render(
      <EditProfileModal {...defaultProps} name="" location="" />
    )

    const saveButton = getByText('Save Changes')
    fireEvent.press(saveButton)

    await waitFor(() => {
      expect(getByText('Full name is required')).toBeTruthy()
      expect(getByText('Location is required')).toBeTruthy()
    })
  })

  it('handles user not available error', async () => {
    mockUseAuth.mockReturnValue({
      user: null,
      refreshMe: mockRefreshMe,
      accessToken: 'token',
      userRole: 'property-owner',
      ready: true,
      loading: false,
      language: 'en',
      computed: {
        logged: false,
        username: undefined,
        avatar: undefined
      },
      init: jest.fn(),
      setUserRole: jest.fn(),
      loginByPassword: jest.fn(),
      loginByPhone: jest.fn(),
      loginByOauth: jest.fn(),
      logout: jest.fn(),
      updateMyInfo: jest.fn(),
      updateMyPassword: jest.fn()
    })

    const { getByText } = render(<EditProfileModal {...defaultProps} />)

    const saveButton = getByText('Save Changes')
    fireEvent.press(saveButton)

    await waitFor(() => {
      expect(Toast.error).toHaveBeenCalledWith('User information not available')
    })
  })

  it('calls onCancel when cancel button is pressed', () => {
    const { getByText } = render(<EditProfileModal {...defaultProps} />)

    const cancelButton = getByText('Cancel')
    fireEvent.press(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('calls onClose when close button is pressed', () => {
    const { getByTestId } = render(<EditProfileModal {...defaultProps} />)

    // Use testID to find the close button
    const closeButton = getByTestId('close-button')
    fireEvent.press(closeButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('updates form data when input changes', () => {
    const mockOnChange = jest.fn()
    const { getByDisplayValue } = render(
      <EditProfileModal {...defaultProps} onChange={mockOnChange} />
    )

    const nameInput = getByDisplayValue('John Doe')
    fireEvent.changeText(nameInput, 'Jane Doe')

    expect(mockOnChange).toHaveBeenCalledWith('name', 'Jane Doe')
  })
})
