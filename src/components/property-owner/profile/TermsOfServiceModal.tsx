import React from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'

export function TermsOfServiceModal({
  visible = true,
  onClose
}: {
  visible?: boolean
  onClose?: () => void
}) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-start bg-black/20 px-2 pt-8">
        <View className="w-full max-w-md rounded-2xl bg-white shadow-xl">
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
            <Text className="text-lg font-bold text-gray-900">
              Terms of Service
            </Text>
            <Pressable onPress={onClose} className="p-1">
              <XMarkIcon size={22} color="#6b7280" />
            </Pressable>
          </View>
          {/* Content */}
          <ScrollView className="max-h-[70vh] px-6 pb-4 pt-2">
            <Text className="mb-1 text-base font-bold text-gray-900">
              PropertyRehab Terms of Service
            </Text>
            <Text className="text-gray-500 mb-3 text-xs">
              Last updated: December 2024
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              1. Acceptance of Terms
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              By accessing and using PropertyRehab, you accept and agree to be
              bound by the terms and provision of this agreement.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              2. Use License
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              Permission is granted to temporarily use PropertyRehab for
              personal, non-commercial transitory viewing only. This is the
              grant of a license, not a transfer of title.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              3. User Accounts
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              You are responsible for safeguarding the password and for all
              activities that occur under your account. You agree to immediately
              notify us of any unauthorized use of your account.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              4. Property Management Services
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              PropertyRehab provides tools and platforms to connect property
              managers, owners, vendors, and tenants. We do not provide direct
              property management services.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              5. Vendor Relationships
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              PropertyRehab facilitates connections between users and vendors
              but is not responsible for the quality of work performed by
              vendors or contractors.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              6. Payment Terms
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              Users are responsible for all fees associated with their use of
              PropertyRehab services. Fees are non-refundable except as required
              by law.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              7. Limitation of Liability
            </Text>
            <Text className="mb-3 text-sm text-gray-800">
              PropertyRehab shall not be liable for any indirect, incidental,
              special, consequential, or punitive damages resulting from your
              use of the service.
            </Text>
            <Text className="mb-1 text-sm font-bold text-gray-900">
              8. Contact Information
            </Text>
            <Text className="mb-1 text-sm text-gray-800">
              Questions about the Terms of Service should be sent to us at
              <EMAIL>.
            </Text>
          </ScrollView>
        </View>
      </View>
    </Modal>
  )
}
