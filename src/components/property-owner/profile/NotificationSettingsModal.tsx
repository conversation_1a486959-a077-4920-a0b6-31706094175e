import React, { useState } from 'react'
import {
  ActivityIndicator,
  Modal,
  Pressable,
  ScrollView,
  Text,
  View
} from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'
import { Toast } from 'toastify-react-native'

import { client } from '@/services/api'
import { useAuth } from '@/store'

import { Switch } from '../Switch'

export interface NotificationSettingsModalProps {
  visible?: boolean
  onClose?: () => void
  onSave?: () => void
  initialSettings?: {
    projectUpdates?: boolean
    approvalRequests?: boolean
    budgetNotifications?: boolean
    pushNotifications?: boolean
    emailNotifications?: boolean
    smsNotifications?: boolean
  }
}

export function NotificationSettingsModal({
  visible = true,
  onClose,
  onSave,
  initialSettings
}: NotificationSettingsModalProps) {
  const { user } = useAuth()
  const [projectUpdates, setProjectUpdates] = useState(
    initialSettings?.projectUpdates ?? true
  )
  const [approvalRequests, setApprovalRequests] = useState(
    initialSettings?.approvalRequests ?? true
  )
  const [budgetNotifications, setBudgetNotifications] = useState(
    initialSettings?.budgetNotifications ?? true
  )
  const [pushNotifications, setPushNotifications] = useState(
    initialSettings?.pushNotifications ?? true
  )
  const [emailNotifications, setEmailNotifications] = useState(
    initialSettings?.emailNotifications ?? true
  )
  const [smsNotifications, setSmsNotifications] = useState(
    initialSettings?.smsNotifications ?? true
  )
  const [loading, setLoading] = useState(false)

  // Handle save settings
  const handleSave = async () => {
    if (!user?.userId) {
      Toast.error('User information not available')
      return
    }

    setLoading(true)

    try {
      // Note: Current API schema doesn't support notification settings directly
      // Using /api/v1/admin/user/me as a workaround until proper API is available
      const { error } = await client.PATCH(
        '/api/v1/property-owner/profile/{ownerId}',
        {
          params: {
            path: {
              ownerId: user.userId
            }
          },
          body: {
            openEmailNotice: emailNotifications,
            openPropertyAlert: smsNotifications,
            openPushNotice: pushNotifications,
            openFinancialUpdate: budgetNotifications,
            openApprovalRequest: approvalRequests,
            openProjectUpdate: projectUpdates
          }
        }
      )

      if (error) {
        Toast.error(error.message || 'Failed to update notification settings')
        return
      }

      Toast.success('Notification settings updated successfully')
      onSave?.()
      onClose?.()
    } catch (err) {
      console.error('Notification settings update error:', err)
      Toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-start bg-black/20 px-2 pt-8">
        <View className="w-full max-w-md overflow-hidden rounded-xl bg-white shadow-xl">
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
            <Text className="text-lg font-bold text-gray-900">
              Notification Settings
            </Text>
            <Pressable onPress={onClose} className="p-1">
              <XMarkIcon size={22} color="#6b7280" />
            </Pressable>
          </View>
          {/* Content */}
          <ScrollView className="max-h-[70vh] px-6 pb-4 pt-4">
            {/* Property Notifications */}
            <Text className="mb-2 text-base font-bold text-gray-900">
              Property Notifications
            </Text>
            <View className="mb-4">
              <View className="mb-1 flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    Project Updates
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Get notified when projects on your properties are updated
                  </Text>
                </View>
                <Switch
                  value={projectUpdates}
                  onChange={setProjectUpdates}
                  disabled={loading}
                />
              </View>
              <View className="mt-4 flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    Approval Requests
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Receive alerts for items requiring your approval
                  </Text>
                </View>
                <Switch
                  value={approvalRequests}
                  onChange={setApprovalRequests}
                  disabled={loading}
                />
              </View>
            </View>
            {/* Financial Alerts */}
            <Text className="mb-2 text-base font-bold text-gray-900">
              Financial Alerts
            </Text>
            <View className="mb-4">
              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    Budget Notifications
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Get notified about budget changes and overruns
                  </Text>
                </View>
                <Switch
                  value={budgetNotifications}
                  onChange={setBudgetNotifications}
                  disabled={loading}
                />
              </View>
            </View>
            {/* Communication Preferences */}
            <Text className="mb-2 text-base font-bold text-gray-900">
              Communication Preferences
            </Text>
            <View className="mb-4">
              <View className="mb-4 flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    Push Notifications
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Receive push notifications on your device
                  </Text>
                </View>
                <Switch
                  value={pushNotifications}
                  onChange={setPushNotifications}
                  disabled={loading}
                />
              </View>
              <View className="mb-4 flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    Email Notifications
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Receive notifications via email
                  </Text>
                </View>
                <Switch
                  value={emailNotifications}
                  onChange={setEmailNotifications}
                  disabled={loading}
                />
              </View>
              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="text-sm font-semibold text-gray-900">
                    SMS Notifications
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Receive urgent alerts via SMS
                  </Text>
                </View>
                <Switch
                  value={smsNotifications}
                  onChange={setSmsNotifications}
                  disabled={loading}
                />
              </View>
            </View>
          </ScrollView>
          {/* Footer */}
          <View className="rounded-b-xl border-t border-gray-200 bg-white px-6 py-4">
            <Pressable
              className={`w-full items-center justify-center rounded-md py-3 ${
                loading ? 'bg-gray-300' : 'bg-blue-600'
              }`}
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Text className="text-base font-semibold text-white">
                  Save Settings
                </Text>
              )}
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}
