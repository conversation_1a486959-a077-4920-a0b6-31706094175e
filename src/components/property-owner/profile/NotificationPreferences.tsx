import React, { useState } from 'react'
import { Text, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'

import { Switch } from '../Switch'

export const NotificationPreferences = () => {
  const [projectUpdates, setProjectUpdates] = useState(true)
  const [approvalRequests, setApprovalRequests] = useState(true)
  const [financialUpdates, setFinancialUpdates] = useState(true)
  const [propertyAlerts, setPropertyAlerts] = useState(true)
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [pushNotifications, setPushNotifications] = useState(true)

  const items = [
    {
      key: 'projectUpdates',
      title: 'Project Updates',
      subtitle: 'Receive project status changes'
    },
    {
      key: 'approvalRequests',
      title: 'Approval Requests',
      subtitle: 'Notify when approval is needed'
    },
    {
      key: 'financialUpdates',
      title: 'Financial Updates',
      subtitle: 'Income reports and expenses'
    },
    {
      key: 'propertyAlerts',
      title: 'Property Alerts',
      subtitle: 'Issues requiring attention'
    },
    {
      key: 'emailNotifications',
      title: 'Email Notifications',
      subtitle: 'Receive notifications via email'
    },
    {
      key: 'pushNotifications',
      title: 'Push Notifications',
      subtitle: 'Receive mobile push notifications'
    }
  ] as const

  return (
    <View
      className="mb-4 rounded-xl bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-2 flex-row items-center">
        <Ionicons
          name="notifications-outline"
          size={18}
          color="#4f46e5"
          className="mr-2"
        />
        <Text className="text-base font-semibold">
          Notification Preferences
        </Text>
      </View>
      {items.map((item, idx) => (
        <View
          key={item.key}
          className={
            idx !== items.length - 1
              ? 'flex-row items-center justify-between border-b border-gray-200 py-3'
              : 'flex-row items-center justify-between py-3'
          }
        >
          <View className="flex-1 pr-2">
            <Text className="text-sm font-medium">{item.title}</Text>
            <Text className="text-gray-500 mt-0.5 text-xs">
              {item.subtitle}
            </Text>
          </View>
          <Switch
            value={
              item.key === 'projectUpdates'
                ? projectUpdates
                : item.key === 'approvalRequests'
                  ? approvalRequests
                  : item.key === 'financialUpdates'
                    ? financialUpdates
                    : item.key === 'propertyAlerts'
                      ? propertyAlerts
                      : item.key === 'emailNotifications'
                        ? emailNotifications
                        : item.key === 'pushNotifications'
                          ? pushNotifications
                          : false
            }
            onChange={
              item.key === 'projectUpdates'
                ? setProjectUpdates
                : item.key === 'approvalRequests'
                  ? setApprovalRequests
                  : item.key === 'financialUpdates'
                    ? setFinancialUpdates
                    : item.key === 'propertyAlerts'
                      ? setPropertyAlerts
                      : item.key === 'emailNotifications'
                        ? setEmailNotifications
                        : item.key === 'pushNotifications'
                          ? setPushNotifications
                          : () => {}
            }
            disabled={false}
          />
        </View>
      ))}
    </View>
  )
}
