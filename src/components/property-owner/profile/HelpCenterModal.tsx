import React from 'react'
import {
  Modal,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View
} from 'react-native'
import { ChevronRightIcon, XMarkIcon } from 'react-native-heroicons/outline'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  PlayCircleIcon,
  WrenchScrewdriverIcon
} from 'react-native-heroicons/solid'

const resources = [
  {
    icon: <PlayCircleIcon size={28} color="#34d399" />,
    title: 'Getting Started as an Owner',
    desc: 'Learn how to manage your investment portfolio'
  },
  {
    icon: <ChartBarIcon size={28} color="#6ee7b7" />,
    title: 'Property Performance Tracking',
    desc: 'Monitor your property investments'
  },
  {
    icon: <WrenchScrewdriverIcon size={28} color="#34d399" />,
    title: 'Managing Rehab Projects',
    desc: 'Oversee renovations and improvements'
  },
  {
    icon: <CurrencyDollarIcon size={28} color="#6ee7b7" />,
    title: 'Financial Reporting',
    desc: 'Understanding costs and ROI'
  }
]

export function HelpCenterModal({
  visible = true,
  onClose
}: {
  visible?: boolean
  onClose?: () => void
}) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-start bg-black/20 px-2 pt-8">
        <View className="w-full max-w-md rounded-2xl bg-white shadow-xl">
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
            <Text className="text-lg font-bold text-gray-900">Help Center</Text>
            <Pressable onPress={onClose} className="p-1">
              <XMarkIcon size={22} color="#6b7280" />
            </Pressable>
          </View>
          {/* Content */}
          <ScrollView className="max-h-[70vh] px-6 pb-4 pt-4">
            <TextInput
              className="mb-4 rounded-lg border border-green-200 bg-white px-3 py-2 text-base text-gray-900"
              placeholder="Search help articles..."
              placeholderTextColor="#6ee7b7"
            />
            {/* Owner Resources */}
            <Text className="mb-2 text-base font-bold text-gray-900">
              Owner Resources
            </Text>
            {resources.map((item, idx) => (
              <View
                key={item.title}
                className={`flex-row items-center rounded-xl bg-white py-3 ${idx !== resources.length - 1 ? 'border-b border-gray-100' : ''}`}
              >
                <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-green-100">
                  {item.icon}
                </View>
                <View className="flex-1">
                  <Text className="mb-0.5 text-sm font-semibold text-gray-900">
                    {item.title}
                  </Text>
                  <Text className="text-gray-500 text-xs">{item.desc}</Text>
                </View>
                <ChevronRightIcon size={20} color="#9ca3af" />
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  )
}
