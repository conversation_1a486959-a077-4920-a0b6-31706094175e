import React, { useState } from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'
import {
  KeyIcon,
  ShieldCheckIcon,
  UserGroupIcon
} from 'react-native-heroicons/solid'

import { Switch } from '../Switch'
import { ChangePasswordModal } from './ChangePasswordModal'

export function PrivacySecurityModal({
  visible = true,
  onClose
}: {
  visible?: boolean
  onClose?: () => void
}) {
  const [showChangePwd, setShowChangePwd] = useState(false)
  const [showSavedMsg, setShowSavedMsg] = useState(false)
  const [dataCollection, setDataCollection] = useState(true)

  const handleToggle = (v: boolean) => {
    setDataCollection(v)
    setShowSavedMsg(true)
    setTimeout(() => setShowSavedMsg(false), 2000)
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-start bg-black/20 px-2 pt-8">
        {showSavedMsg && (
          <View className="absolute left-0 right-0 top-6 z-50 mx-auto w-[260px] rounded-xl bg-green-500 px-4 py-2 shadow-lg">
            <Text className="text-sm text-white">
              Privacy settings saved automatically
            </Text>
          </View>
        )}
        <View className="w-full max-w-md rounded-2xl bg-white shadow-xl">
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
            <Text className="text-lg font-bold text-gray-900">
              Privacy & Security
            </Text>
            <Pressable onPress={onClose} className="p-1">
              <XMarkIcon size={22} color="#6b7280" />
            </Pressable>
          </View>
          {/* Content */}
          <ScrollView className="max-h-[70vh] px-6 pb-4 pt-4">
            {/* Account Security Card */}
            <View className="mb-4 rounded-2xl bg-[#f5faff] p-4">
              <View className="mb-3 flex-row items-center">
                <View className="mr-3 h-10 w-10 items-center justify-center rounded-xl bg-blue-600">
                  <ShieldCheckIcon size={24} color="#fff" />
                </View>
                <View>
                  <Text className="text-base font-bold text-gray-900">
                    Account Security
                  </Text>
                  <Text className="text-gray-500 mt-0.5 text-sm">
                    Manage your password and account access
                  </Text>
                </View>
              </View>
              <Pressable
                className="mt-2 flex-row items-center justify-center rounded-lg border border-blue-600 bg-white py-2"
                onPress={() => setShowChangePwd(true)}
              >
                <KeyIcon size={18} color="#2563eb" className="mr-2" />
                <Text className="font-semibold text-blue-600">
                  Change Password
                </Text>
              </Pressable>
            </View>
            {/* Privacy Settings Card */}
            <View className="rounded-2xl bg-[#f5faff] p-4">
              <View className="mb-3 flex-row items-center">
                <View className="mr-3 h-10 w-10 items-center justify-center rounded-xl bg-green-600">
                  <UserGroupIcon size={24} color="#fff" />
                </View>
                <View>
                  <Text className="text-base font-bold text-gray-900">
                    Privacy Settings
                  </Text>
                  <Text className="text-gray-500 mt-0.5 text-sm">
                    Control how we use your data
                  </Text>
                </View>
              </View>
              <View className="mt-2 flex-row items-center justify-between rounded-xl bg-white p-4 shadow-sm">
                <View>
                  <Text className="mb-0.5 text-xs font-bold text-gray-900">
                    Data Collection
                  </Text>
                  <Text className="text-gray-500 text-xs">
                    Allow usage analytics to improve our service
                  </Text>
                </View>
                <Switch
                  value={dataCollection}
                  onChange={handleToggle}
                  disabled={false}
                />
              </View>
            </View>
          </ScrollView>
        </View>
        <ChangePasswordModal
          visible={showChangePwd}
          onClose={() => setShowChangePwd(false)}
        />
      </View>
    </Modal>
  )
}
