import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { PencilSquareIcon } from 'react-native-heroicons/outline'

export function Header({
  // eslint-disable-next-line unused-imports/no-unused-vars
  onBack,
  onEdit
}: {
  onBack?: () => void
  onEdit?: () => void
}) {
  return (
    <View
      className="flex-row items-center justify-between bg-white px-4 pb-2 pt-4"
      style={{ top: 0, zIndex: 50 }}
    >
      <View className="w-10"></View>
      {/* <Pressable
        className="h-10 w-10 items-center justify-center rounded-xl"
        onPress={onBack}
        android_ripple={{ color: '#f3f4f6', borderless: true }}
      >
        <ArrowLeftIcon size={20} color="#222" />
      </Pressable> */}
      <Text className="-ml-10 flex-1 text-center text-lg font-bold text-gray-900">
        Profile
      </Text>
      <Pressable
        className="bg-gray-50 h-10 w-10 items-center justify-center rounded-xl border border-gray-200"
        onPress={onEdit}
        android_ripple={{ color: '#f3f4f6', borderless: true }}
      >
        <PencilSquareIcon size={20} color="#222" />
      </Pressable>
    </View>
  )
}
