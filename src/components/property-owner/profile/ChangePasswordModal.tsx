import React, { useState } from 'react'
import {
  ActivityIndicator,
  Modal,
  Pressable,
  Text,
  TextInput,
  View
} from 'react-native'
import { XMarkIcon } from 'react-native-heroicons/outline'
import { Toast } from 'toastify-react-native'

import { client } from '@/services/api'
import { useAuth } from '@/store'
import { crossPlatformShadow } from '@/theme/shadow'

export interface ChangePasswordModalProps {
  visible: boolean
  onClose: () => void
  onSubmit?: (oldPwd: string, newPwd: string, confirmPwd: string) => void
}

export function ChangePasswordModal({
  visible,
  onClose
  // onSubmit
}: ChangePasswordModalProps) {
  const [oldPwd, setOldPwd] = useState('')
  const [newPwd, setNewPwd] = useState('')
  const [confirmPwd, setConfirmPwd] = useState('')
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{
    oldPwd?: string
    newPwd?: string
    confirmPwd?: string
  }>({})

  const { logout } = useAuth()

  // Password validation function
  const validatePassword = (password: string) => {
    const minLength = 8
    const hasUpperCase = /[A-Z]/.test(password)
    const hasLowerCase = /[a-z]/.test(password)
    const hasNumber = /\d/.test(password)

    if (password.length < minLength) {
      return 'Password must be at least 8 characters long'
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!hasNumber) {
      return 'Password must contain at least one number'
    }
    return null
  }

  // Clear form and errors
  const clearForm = () => {
    setOldPwd('')
    setNewPwd('')
    setConfirmPwd('')
    setErrors({})
  }

  // Handle modal close
  const handleClose = () => {
    clearForm()
    onClose()
  }

  // Validate form
  const validateForm = () => {
    const newErrors: typeof errors = {}

    if (!oldPwd.trim()) {
      newErrors.oldPwd = 'Current password is required'
    }

    const passwordError = validatePassword(newPwd)
    if (passwordError) {
      newErrors.newPwd = passwordError
    }

    if (newPwd !== confirmPwd) {
      newErrors.confirmPwd = 'Passwords do not match'
    }

    if (oldPwd === newPwd) {
      newErrors.newPwd = 'New password must be different from current password'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle password update
  const handleUpdate = async () => {
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const { error } = await client.PATCH('/api/v1/admin/user/me/password', {
        body: {
          oldPassword: oldPwd,
          newPassword: newPwd
        }
      })

      if (error) {
        Toast.error(error.message || 'Failed to update password')
        return
      }

      Toast.success('Password updated successfully. Please login again.')

      // Close modal and logout after a short delay
      setTimeout(() => {
        handleClose()
        logout(false)
      }, 1500)
    } catch (err) {
      console.error('Password update error:', err)
      Toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View className="flex-1 items-center justify-center bg-black/30 px-2">
        <View
          className="w-full max-w-md rounded-2xl bg-white"
          style={[crossPlatformShadow('lg')]}
        >
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-gray-100 px-6 py-4">
            <Text className="text-lg font-bold text-gray-900">
              Change Password
            </Text>
            <Pressable onPress={handleClose} className="p-1" disabled={loading}>
              <XMarkIcon size={22} color="#6b7280" />
            </Pressable>
          </View>
          {/* Content */}
          <View className="px-6 pb-2 pt-4">
            <Text className="mb-1 text-sm font-semibold text-gray-900">
              Current Password
            </Text>
            <TextInput
              className={`mb-1 rounded-lg border px-3 py-2 text-base text-gray-900 ${
                errors.oldPwd
                  ? 'border-red-500 bg-red-50'
                  : 'bg-gray-50 border-gray-200'
              }`}
              placeholder="Enter current password"
              placeholderTextColor="#9ca3af"
              secureTextEntry
              value={oldPwd}
              onChangeText={text => {
                setOldPwd(text)
                if (errors.oldPwd) {
                  setErrors(prev => ({ ...prev, oldPwd: undefined }))
                }
              }}
              editable={!loading}
            />
            {errors.oldPwd && (
              <Text className="mb-3 text-xs text-red-500">{errors.oldPwd}</Text>
            )}

            <Text className="mb-1 text-sm font-semibold text-gray-900">
              New Password
            </Text>
            <TextInput
              className={`mb-1 rounded-lg border px-3 py-2 text-base text-gray-900 ${
                errors.newPwd
                  ? 'border-red-500 bg-red-50'
                  : 'bg-gray-50 border-gray-200'
              }`}
              placeholder="Enter new password"
              placeholderTextColor="#9ca3af"
              secureTextEntry
              value={newPwd}
              onChangeText={text => {
                setNewPwd(text)
                if (errors.newPwd) {
                  setErrors(prev => ({ ...prev, newPwd: undefined }))
                }
              }}
              editable={!loading}
            />
            {errors.newPwd && (
              <Text className="mb-3 text-xs text-red-500">{errors.newPwd}</Text>
            )}

            <Text className="mb-1 text-sm font-semibold text-gray-900">
              Confirm New Password
            </Text>
            <TextInput
              className={`mb-1 rounded-lg border px-3 py-2 text-base text-gray-900 ${
                errors.confirmPwd
                  ? 'border-red-500 bg-red-50'
                  : 'bg-gray-50 border-gray-200'
              }`}
              placeholder="Confirm new password"
              placeholderTextColor="#9ca3af"
              secureTextEntry
              value={confirmPwd}
              onChangeText={text => {
                setConfirmPwd(text)
                if (errors.confirmPwd) {
                  setErrors(prev => ({ ...prev, confirmPwd: undefined }))
                }
              }}
              editable={!loading}
            />
            {errors.confirmPwd && (
              <Text className="mb-3 text-xs text-red-500">
                {errors.confirmPwd}
              </Text>
            )}

            <Text className="text-gray-400 mb-4 text-xs">
              Password must be at least 8 characters long and contain at least
              one uppercase letter, one lowercase letter, and one number.
            </Text>
          </View>
          {/* Footer */}
          <View className="flex-row justify-end gap-2 border-t border-gray-100 bg-white px-6 py-4">
            <Pressable
              className="flex-1 items-center justify-center rounded-lg bg-gray-100 py-3"
              onPress={handleClose}
              disabled={loading}
            >
              <Text className="text-base font-semibold text-gray-900">
                Cancel
              </Text>
            </Pressable>
            <Pressable
              className="flex-1 items-center justify-center rounded-lg bg-gray-900 py-3"
              onPress={handleUpdate}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className="text-base font-semibold text-white">
                  Update Password
                </Text>
              )}
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}
