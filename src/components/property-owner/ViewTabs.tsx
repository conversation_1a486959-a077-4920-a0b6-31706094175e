import React, { useState } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'

import classNames from '@/utils/classname'

export interface ViewTabOption {
  /**
   * Display label, e.g. 'Overview'
   */
  label: string
  /**
   * Unique value for the tab
   */
  value: string
}

export interface ViewTabsProps {
  /**
   * Tabs to display
   */
  options: ViewTabOption[]
  /**
   * Currently selected value
   */
  value: string
  /**
   * Callback when a tab is pressed
   */
  onChange: (value: string) => void
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * ViewTabs Component
 * Horizontally arranged tabs with active highlight and underline
 */
export const ViewTabs = ({
  options,
  value,
  onChange,
  className
}: ViewTabsProps) => {
  // eslint-disable-next-line unused-imports/no-unused-vars
  const [labelWidths, setLabelWidths] = useState<{ [key: string]: number }>({})

  return (
    <View
      className={classNames(
        'flex-row border-b border-gray-200 bg-white px-4',
        className
      )}
      style={{ minHeight: 44 }}
    >
      {options.map(option => {
        const isActive = value === option.value
        return (
          <TouchableOpacity
            key={option.value}
            className="flex-1 items-center justify-center"
            activeOpacity={0.7}
            onPress={() => onChange(option.value)}
            accessibilityRole="button"
            accessibilityState={{ selected: isActive }}
          >
            <View className="w-full items-center">
              <Text
                className={classNames(
                  'text-sm font-semibold',
                  isActive ? 'text-indigo-600' : 'text-gray-500'
                )}
                onLayout={e => {
                  const width = e.nativeEvent.layout.width
                  setLabelWidths(prev => ({
                    ...prev,
                    [option.value]: width
                  }))
                }}
              >
                {option.label}
              </Text>
              {isActive && (
                <View
                  style={{
                    marginTop: 8,
                    height: 2,
                    borderRadius: 1,
                    backgroundColor: '#4F46E5',
                    width: '100%'
                  }}
                />
              )}
            </View>
          </TouchableOpacity>
        )
      })}
    </View>
  )
}
