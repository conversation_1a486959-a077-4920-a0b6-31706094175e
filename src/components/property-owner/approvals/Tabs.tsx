import React, { useEffect, useRef, useState } from 'react'
import type { LayoutChangeEvent } from 'react-native'
import { Animated, Easing, Pressable, Text, View } from 'react-native'

export function Tabs({
  tabs = [
    { key: 'all', label: 'All' },
    { key: 'pending', label: 'Pending' },
    { key: 'approved', label: 'Approved' },
    { key: 'rejected', label: 'Rejected' }
  ],
  value,
  onChange
}: {
  tabs?: { key: string; label: string }[]
  value: string
  onChange: (key: string) => void
}) {
  const [loading, setLoading] = useState(false)
  const [tabLayouts, setTabLayouts] = useState<{ x: number; width: number }[]>(
    []
  )
  const indicatorX = useRef(new Animated.Value(0)).current
  const indicatorW = useRef(new Animated.Value(0)).current

  const handleTabLayout = (idx: number, e: LayoutChangeEvent) => {
    const { x, width } = e.nativeEvent.layout
    setTabLayouts(prev => {
      const next = [...prev]
      next[idx] = { x, width }
      return next
    })
  }

  useEffect(() => {
    const idx = tabs.findIndex(t => t.key === value)
    if (tabLayouts[idx]) {
      Animated.timing(indicatorX, {
        toValue: tabLayouts[idx].x,
        duration: 200,
        useNativeDriver: false,
        easing: Easing.out(Easing.exp)
      }).start()
      Animated.timing(indicatorW, {
        toValue: tabLayouts[idx].width,
        duration: 200,
        useNativeDriver: false,
        easing: Easing.out(Easing.exp)
      }).start()
    }
  }, [value, tabLayouts])

  // mock request
  const handlePress = (key: string) => {
    if (key === value || loading) return
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      onChange(key)
    }, 500)
  }

  return (
    <View className="relative mx-3 mt-3 flex-row overflow-hidden rounded-md bg-white p-1 px-3">
      {/* Animated slider */}
      {tabLayouts.length === tabs.length && (
        <Animated.View
          style={{
            position: 'absolute',
            left: indicatorX,
            width: indicatorW,
            top: 6,
            height: 28,
            backgroundColor: '#F1F5FF',
            borderRadius: 16,
            shadowColor: '#6366F1',
            shadowOpacity: 0.08,
            shadowRadius: 8,
            shadowOffset: { width: 0, height: 2 },
            zIndex: 0
          }}
        />
      )}
      {tabs.map((tab, idx) => {
        const isActive = value === tab.key
        return (
          <Pressable
            key={tab.key}
            className="z-10 flex-1 items-center justify-center rounded-lg py-2"
            onPress={() => handlePress(tab.key)}
            onLayout={e => handleTabLayout(idx, e)}
            disabled={loading}
          >
            <Text
              className={`text-sm font-semibold ${isActive ? 'text-indigo-600' : 'text-gray-500'}`}
            >
              {tab.label}
              {loading && isActive ? '…' : ''}
            </Text>
          </Pressable>
        )
      })}
    </View>
  )
}
