import React from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import {
  FontAwesome,
  FontAwesome6,
  MaterialCommunityIcons,
  MaterialIcons
} from '@expo/vector-icons'

import classNames from '@/utils/classname'

interface ApprovalDetailsModalProps {
  visible: boolean
  onClose: () => void
  data: {
    projectName: string
    propertyAddress: string
    projectManager: string
    requestDate: string
    itemName: string
    itemCategory: string
    priorityLevel: 'HIGH' | 'MEDIUM' | 'LOW'
    status: string
    pmScopeAmount: string
    vendorRequestAmount: string
    budgetVariance: string
    budgetVariancePercent: string
    budgetVarianceColor: string
    estimatedDuration: string
    itemDescription: string
  }
}

export function ApprovalDetailsModal({
  visible,
  onClose,
  data
}: ApprovalDetailsModalProps) {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View className="flex-1 items-center justify-center bg-black/40 px-2">
        <View className="w-full max-w-md rounded-3xl bg-white pb-4 pt-2">
          <ScrollView
            className="max-h-[75vh]"
            contentContainerStyle={{ paddingBottom: 16 }}
          >
            {/* Header */}
            <View className="px-6 pb-2 pt-4">
              <View className="flex-row items-center">
                <View className="mr-3 h-9 w-9 items-center justify-center rounded-full bg-[#4F46E5]">
                  <FontAwesome name="info" size={20} color="#fff" />
                </View>
                <Text className="text-lg font-bold text-gray-900">
                  Approval Details
                </Text>
              </View>
              <Text className="text-gray-500 ml-12 mt-1 text-xs">
                Complete information about this approval request
              </Text>
            </View>
            {/* Project Information */}
            <View className="mt-2 px-6">
              <View className="mb-2 flex-row items-center">
                <FontAwesome name="building" size={16} color="#6366F1" />
                <Text className="ml-2 text-base font-bold text-gray-900">
                  Project Information
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Project Name:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.projectName}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Property Address:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.propertyAddress}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Project Manager:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.projectManager}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Request Date:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.requestDate}
                </Text>
              </View>
            </View>
            {/* Item Information */}
            <View className="mt-4 px-6">
              <View className="mb-2 flex-row items-center">
                <FontAwesome6 name="wrench" size={18} color="#6366F1" />
                <Text className="ml-2 text-base font-bold text-gray-900">
                  Item Information
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Item Name:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.itemName}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Item Category:</Text>
                <Text className="font-semibold text-gray-900">
                  {data.itemCategory}
                </Text>
              </View>
              <View className="mb-2 rounded-md bg-gray-100 px-4 py-2">
                <View className="flex-row items-center justify-between">
                  <Text className="text-gray-400 text-xs">Priority Level:</Text>
                  <Text className="font-semibold text-red-400">
                    {data.priorityLevel}
                  </Text>
                </View>
              </View>
              <View className="mb-2 bg-gray-100 px-4 py-2">
                <View className="flex-row items-center justify-between">
                  <Text className="text-gray-400 text-xs">Status:</Text>
                  <View className="flex-row items-baseline rounded-full bg-blue-100 px-3 py-1">
                    <FontAwesome
                      name="hourglass-half"
                      size={14}
                      color="#38bdf8"
                      style={{ marginRight: 4 }}
                    />
                    <Text className="text-xs font-semibold text-blue-500">
                      {data.status}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            {/* Financial Details */}
            <View className="mt-4 px-6">
              <View className="mb-2 flex-row items-center">
                <MaterialCommunityIcons
                  name="currency-usd"
                  size={18}
                  color="#6366F1"
                />
                <Text className="ml-2 text-base font-bold text-gray-900">
                  Financial Details
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">PM Scope Amount:</Text>
                <Text className="font-bold text-green-600">
                  {data.pmScopeAmount}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">
                  Vendor Request Amount:
                </Text>
                <Text className="font-bold text-green-400">
                  {data.vendorRequestAmount}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">Budget Variance:</Text>
                <Text
                  className={classNames('font-bold', data.budgetVarianceColor)}
                >
                  {data.budgetVariance} ({data.budgetVariancePercent})
                </Text>
              </View>
              <View className="mb-2 flex-row items-center justify-between rounded-md bg-gray-100 px-4 py-2">
                <Text className="text-gray-400 text-xs">
                  Estimated Duration:
                </Text>
                <Text className="font-bold text-gray-900">
                  {data.estimatedDuration}
                </Text>
              </View>
            </View>
            {/* Item Description */}
            <View className="mt-4 px-6">
              <View className="mb-2 flex-row items-center">
                <MaterialIcons name="description" size={18} color="#6366F1" />
                <Text className="ml-2 text-base font-bold">
                  Item Description
                </Text>
              </View>
              <View className="relative flex-row items-start rounded-xl bg-neutral-50 px-4 py-3">
                <View
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 8,
                    bottom: 8,
                    width: 4,
                    borderRadius: 8,
                    backgroundColor: '#6366F1'
                  }}
                />
                <View className="ml-4 flex-1">
                  <Text className="text-sm leading-relaxed text-gray-900">
                    {data.itemDescription}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
          <View className="mt-2 px-6">
            <Pressable
              className="mt-2 w-full items-center justify-center rounded-md bg-gray-100 py-3"
              onPress={onClose}
            >
              <View className="flex-row items-center">
                <FontAwesome name="times" size={16} color="#374151" />
                <Text className="ml-2 font-semibold text-gray-900">Close</Text>
              </View>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}
