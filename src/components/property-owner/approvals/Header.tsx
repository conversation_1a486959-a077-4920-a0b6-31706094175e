import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { FontAwesome } from '@expo/vector-icons'

export function Header({ onFilter }: { onFilter?: () => void }) {
  return (
    <View
      className="flex-row items-center justify-between bg-white px-4 pb-2 pt-4"
      style={{ top: 0, zIndex: 50 }}
    >
      <Text className="text-lg font-bold text-black">Approval History</Text>
      <Pressable
        className="bg-gray-50 h-10 w-10 items-center justify-center rounded-xl shadow-sm"
        onPress={onFilter}
        android_ripple={{ color: '#f3f4f6', borderless: true }}
      >
        <FontAwesome name="sliders" size={20} color="#1F2937" />
      </Pressable>
    </View>
  )
}
