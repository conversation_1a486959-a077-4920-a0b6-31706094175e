import React from 'react'
import { Pressable, Text, View } from 'react-native'

import { crossPlatformShadow } from '@/theme/shadow'

export function ApprovalRequestCard({
  title = 'HVAC Repair Quote',
  address = '789 Pine Avenue, Seattle, WA',
  pmAmount = '$680.00',
  vendorAmount = '$750.00',
  requestedDate = 'Jul 19, 2023',
  statusText = 'Needs Approval',
  onApprove,
  onDetails
}: {
  title?: string
  address?: string
  pmAmount?: string
  vendorAmount?: string
  requestedDate?: string
  statusText?: string
  onApprove?: () => void
  onDetails?: () => void
}) {
  return (
    <View
      className="mb-4 rounded-2xl bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-1 flex-row items-center justify-between">
        <Text className="text-base font-bold text-gray-900">{title}</Text>
        <View className="rounded-full bg-blue-100 px-3 py-1">
          <Text className="text-xs font-semibold text-blue-500">
            {statusText}
          </Text>
        </View>
      </View>
      <Text className="text-gray-500 mb-1 text-sm">{address}</Text>
      <View className="mb-1 flex-row justify-between">
        <Text className="text-gray-400 text-xs">PM Scope Amount</Text>
        <Text className="text-gray-400 text-xs">Vendor Request Amount</Text>
        <Text className="text-gray-400 text-xs">Requested</Text>
      </View>
      <View className="mb-1 flex-row justify-between">
        <Text className="text-base font-bold text-gray-900">{pmAmount}</Text>
        <Text className="text-base font-bold text-gray-900">
          {vendorAmount}
        </Text>
        <Text className="text-base font-bold text-gray-900">
          {requestedDate}
        </Text>
      </View>
      <View className="h-px w-full bg-gray-200" />
      <View className="mt-2 flex-row gap-2">
        <Pressable
          className="flex-1 items-center justify-center rounded-[8px] bg-green-500 py-3"
          onPress={onApprove}
        >
          <Text className="font-semibold text-white">Approve</Text>
        </Pressable>
        <Pressable
          className="flex-1 items-center justify-center rounded-[8px] bg-indigo-600 py-3"
          onPress={onDetails}
        >
          <Text className="font-semibold text-white">View Details</Text>
        </Pressable>
      </View>
    </View>
  )
}
