import React from 'react'
import { Pressable, Text, View } from 'react-native'

import { crossPlatformShadow } from '@/theme/shadow'

const STATUS_STYLES = {
  approved: {
    bg: 'bg-green-100',
    text: 'text-green-500',
    label: 'Approved'
  },
  rejected: {
    bg: 'bg-red-100',
    text: 'text-red-500',
    label: 'Rejected'
  },
  pending: {
    bg: 'bg-blue-100',
    text: 'text-blue-500',
    label: 'Needs Approval'
  }
} as const

type StatusType = keyof typeof STATUS_STYLES

// eslint-disable-next-line unused-imports/no-unused-vars
type CardType = 'pending' | 'approved' | 'rejected'

export function ApprovalHistoryCard({
  title = 'HVAC Repair Quote',
  address = '789 Pine Avenue, Seattle, WA',
  pmAmount = '$680.00',
  vendorAmount = '$750.00',
  approvedAmount = '',
  date = 'Jul 19, 2023',
  status = 'pending',
  onApprove,
  onDetails
}: {
  title?: string
  address?: string
  pmAmount?: string
  vendorAmount?: string
  approvedAmount?: string
  date?: string
  status?: StatusType
  onApprove?: () => void
  onDetails?: () => void
}) {
  const statusStyle = STATUS_STYLES[status] || STATUS_STYLES.pending
  const amount1Label = 'PM Scope Amount'
  let amount2Label = 'Vendor Request Amount'
  let dateLabel = 'Requested'
  let showApprove = false
  let amount2 = vendorAmount
  if (status === 'approved') {
    amount2Label = 'Approved Amount'
    dateLabel = 'Approved On'
    showApprove = false
    amount2 = approvedAmount
  } else if (status === 'rejected') {
    amount2Label = 'Vendor Request Amount'
    dateLabel = 'Rejected On'
    showApprove = false
    amount2 = vendorAmount
  } else if (status === 'pending') {
    showApprove = true
  }

  return (
    <View
      className="mb-4 rounded-2xl bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-1 flex-row items-center justify-between">
        <Text className="text-base font-bold text-gray-900">{title}</Text>
        <View className={`rounded-full px-3 py-1 ${statusStyle.bg}`}>
          <Text className={`text-xs font-semibold ${statusStyle.text}`}>
            {statusStyle.label}
          </Text>
        </View>
      </View>
      <Text className="text-gray-500 mb-1 text-sm">{address}</Text>
      <View className="mb-1 flex-row justify-between">
        <Text className="text-gray-400 text-xs">{amount1Label}</Text>
        <Text className="text-gray-400 text-xs">{amount2Label}</Text>
        <Text className="text-gray-400 text-xs">{dateLabel}</Text>
      </View>
      <View className="mb-2 flex-row justify-between">
        <Text className="text-base font-bold text-gray-900">{pmAmount}</Text>
        <Text className="text-base font-bold text-gray-900">{amount2}</Text>
        <Text className="text-base font-bold text-gray-900">{date}</Text>
      </View>
      <View className="mb-2 h-px w-full bg-gray-200" />
      {showApprove ? (
        <View className="flex-row gap-2">
          <Pressable
            className="flex-1 items-center justify-center rounded-[8px] bg-green-500 py-3"
            onPress={onApprove}
          >
            <Text className="font-semibold text-white">Approve</Text>
          </Pressable>
          <Pressable
            className="flex-1 items-center justify-center rounded-[8px] bg-indigo-600 py-3"
            onPress={onDetails}
          >
            <Text className="font-semibold text-white">View Details</Text>
          </Pressable>
        </View>
      ) : (
        <Pressable
          className="items-center justify-center rounded-[8px] bg-indigo-600 py-3"
          onPress={onDetails}
        >
          <Text className="font-semibold text-white">View Details</Text>
        </Pressable>
      )}
    </View>
  )
}
