import React from 'react'
import type { ViewStyle } from 'react-native'
import { Text, TouchableOpacity } from 'react-native'
import { Ionicons } from '@expo/vector-icons'

export type ButtonVariant = 'primary' | 'secondary' | 'danger'

export interface ButtonProps {
  onPress?: () => void
  label: string
  iconName?: keyof typeof Ionicons.glyphMap
  variant?: ButtonVariant
  disabled?: boolean
  style?: ViewStyle
}

export const Button = ({
  onPress,
  label,
  iconName,
  variant = 'primary',
  disabled = false,
  style
}: ButtonProps) => {
  const getButtonStyle = () => {
    switch (variant) {
      case 'primary':
        return 'flex-row items-center justify-center rounded-lg bg-indigo-600 py-3'
      case 'secondary':
        return 'border-gray-300 flex-row items-center justify-center rounded-lg border py-3'
      case 'danger':
        return 'flex-row items-center justify-center rounded-lg bg-red-500 py-3'
      default:
        return 'flex-row items-center justify-center rounded-lg bg-indigo-600 py-3'
    }
  }

  const getTextColor = () => {
    switch (variant) {
      case 'secondary':
        return 'text-gray-700'
      default:
        return 'text-white'
    }
  }

  const getIconColor = () => {
    switch (variant) {
      case 'secondary':
        return '#4b5563' // gray-600
      default:
        return '#ffffff' // white
    }
  }

  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      className={getButtonStyle()}
      style={style}
    >
      {iconName && (
        <Ionicons name={iconName} size={18} color={getIconColor()} />
      )}
      <Text className={`ml-2 text-center font-medium ${getTextColor()}`}>
        {label}
      </Text>
    </TouchableOpacity>
  )
}
