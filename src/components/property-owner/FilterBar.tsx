import React from 'react'
import { ScrollView, Text, TouchableOpacity, View } from 'react-native'

import classNames from '@/utils/classname'

export interface FilterBarOption {
  /**
   * Display label, e.g. 'All (24)'
   */
  label: string
  /**
   * Unique value for the option
   */
  value: string
}

export interface FilterBarProps {
  /**
   * Options to display in the filter bar
   */
  options: FilterBarOption[]
  /**
   * Currently selected values (multi-select)
   */
  value: string[]
  /**
   * Callback when an option is pressed
   */
  onChange: (value: string) => void
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * FilterBar Component
 * Horizontally scrollable filter chips bar, with active highlight
 */
export const FilterBar = ({
  options,
  value,
  onChange,
  className
}: FilterBarProps) => {
  return (
    <View
      className={classNames('border-b border-gray-200 bg-white', className)}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingVertical: 12,
          gap: 8
        }}
        className="flex-row"
      >
        {options.map((option, idx) => {
          const selected = value.includes(option.value)
          return (
            <TouchableOpacity
              key={option.value}
              className={classNames(
                'mr-2 px-4 py-1.5',
                selected
                  ? 'border border-indigo-200 bg-indigo-50 font-semibold text-indigo-600'
                  : 'bg-gray-100 text-gray-700',
                'rounded-md',
                idx === options.length - 1 ? '' : 'mr-2'
              )}
              activeOpacity={0.7}
              onPress={() => onChange(option.value)}
            >
              <Text
                className={classNames(
                  'text-xs font-medium',
                  selected ? 'text-indigo-600' : 'text-gray-900'
                )}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}
