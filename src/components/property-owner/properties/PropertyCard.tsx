import React from 'react'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useRouter } from 'expo-router'

interface ProjectMetric {
  value: string | number
  label: string
}

interface ProjectManager {
  name: string
  title: string
  avatar?: string
}

interface MyProjectProps {
  id: number
  image: string
  address: string
  type: string
  status: 'occupied' | 'vacant' | 'renovation'
  metrics: ProjectMetric[]
  manager: ProjectManager
  onPress?: () => void
}

export const PropertyCard = ({
  id,
  image,
  address,
  type,
  status,
  metrics,
  manager,
  onPress
}: MyProjectProps) => {
  const router = useRouter()
  const getStatusStyle = () => {
    switch (status) {
      case 'occupied':
        return { backgroundColor: '#D1FAE5', color: '#059669' }
      case 'vacant':
        return { backgroundColor: '#FEE2E2', color: '#DC2626' }
      case 'renovation':
        return { backgroundColor: '#FEF3C7', color: '#D97706' }
      default:
        return { backgroundColor: '#F3F4F6', color: '#6B7280' }
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'occupied':
        return 'Occupied'
      case 'vacant':
        return 'Vacant'
      case 'renovation':
        return 'Renovation'
      default:
        return status
    }
  }

  return (
    <TouchableOpacity
      className="mb-4 overflow-hidden rounded-lg bg-white shadow-sm"
      onPress={() => {
        if (onPress) {
          onPress()
        } else {
          router.push(`/property-owner/property-details/${id}`)
        }
      }}
    >
      <View className="relative">
        <Image
          source={{ uri: image }}
          className="h-40 w-full"
          resizeMode="cover"
        />
        <View className="absolute right-3 top-3">
          <View
            style={{
              borderRadius: 999,
              paddingHorizontal: 8,
              paddingVertical: 4,
              backgroundColor: getStatusStyle().backgroundColor
            }}
          >
            <Text
              style={{
                fontSize: 12,
                fontWeight: '500',
                color: getStatusStyle().color
              }}
            >
              {getStatusText()}
            </Text>
          </View>
        </View>
        <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
          <Text className="mb-0.5 text-base font-semibold text-white">
            {address}
          </Text>
          <Text className="text-xs text-white/90">{type}</Text>
        </View>
      </View>
      <View className="p-4">
        <View className="mb-4 grid grid-cols-3 gap-3">
          {metrics.map((metric, index) => (
            <View key={index} className="items-center">
              <Text className="mb-0.5 text-base font-semibold">
                {metric.value}
              </Text>
              <Text className="text-gray-500 text-xs">{metric.label}</Text>
            </View>
          ))}
        </View>
        <View
          className="flex-row items-center pt-3"
          style={{ borderTopWidth: 1, borderTopColor: '#E5E7EB' }}
        >
          <View className="mr-2.5 h-9 w-9 items-center justify-center rounded-full bg-indigo-100">
            <Text className="text-sm font-semibold text-indigo-600">
              {manager.avatar || manager.name.charAt(0)}
            </Text>
          </View>
          <View className="flex-1">
            <Text className="text-xs font-medium text-gray-900">
              {manager.name}
            </Text>
            <Text className="text-gray-500 text-xs">{manager.title}</Text>
          </View>
          <View className="flex-row gap-3">
            <TouchableOpacity className="h-8 w-8 items-center justify-center rounded-full bg-gray-100">
              <Ionicons name="call" size={18} color="#111827" />
            </TouchableOpacity>
            <TouchableOpacity className="h-8 w-8 items-center justify-center rounded-full bg-gray-100">
              <Ionicons name="mail" size={18} color="#111827" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}
