import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'

import type { IconTypes } from '@/components/Icon'
import { Icon } from '@/components/Icon'
import { crossPlatformShadow } from '@/theme/shadow'
import classNames from '@/utils/classname'

export interface HeaderAction {
  icon: IconTypes
  onPress: () => void
  key?: string
}

export interface HeaderProps {
  /** Main title text */
  title: string
  /** Actions on the right, 1-2 items */
  actions?: HeaderAction[]
  /** Optional: container className */
  className?: string
}

/**
 * Page Header Component
 * Left: title, Right: 1-2 icon buttons (search, filter, etc.)
 */
export const Header = ({ title, actions = [], className }: HeaderProps) => {
  return (
    <View
      className={classNames(
        'flex-row items-center justify-between bg-white px-4 py-3',
        className
      )}
      style={[crossPlatformShadow('sm'), { zIndex: 10 }]}
    >
      <Text className="text-lg font-bold text-black">{title}</Text>
      <View className="flex-row gap-3">
        {actions.map((action, idx) => (
          <TouchableOpacity
            key={action.key || idx}
            onPress={action.onPress}
            className="ml-2 h-9 w-9 items-center justify-center rounded-lg bg-gray-100 active:bg-gray-200"
            activeOpacity={0.7}
          >
            <Icon icon={action.icon} size={20} color="#222" />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}
