import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'

import { IconSymbol } from '@/components/ui/IconSymbol'
import classNames from '@/utils/classname'

export interface ViewAllProps {
  /**
   * Left title text
   */
  title: string
  /**
   * Right link text
   */
  linkText?: string
  /**
   * Click handler for right link
   */
  onPress?: () => void
  /**
   * Optional: container className
   */
  className?: string
}

export const ViewAll = ({
  title,
  linkText,
  onPress,
  className
}: ViewAllProps) => {
  return (
    <View
      className={classNames(
        'mb-2 flex-row items-center justify-between',
        className
      )}
    >
      <Text className="text-base font-bold text-black">{title}</Text>
      {linkText ? (
        <TouchableOpacity
          className="flex-row items-center active:opacity-70"
          onPress={onPress}
          accessibilityRole="link"
        >
          <Text className="mr-1 text-sm font-semibold text-indigo-500">
            {linkText}
          </Text>
          <IconSymbol name="chevron.right" size={16} color="#4f46e5" />
        </TouchableOpacity>
      ) : null}
    </View>
  )
}
