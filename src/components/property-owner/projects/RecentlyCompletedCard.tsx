import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { FontAwesome5, Ionicons } from '@expo/vector-icons'
import { useRouter } from 'expo-router'

import { crossPlatformShadow } from '@/theme/shadow'

export interface TimelineItem {
  id: number
  date: string
  event: string
  label: string
}

export interface RecentlyCompletedProps {
  id: number
  title: string
  address: string
  completedOn: string
  duration: string
  budget: string
  finalCost: string
  manager: {
    initials: string
    name: string
    company: string
    bgColor?: string
    color?: string
  }
  timeline: TimelineItem[]
  onReport?: () => void
  onDetails?: () => void
}

export const RecentlyCompletedCard = ({
  id,
  title,
  address,
  completedOn,
  duration,
  budget,
  finalCost,
  manager,
  timeline,
  onReport,
  onDetails
}: RecentlyCompletedProps) => {
  const router = useRouter()
  const handlePress = () => {
    if (onDetails) {
      onDetails()
    } else if (id) {
      router.push(`/property-manager/project/${id}`)
    }
  }

  const handleReport = () => {
    if (onReport) {
      onReport()
    } else {
      router.push('/property-owner/(tabs)/reports')
    }
  }

  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      {/* Title and badge */}
      <View className="mb-1 flex-row items-center justify-between">
        <Text className="text-base font-semibold">{title}</Text>
        <View className="rounded-full bg-green-100 px-2 py-1">
          <Text className="text-xs font-medium text-green-600">Completed</Text>
        </View>
      </View>
      {/* Address */}
      <Text className="text-gray-500 mb-2 text-xs">{address}</Text>
      {/* Details grid */}
      <View className="mb-2 flex-row flex-wrap border-b border-gray-200 pb-2">
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Completed On</Text>
          <Text className="text-sm font-semibold">{completedOn}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Duration</Text>
          <Text className="text-sm font-semibold">{duration}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Budget</Text>
          <Text className="text-sm font-semibold">{budget}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Final Cost</Text>
          <Text className="text-sm font-semibold">{finalCost}</Text>
        </View>
      </View>
      {/* Manager */}
      <View className="mb-2">
        <Text className="mb-1 text-xs font-medium">Property Manager</Text>
        <View className="flex-row items-center gap-2">
          <View
            className={`h-9 w-9 items-center justify-center rounded-full ${manager.bgColor || 'bg-sky-100'}`}
          >
            <Text
              className={`text-sm font-bold ${manager.color || 'text-sky-600'}`}
            >
              {manager.initials}
            </Text>
          </View>
          <View>
            <Text className="text-sm font-semibold">{manager.name}</Text>
            <Text className="text-gray-500 text-xs">{manager.company}</Text>
          </View>
        </View>
      </View>
      {/* Timeline */}
      <View className="mb-2">
        <Text className="mb-1 text-xs font-medium">Project Timeline</Text>
        <View className="border-l-2 border-gray-200 pl-3">
          {timeline.map((item, idx) => (
            <View key={idx} className="mb-2 flex-row items-start">
              <View className="-ml-3 mt-0.5">
                <Ionicons name="ellipse" size={12} color="#22c55e" />
              </View>
              <View className="ml-2">
                <Text className="text-gray-500 text-xs">{item.date}</Text>
                <Text className="text-sm font-medium">{item.label}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
      {/* Buttons */}
      <View className="mt-2 flex-row gap-2">
        <TouchableOpacity
          className="flex-1 flex-row items-center justify-center rounded-lg border border-gray-300 bg-white py-3"
          onPress={handleReport}
        >
          <FontAwesome5 name="file-pdf" size={16} color="#6366f1" />
          <Text className="ml-2 font-semibold text-indigo-600">
            View Report
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="flex-1 flex-row items-center justify-center rounded-lg bg-indigo-600 py-3"
          onPress={handlePress}
        >
          <Ionicons name="eye" size={18} color="#fff" />
          <Text className="ml-2 font-semibold text-white">View Details</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
