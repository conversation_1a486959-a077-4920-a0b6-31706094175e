import React from 'react'
import { View } from 'react-native'

import classNames from '@/utils/classname'

import { StatCard, type StatCardProps } from './StatCard'

export interface StatsGridProps {
  /**
   * StatCard
   */
  items: StatCardProps[]
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * StatsGrid Component
 * 2-column grid for StatCard, auto wrap, consistent gap
 */
export const StatsGrid = ({ items, className }: StatsGridProps) => {
  const rows = []
  for (let i = 0; i < items.length; i += 2) {
    rows.push(items.slice(i, i + 2))
  }
  return (
    <View className={classNames('', className)}>
      {rows.map((row, rowIdx) => (
        <View
          key={rowIdx}
          style={{
            flexDirection: 'row',
            marginBottom: 12,
            alignItems: 'stretch'
          }}
        >
          {row.map((item, idx) => (
            <View
              key={idx}
              style={{ flex: 1, marginRight: idx === 0 ? 12 : 0 }}
            >
              <StatCard {...item} />
            </View>
          ))}
          {row.length < 2 && <View style={{ flex: 1 }} />}
        </View>
      ))}
    </View>
  )
}
