import React, { useState } from 'react'
import { Modal, Pressable, Text, View } from 'react-native'
import { MaterialCommunityIcons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'

interface ApprovalModalProps {
  visible: boolean
  onClose: () => void
  onApprove?: () => Promise<void> | void
  itemName: string
  projectName: string
  property: string
  amount: number
}

export const ApprovalModal = ({
  visible,
  onClose,
  onApprove,
  itemName,
  projectName,
  property,
  amount
}: ApprovalModalProps) => {
  const [loading, setLoading] = useState(false)

  const handleApprove = async () => {
    setLoading(true)
    await new Promise(res => setTimeout(res, 1000))
    setLoading(false)
    onApprove?.()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View
        className="flex-1 items-center justify-center bg-black/30 px-4"
        style={[crossPlatformShadow('lg')]}
      >
        <View className="w-full max-w-sm items-center rounded-2xl bg-white pb-2 pt-8">
          <View
            className="mb-2 rounded-full bg-white p-2"
            style={{ elevation: 4, ...crossPlatformShadow('default') }}
          >
            <View className="rounded-full bg-green-500 p-3">
              <MaterialCommunityIcons name="check" size={32} color="#fff" />
            </View>
          </View>
          <Text className="mb-1 text-center text-xl font-bold text-gray-900">
            Approve Request
          </Text>
          <Text className="text-gray-500 mb-6 mt-1 px-4 text-center text-sm leading-5">
            Are you sure you want to approve this request?
          </Text>
          {/* Content section with proper dividers */}
          <View className="bg-gray-50 mb-6 w-full">
            <View className="px-6 py-5">
              <View className="mb-4 flex-row items-start justify-between">
                <Text className="text-gray-500 text-sm font-medium">
                  Item Name:
                </Text>
                <Text className="max-w-[60%] text-right text-sm font-semibold leading-5 text-gray-900">
                  {itemName}
                </Text>
              </View>
              <View className="mb-4 border-t border-gray-200 pt-4">
                <View className="flex-row items-start justify-between">
                  <Text className="text-gray-500 text-sm font-medium">
                    Project Name:
                  </Text>
                  <Text className="max-w-[60%] text-right text-sm font-semibold leading-5 text-gray-900">
                    {projectName}
                  </Text>
                </View>
              </View>
              <View className="mb-4 border-t border-gray-200 pt-4">
                <View className="flex-row items-start justify-between">
                  <Text className="text-gray-500 text-sm font-medium">
                    Property:
                  </Text>
                  <Text className="max-w-[60%] text-right text-sm font-semibold leading-5 text-gray-900">
                    {property}
                  </Text>
                </View>
              </View>

              <View className="border-t border-gray-200 pt-4">
                <View className="flex-row items-center justify-between">
                  <Text className="text-gray-500 text-sm font-medium">
                    Amount:
                  </Text>
                  <Text className="max-w-[60%] text-right text-base font-bold text-green-600">
                    ${amount.toFixed(2)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          {/* Buttons with icons */}
          <View className="w-full flex-row gap-3 px-6 pb-6">
            <Pressable
              className="flex-1 flex-row items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white py-3"
              onPress={onClose}
              disabled={loading}
            >
              <MaterialCommunityIcons name="close" size={18} color="#6b7280" />
              <Text className="text-gray-500 text-base font-semibold">
                Cancel
              </Text>
            </Pressable>
            <Pressable
              className="flex-1 flex-row items-center justify-center gap-2 rounded-lg bg-green-600 py-3"
              onPress={handleApprove}
              disabled={loading}
            >
              <MaterialCommunityIcons name="check" size={18} color="#ffffff" />
              <Text className="text-base font-semibold text-white">
                {loading ? 'Approving...' : 'Approve'}
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}

export default ApprovalModal
