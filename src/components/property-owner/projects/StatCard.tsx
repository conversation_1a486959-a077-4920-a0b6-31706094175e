import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome5, Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'
import classNames from '@/utils/classname'
export interface StatCardProps {
  /**
   * Main title, e.g. 'Monthly Income'
   */
  title: string
  /**
   * Main value, e.g. '$42,500'
   */
  value: string | number
  /**
   * Icon type or custom icon
   */
  icon?: 'tools' | 'check'
  /**
   * Trend info, e.g. '+4.5% from last month' or '8 completed'
   */
  trendText?: string
  /**
   * Trend type: 'up', 'down', 'dot', or undefined
   */
  trendType?: 'up' | 'down' | 'dot'
  /**
   * Trend color: 'success', 'danger', 'default'
   */
  trendColor?: 'success' | 'danger' | 'default'
  /**
   * Optional: container className
   */
  className?: string
}

const iconMap = {
  tools: <FontAwesome5 name="tools" size={13} color="#6366f1" />,
  check: <Ionicons name="checkmark" size={15} color="#6366f1" />
}

// const trendColorMap = {
//   success: 'text-green-600',
//   danger: 'text-red-500',
//   default: 'text-gray-500'
// }

export const StatCard = ({
  title,
  value,
  icon = 'tools',
  trendText,
  trendType = 'up',
  // trendColor = 'success',
  className
}: StatCardProps) => {
  return (
    <View
      className={classNames('rounded-2xl bg-white p-4', className)}
      style={{
        minHeight: 130,
        justifyContent: 'space-between',
        ...crossPlatformShadow('lg')
      }}
    >
      <View className="mb-2 flex-row items-center">
        <View className="mr-1 h-5 w-5 items-center justify-center rounded-full bg-indigo-100">
          {iconMap[icon] ?? iconMap['tools']}
        </View>
        <Text
          className="ml-1 text-xs font-semibold text-indigo-700"
          numberOfLines={1}
        >
          {title}
        </Text>
      </View>
      <Text
        className="mb-1 text-2xl font-extrabold text-black"
        numberOfLines={1}
      >
        {value}
      </Text>
      <View className="min-h-[20px] flex-row items-center">
        {trendType === 'up' && (
          <Ionicons
            name="arrow-up"
            size={14}
            color="#22c55e"
            className="mr-1"
          />
        )}
        {trendType === 'down' && (
          <Ionicons
            name="arrow-down"
            size={14}
            color="#ef4444"
            className="mr-1"
          />
        )}
        {trendType === 'dot' && (
          <View className="mr-1 h-2 w-2 rounded-full bg-green-500" />
        )}
        <Text className="text-xs font-medium text-green-600">
          {trendText || ' '}
        </Text>
      </View>
    </View>
  )
}
