import React, { useRef, useState } from 'react'
import {
  Dimensions,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native'
import { FontAwesome5, Ionicons, MaterialIcons } from '@expo/vector-icons'
import MultiSlider from '@ptomasroos/react-native-multi-slider'
import { useRequest } from 'ahooks'

import { useDict } from '@/store'

const { width, height } = Dimensions.get('window')

interface FilterBarProps {
  onClose?: () => void
  onApply?: (filters: {
    projectType: string
    projectStatus: string
    manager: string
    budgetRange: [number, number]
    sortBy: string
  }) => void
}

export function FilterBar({ onClose, onApply }: FilterBarProps) {
  const scrollRef = useRef<ScrollView>(null)

  const [selectedProjectType, setSelectedProjectType] = useState('all')
  const [selectedProjectStatus, setSelectedProjectStatus] = useState('all')
  const [selectedManager, setSelectedManager] = useState('all')
  const [budgetRange, setBudgetRange] = useState<[number, number]>([0, 30000])
  const [sortBy, setSortBy] = useState('date')

  const [activeTags, setActiveTags] = useState([
    { key: 'type', label: 'Type', value: 'All Types' },
    { key: 'status', label: 'Status', value: 'All Statuses' },
    { key: 'date', label: 'Date', value: 'Any Time' },
    { key: 'budget', label: 'Budget', value: 'Any Amount' }
  ])

  const { getDictItems } = useDict()
  const projectStatusRequest = useRequest(() => getDictItems('PROJECT_STATUS'))

  const projectTypeOptions = [
    { label: 'All Types', value: 'all', icon: undefined },
    {
      label: 'Work Order',
      value: 'WORK_ORDER',
      icon: <FontAwesome5 name="clipboard-check" size={16} color="#6366F1" />
    },
    {
      label: 'Rehab',
      value: 'REHAB',
      icon: <FontAwesome5 name="hammer" size={16} color="#6366F1" />
    }
  ]
  const managerOptions = [
    { label: 'All Managers', value: 'all', icon: undefined },
    {
      label: 'John Davis',
      value: 'john',
      icon: <FontAwesome5 name="user-tie" size={16} color="#6366F1" />
    },
    {
      label: 'Maria Williams',
      value: 'maria',
      icon: <FontAwesome5 name="user-tie" size={16} color="#6366F1" />
    },
    {
      label: 'Sarah Johnson',
      value: 'sarah',
      icon: <FontAwesome5 name="user-tie" size={16} color="#6366F1" />
    },
    {
      label: 'Thomas Davis',
      value: 'thomas',
      icon: <FontAwesome5 name="user-tie" size={16} color="#6366F1" />
    }
  ]

  const projectStatusOptions = [
    { label: 'All Statuses', value: 'all', icon: undefined },
    ...(projectStatusRequest.data?.map((item: any) => ({
      label: item.label,
      value: item.code,
      icon: getStatusIcon(item.code)
    })) ?? [])
  ]

  const sortByOptions = [
    {
      label: 'Start Date',
      value: 'date',
      icon: <FontAwesome5 name="calendar-alt" size={16} color="#6366F1" />
    },
    {
      label: 'Budget',
      value: 'budget',
      icon: <FontAwesome5 name="dollar-sign" size={16} color="#6366F1" />
    },
    {
      label: 'Name',
      value: 'name',
      icon: <FontAwesome5 name="sort-alpha-down" size={16} color="#6366F1" />
    }
  ]

  function getStatusIcon(code: string) {
    switch (code) {
      case 'PLANNING':
        return <FontAwesome5 name="clipboard-list" size={16} color="#6366F1" />
      case 'IN_PROGRESS':
        return <Ionicons name="sync" size={16} color="#6366F1" />
      case 'ON_HOLD':
        return <FontAwesome5 name="pause-circle" size={16} color="#6366F1" />
      case 'COMPLETED':
        return <FontAwesome5 name="check-circle" size={16} color="#6366F1" />
      case 'OVERDUE':
        return <MaterialIcons name="error" size={16} color="#6366F1" />
      case 'PENDING_QUOTES':
        return <FontAwesome5 name="hourglass-half" size={16} color="#6366F1" />
      case 'SUBMITTED':
        return <FontAwesome5 name="paper-plane" size={16} color="#6366F1" />
      case 'DRAFT':
        return <FontAwesome5 name="edit" size={16} color="#6366F1" />
      case 'CANCELLED':
        return <MaterialIcons name="cancel" size={16} color="#6366F1" />
      default:
        return undefined
    }
  }
  const handleRemoveTag = (key: string) => {
    setActiveTags(tags => tags.filter(tag => tag.key !== key))
  }

  const handleApply = () => {
    onApply?.({
      projectType: selectedProjectType,
      projectStatus: selectedProjectStatus,
      manager: selectedManager,
      budgetRange,
      sortBy
    })
    onClose?.()
  }

  return (
    <>
      <Pressable style={styles.overlay} onPress={onClose} />
      <View style={styles.panel}>
        <View style={styles.topDivider} />
        <ScrollView
          ref={scrollRef}
          style={styles.scroll}
          contentContainerStyle={{ paddingBottom: 0 }}
          showsVerticalScrollIndicator={false}
        >
          {/* Project Type */}
          <View style={styles.group}>
            <Text style={styles.label}>Project Type</Text>
            <View style={styles.optionsRow}>
              {projectTypeOptions.map(opt => (
                <FilterButton
                  key={opt.value}
                  label={opt.label}
                  icon={opt.icon}
                  active={selectedProjectType === opt.value}
                  onPress={() => setSelectedProjectType(opt.value)}
                />
              ))}
            </View>
          </View>
          {/* Project Status */}
          <View style={styles.group}>
            <Text style={styles.label}>Project Status</Text>
            <View style={styles.optionsRow}>
              {projectStatusOptions.map(opt => (
                <FilterButton
                  key={opt.value}
                  label={opt.label}
                  icon={opt.icon}
                  active={selectedProjectStatus === opt.value}
                  onPress={() => setSelectedProjectStatus(opt.value)}
                />
              ))}
            </View>
          </View>
          {/* Property Manager */}
          <View style={styles.group}>
            <Text style={styles.label}>Property Manager</Text>
            <View style={styles.optionsRow}>
              {managerOptions.map(opt => (
                <FilterButton
                  key={opt.value}
                  label={opt.label}
                  icon={opt.icon}
                  active={selectedManager === opt.value}
                  onPress={() => setSelectedManager(opt.value)}
                />
              ))}
            </View>
          </View>
          {/* Date Range */}
          <View style={styles.group}>
            <Text style={styles.label}>Date Range</Text>
            <View style={styles.dateRow}>
              <DateInput placeholder="Start Date" />
              <Text style={styles.dateSep}>to</Text>
              <DateInput placeholder="End Date" />
            </View>
          </View>
          {/* Budget Range */}
          <View style={styles.group}>
            <Text style={styles.label}>
              Budget Range:{' '}
              <Text style={styles.budgetValue}>
                ${Number(budgetRange[0]).toLocaleString()} - $
                {Number(budgetRange[1]).toLocaleString()}
              </Text>
            </Text>
            <MultiSlider
              values={budgetRange}
              min={0}
              max={30000}
              step={500}
              onValuesChange={(vals: number[]) =>
                setBudgetRange([vals[0] ?? 0, vals[1] ?? 0])
              }
              sliderLength={width - 64}
              selectedStyle={{ backgroundColor: '#4f46e5' }}
              unselectedStyle={{ backgroundColor: '#E5E7EB' }}
              markerStyle={{
                backgroundColor: '#4f46e5',
                borderWidth: 2,
                borderColor: '#fff',
                width: 20,
                height: 20
              }}
              containerStyle={{ alignSelf: 'center', marginVertical: 8 }}
            />
            <View style={styles.rangeLabels}>
              <Text>${Number(budgetRange[0]).toLocaleString()}</Text>
              <Text>${Number(budgetRange[1]).toLocaleString()}</Text>
            </View>
          </View>
          {/* Sort By */}
          <View style={styles.group}>
            <Text style={styles.label}>Sort By</Text>
            <View style={styles.optionsRow}>
              {sortByOptions.map(opt => (
                <FilterButton
                  key={opt.value}
                  label={opt.label}
                  icon={opt.icon}
                  active={sortBy === opt.value}
                  onPress={() => setSortBy(opt.value)}
                />
              ))}
            </View>
          </View>
          <View style={styles.sectionDivider} />
        </ScrollView>
        {/* Bottom actions */}
        <View style={styles.bottomPanel}>
          <TouchableOpacity style={styles.applyBtn} onPress={handleApply}>
            <Text style={styles.applyBtnText}>Apply Filters</Text>
          </TouchableOpacity>
          {/* <View style={styles.sectionDivider} /> */}
          <View style={styles.activeFiltersBox}>
            <View style={styles.activeFiltersHeaderRow}>
              <Text style={styles.activeFiltersLabel}>
                <FontAwesome5 name="tag" size={14} color="#6366F1" /> Active
                Filters
              </Text>
              <TouchableOpacity style={styles.clearAllBtn}>
                <FontAwesome5 name="broom" size={14} color="#ef4444" />
                <Text style={styles.clearAllText}>Clear All</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.tagsRow}>
              {activeTags.map(tag => (
                <FilterTag
                  key={tag.key}
                  category={tag.label}
                  value={tag.value}
                  onRemove={() => handleRemoveTag(tag.key)}
                />
              ))}
            </View>
          </View>
        </View>
      </View>
    </>
  )
}

interface FilterButtonProps {
  label: string
  icon?: React.ReactNode
  active?: boolean
  onPress?: () => void
}

function FilterButton({ label, icon, active, onPress }: FilterButtonProps) {
  return (
    <TouchableOpacity
      style={[styles.filterBtn, active && styles.filterBtnActive]}
      onPress={onPress}
    >
      {icon && <View style={{ marginRight: 6 }}>{icon}</View>}
      <Text
        style={[styles.filterBtnText, active && styles.filterBtnTextActive]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  )
}

interface FilterTagProps {
  category: string
  value: string
  onRemove: () => void
}

function FilterTag({ category, value, onRemove }: FilterTagProps) {
  return (
    <View style={styles.tag}>
      <Text style={styles.tagCategory}>{category}:</Text>
      <Text style={styles.tagValue}>{value}</Text>
      <TouchableOpacity style={styles.tagRemove} onPress={onRemove}>
        <FontAwesome5 name="times" size={12} color="#ef4444" />
      </TouchableOpacity>
    </View>
  )
}

function DateInput({ placeholder }: { placeholder: string }) {
  return (
    <View style={styles.dateInputWrap}>
      <TextInput
        style={styles.dateInput}
        placeholder={placeholder}
        placeholderTextColor="#888"
        editable={false}
      />
      <FontAwesome5
        name="calendar-alt"
        size={16}
        color="#6366F1"
        style={{ position: 'absolute', right: 8, top: 10 }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.08)',
    zIndex: 9998
  },
  panel: {
    position: 'absolute',
    top: 56, // Filter Bar height
    left: 0,
    right: 0,
    height: height - 56,
    backgroundColor: '#fff',
    zIndex: 9999,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB'
  },
  topDivider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    width: '100%'
  },
  scroll: { flex: 1 },
  group: { marginTop: 16, paddingHorizontal: 16 },
  label: { fontWeight: '500', fontSize: 13, marginBottom: 8, color: '#222' },
  optionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 6 },
  dateRow: { flexDirection: 'row', alignItems: 'center', gap: 8 },
  dateSep: { marginHorizontal: 8, color: '#888', fontSize: 13 },
  budgetValue: { color: '#6366F1', fontWeight: '500', fontSize: 13 },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 2,
    paddingHorizontal: 2,
    fontSize: 12
  },
  bottomPanel: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    padding: 16
  },
  applyBtn: {
    backgroundColor: '#4f46e5',
    borderRadius: 10,
    paddingVertical: 10,
    alignItems: 'center',
    marginBottom: 14,
    width: '100%',
    minHeight: 38,
    justifyContent: 'center',
    shadowColor: 'transparent'
  },
  applyBtnText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: 0.1
  },
  activeFiltersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  activeFiltersLabel: { fontWeight: 'bold', fontSize: 15 },
  clearAll: { color: '#ef4444', fontWeight: 'bold', marginLeft: 8 },
  tagsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff1f2',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8
  },
  tagCategory: { color: '#ef4444', fontWeight: 'bold', marginRight: 4 },
  tagValue: { color: '#ef4444', marginRight: 4 },
  tagRemove: { padding: 2 },
  filterBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 6,
    marginBottom: 6,
    minHeight: 32,
    minWidth: 0
  },
  filterBtnActive: { backgroundColor: '#4f46e5' },
  filterBtnText: { color: '#222', fontWeight: '500', fontSize: 13 },
  filterBtnTextActive: { color: '#fff' },
  dateInputWrap: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    marginRight: 0,
    position: 'relative',
    height: 36,
    justifyContent: 'center'
  },
  dateInput: {
    color: '#222',
    fontSize: 13,
    paddingLeft: 10,
    paddingRight: 28,
    height: 36
  },
  sliderContainer: { height: 28, marginVertical: 6, justifyContent: 'center' },
  sliderTrack: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 12,
    height: 3,
    backgroundColor: '#E5E7EB',
    borderRadius: 2
  },
  sliderRange: {
    position: 'absolute',
    top: 12,
    height: 3,
    backgroundColor: '#4f46e5',
    borderRadius: 2
  },
  sliderThumb: {
    position: 'absolute',
    top: 4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#4f46e5',
    borderWidth: 2,
    borderColor: '#fff',
    zIndex: 2
  },
  sectionDivider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 16
  },
  activeFiltersBox: {
    backgroundColor: '#f6f7fb',
    borderRadius: 12,
    padding: 12,
    marginTop: 16
  },
  activeFiltersHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  clearAllBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#f3d6d6',
    marginLeft: 8
  },
  clearAllText: {
    color: '#ef4444',
    fontWeight: '600',
    fontSize: 13,
    marginLeft: 4
  }
})
