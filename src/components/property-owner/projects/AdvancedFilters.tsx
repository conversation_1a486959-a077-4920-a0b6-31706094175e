import React from 'react'
import { Text, TextInput, TouchableOpacity, View } from 'react-native'
import Slider from '@react-native-community/slider'
import { Ionicons } from '@expo/vector-icons'
import type { ReactNode } from 'react'

import { crossPlatformShadow } from '@/theme/shadow'

interface Option {
  label: string
  value: string
  icon?: ReactNode
}

interface FilterGroup {
  type: 'buttons' | 'date-range' | 'slider' | 'sort'
  title?: string
  options?: Option[]
  value?: string
  onChange?: (val: string) => void
  // date-range
  startDate?: string
  endDate?: string
  onStartDateChange?: (val: string) => void
  onEndDateChange?: (val: string) => void
  // slider
  min?: number
  max?: number
  step?: number
  sliderValue?: [number, number]
  onSliderChange?: (val: [number, number]) => void
}

interface AdvancedFiltersProps {
  groups: FilterGroup[]
  onApply?: () => void
  onClear?: () => void
  activeFiltersCount?: number
}

export function AdvancedFilters({
  groups,
  onApply,
  onClear,
  activeFiltersCount = 0
}: AdvancedFiltersProps) {
  return (
    <View
      style={[
        crossPlatformShadow('md'),
        {
          backgroundColor: '#fff',
          borderRadius: 16,
          padding: 16,
          marginHorizontal: 8,
          zIndex: 20
        }
      ]}
    >
      {groups.map((group, idx) => {
        if (group.type === 'buttons' || group.type === 'sort') {
          return (
            <View key={group.title || idx} className={idx > 0 ? 'mt-4' : ''}>
              {group.title && (
                <Text className="mb-2 text-sm font-semibold text-gray-900">
                  {group.title}
                </Text>
              )}
              <View className="flex-row flex-wrap gap-2">
                {group.options?.map(opt => {
                  const selected = group.value === opt.value
                  return (
                    <TouchableOpacity
                      key={opt.value}
                      className={
                        'mb-2 flex-row items-center rounded-lg px-3 py-2 ' +
                        (selected ? 'bg-indigo-600' : 'bg-gray-100')
                      }
                      onPress={() => group.onChange?.(opt.value)}
                      activeOpacity={0.8}
                    >
                      {opt.icon && <View className="mr-1">{opt.icon}</View>}
                      <Text
                        className={
                          selected
                            ? 'font-semibold text-white'
                            : 'font-semibold text-gray-800'
                        }
                      >
                        {opt.label}
                      </Text>
                    </TouchableOpacity>
                  )
                })}
              </View>
              {idx < groups.length - 1 && (
                <View className="my-3 h-px w-full bg-gray-200" />
              )}
            </View>
          )
        }
        if (group.type === 'date-range') {
          return (
            <View key={group.title || idx} className="mt-4">
              <Text className="mb-2 text-sm font-semibold text-gray-900">
                {group.title}
              </Text>
              <View className="flex-row items-center gap-2">
                <View className="flex-1 flex-row items-center rounded-lg bg-gray-100 px-3 py-2">
                  <TextInput
                    className="flex-1 text-base"
                    placeholder="Year/Month/Day"
                    value={group.startDate}
                    onChangeText={group.onStartDateChange}
                  />
                  <Ionicons name="calendar-outline" size={16} color="#222" />
                </View>
                <Text className="text-gray-500 mx-1">to</Text>
                <View className="flex-1 flex-row items-center rounded-lg bg-gray-100 px-3 py-2">
                  <TextInput
                    className="flex-1 text-base"
                    placeholder="Year/Month/Day"
                    value={group.endDate}
                    onChangeText={group.onEndDateChange}
                  />
                  <Ionicons name="calendar-outline" size={16} color="#222" />
                </View>
              </View>
              {idx < groups.length - 1 && (
                <View className="my-3 h-px w-full bg-gray-200" />
              )}
            </View>
          )
        }
        if (group.type === 'slider') {
          const [min, max] = [group.min ?? 0, group.max ?? 30000]
          const [val1, val2] = group.sliderValue ?? [min, max]
          return (
            <View key={group.title || idx} className="mt-4">
              <Text className="mb-2 text-sm font-semibold text-gray-900">
                {group.title}: ${val1} - ${val2}
              </Text>
              <View
                style={{
                  position: 'relative',
                  height: 40,
                  justifyContent: 'center'
                }}
              >
                <View
                  style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: '50%',
                    height: 6,
                    backgroundColor: '#e5e7eb',
                    borderRadius: 3,
                    zIndex: 0
                  }}
                />
                <View
                  style={{
                    position: 'absolute',
                    left: `${((val1 - min) / (max - min)) * 100}%`,
                    width: `${((val2 - val1) / (max - min)) * 100}%`,
                    top: '50%',
                    height: 6,
                    backgroundColor: '#6366f1',
                    borderRadius: 3,
                    zIndex: 1
                  }}
                />
                <Slider
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: 40,
                    zIndex: 2
                  }}
                  minimumValue={min}
                  maximumValue={val2}
                  value={val1}
                  step={group.step ?? 100}
                  minimumTrackTintColor="transparent"
                  maximumTrackTintColor="transparent"
                  thumbTintColor="#6366f1"
                  onValueChange={v => group.onSliderChange?.([v, val2])}
                />
                <Slider
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: 40,
                    zIndex: 3
                  }}
                  minimumValue={val1}
                  maximumValue={max}
                  value={val2}
                  step={group.step ?? 100}
                  minimumTrackTintColor="transparent"
                  maximumTrackTintColor="transparent"
                  thumbTintColor="#6366f1"
                  onValueChange={v => group.onSliderChange?.([val1, v])}
                />
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-500 text-xs">${val1}</Text>
                <Text className="text-gray-500 text-xs">${val2}</Text>
              </View>
              {idx < groups.length - 1 && (
                <View className="my-3 h-px w-full bg-gray-200" />
              )}
            </View>
          )
        }
        return null
      })}
      {/* Apply/Clear Button and Active Filters */}
      <View className="mt-6">
        <TouchableOpacity
          className="mb-3 h-11 w-full items-center justify-center rounded-xl bg-indigo-600"
          onPress={onApply}
          activeOpacity={0.85}
        >
          <Text className="text-base font-bold text-white">Apply Filters</Text>
        </TouchableOpacity>
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <View className="mr-2 h-2 w-2 rounded-full bg-indigo-600" />
            <Text className="text-sm font-semibold text-gray-800">
              Active Filters
            </Text>
            {activeFiltersCount > 0 && (
              <Text className="ml-2 rounded-full bg-indigo-100 px-2 py-0.5 text-xs font-bold text-indigo-600">
                {activeFiltersCount}
              </Text>
            )}
          </View>
          <TouchableOpacity onPress={onClear} activeOpacity={0.8}>
            <Text className="text-sm font-semibold text-rose-500">
              Clear All
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}
