import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useRouter } from 'expo-router'

import { crossPlatformShadow } from '@/theme/shadow'

export interface ActiveProjectProps {
  title: string
  address: string
  startDate: string
  endDate: string
  budget: string
  spent: string
  manager: {
    initials: string
    name: string
    company: string
    bgColor?: string
    color?: string
  }
  statusText?: string
  onDetails?: () => void
  id?: string | number
}

export const ActiveProjectCard = ({
  title,
  address,
  startDate,
  endDate,
  budget,
  spent,
  manager,
  statusText = 'In Progress',
  onDetails,
  id
}: ActiveProjectProps) => {
  const router = useRouter()
  const handlePress = () => {
    if (onDetails) {
      onDetails()
    } else if (id) {
      router.push(`/property-manager/project/${id}`)
    }
  }

  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      {/* Title and badge */}
      <View className="mb-1 flex-row items-center justify-between">
        <Text className="text-base font-semibold">{title}</Text>
        <View className="rounded-full bg-yellow-100 px-2 py-1">
          <Text className="text-xs font-medium text-yellow-600">
            {statusText}
          </Text>
        </View>
      </View>
      {/* Address */}
      <Text className="text-gray-500 mb-2 text-xs">{address}</Text>
      {/* Details grid */}
      <View className="mb-2 flex-row flex-wrap border-b border-gray-200 pb-2">
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Start Date</Text>
          <Text className="text-sm font-semibold">{startDate}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">End Date</Text>
          <Text className="text-sm font-semibold">{endDate}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Budget</Text>
          <Text className="text-sm font-semibold">{budget}</Text>
        </View>
        <View className="mb-2 min-w-[40%] flex-1">
          <Text className="text-gray-500 text-xs">Spent</Text>
          <Text className="text-sm font-semibold">{spent}</Text>
        </View>
      </View>
      {/* Manager */}
      <View className="mb-3">
        <Text className="mb-1 text-xs font-medium">Property Manager</Text>
        <View className="flex-row items-center gap-2">
          <View
            className={`h-9 w-9 items-center justify-center rounded-full ${manager.bgColor || 'bg-sky-100'}`}
          >
            <Text
              className={`text-sm font-bold ${manager.color || 'text-sky-600'}`}
            >
              {manager.initials}
            </Text>
          </View>
          <View>
            <Text className="text-sm font-semibold">{manager.name}</Text>
            <Text className="text-gray-500 text-xs">{manager.company}</Text>
          </View>
        </View>
      </View>
      {/* Button */}
      <TouchableOpacity
        className="flex-row items-center justify-center rounded-lg bg-indigo-600 py-3"
        onPress={handlePress}
      >
        <Ionicons name="eye" size={18} color="#fff" />
        <Text className="ml-2 font-semibold text-white">View Details</Text>
      </TouchableOpacity>
    </View>
  )
}
