import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'

export function Header({ onSearch }: { onSearch?: () => void }) {
  return (
    <View className="flex-row items-center justify-between bg-white px-2 pb-3 pt-2">
      <Text className="text-xl font-bold text-black">Projects</Text>
      <TouchableOpacity
        className="bg-gray-50 h-9 w-9 items-center justify-center rounded-xl"
        onPress={onSearch}
        activeOpacity={0.7}
      >
        <Ionicons name="search" size={18} color="#222" />
      </TouchableOpacity>
    </View>
  )
}
