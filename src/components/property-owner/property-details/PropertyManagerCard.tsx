import React from 'react'
import { Pressable, Text, View } from 'react-native'
import {
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  UserGroupIcon
} from 'react-native-heroicons/solid'

import { crossPlatformShadow } from '@/theme/shadow'

export function PropertyManagerCard() {
  return (
    <View
      className="mb-4 rounded-lg bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <UserGroupIcon size={18} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Property Manager
        </Text>
      </View>
      <View className="mb-3 flex-row items-center">
        <View className="mr-3 h-12 w-12 items-center justify-center rounded-full bg-indigo-50">
          <Text className="text-lg font-bold text-indigo-600">SJ</Text>
        </View>
        <View>
          <Text className="text-base font-semibold text-gray-900">
            <PERSON>
          </Text>
          <Text className="text-gray-500 mt-0.5 text-xs">
            Property Manager since Jun 2019
          </Text>
        </View>
      </View>
      <View className="mb-3 h-px bg-gray-200" />
      <View className="flex-row gap-2">
        <Pressable
          className="flex-row items-center rounded-lg bg-green-50 px-3 py-1.5"
          onPress={() => {}}
        >
          <PhoneIcon size={16} color="#22c55e" className="mr-1" />
          <Text className="text-sm font-medium text-green-600">Call</Text>
        </Pressable>
        <Pressable
          className="flex-row items-center rounded-lg bg-indigo-50 px-3 py-1.5"
          onPress={() => {}}
        >
          <ChatBubbleLeftRightIcon size={16} color="#6366f1" className="mr-1" />
          <Text className="text-sm font-medium text-indigo-600">Message</Text>
        </Pressable>
      </View>
    </View>
  )
}
