import React from 'react'
import { Text, View } from 'react-native'
import { WrenchScrewdriverIcon } from 'react-native-heroicons/outline'

import classNames from '@/utils/classname'

export interface MaintenanceRecord {
  title: string
  date: string // e.g. 'Jun 12, 2023'
  description: string
  cost: string // e.g. '$175.00'
}

export interface MaintenanceHistoryProps {
  records: MaintenanceRecord[]
  className?: string
}

/**
 * MaintenanceHistory component
 * Displays a list of maintenance records in a styled card
 */
export const MaintenanceHistory = ({
  records,
  className
}: MaintenanceHistoryProps) => {
  return (
    <View className={classNames('rounded-2xl bg-white p-3', className)}>
      {/* Title Row */}
      <View className="mb-2 flex-row items-center">
        <WrenchScrewdriverIcon size={20} color="#6366F1" />
        <Text className="ml-2 text-base font-bold text-gray-900">
          Maintenance History
        </Text>
      </View>
      {/* Records List */}
      {records.map((rec, idx) => (
        <View
          key={rec.title + rec.date}
          className={classNames(
            'mb-3 rounded-xl bg-[#F9FAFB] px-3 py-2',
            idx === records.length - 1 && 'mb-0'
          )}
        >
          <View className="flex-row items-start justify-between">
            <Text className="text-base font-bold text-gray-900">
              {rec.title}
            </Text>
            <Text className="text-gray-400 mt-0.5 text-xs">{rec.date}</Text>
          </View>
          <Text className="mt-1 text-xs leading-snug text-gray-700">
            {rec.description}
          </Text>
          <View className="mt-2 flex-row items-end justify-between">
            <Text className="text-gray-400 text-xs">Cost:</Text>
            <Text className="text-base font-extrabold text-gray-900">
              {rec.cost}
            </Text>
          </View>
        </View>
      ))}
    </View>
  )
}
