import React from 'react'
import { Text, View } from 'react-native'
import { HomeIcon } from 'react-native-heroicons/solid'

import { crossPlatformShadow } from '@/theme/shadow'

export function PropertyDetailsCard() {
  return (
    <View
      className="mb-4 rounded-lg bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <HomeIcon size={18} color="#6366f1" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Property Details
        </Text>
      </View>
      <View className="flex-row flex-wrap">
        <View className="min-w-[50%] flex-1">
          <Text className="text-gray-500 mb-0.5 text-xs">Property Type</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">
            Single Family Home
          </Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Square Footage</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">
            2,240 sq ft
          </Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Bedrooms</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">3</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Parking</Text>
          <Text className="text-base font-bold text-gray-900">
            2-Car Garage
          </Text>
        </View>
        <View className="min-w-[50%] flex-1">
          <Text className="text-gray-500 mb-0.5 text-xs">Year Built</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">2008</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Lot Size</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">
            0.25 acres
          </Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Bathrooms</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">2</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Last Renovation</Text>
          <Text className="text-base font-bold text-gray-900">2020</Text>
        </View>
      </View>
    </View>
  )
}
