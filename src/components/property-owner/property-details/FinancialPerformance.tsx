import React from 'react'
import { Text, View } from 'react-native'
import { CurrencyDollarIcon } from 'react-native-heroicons/solid'

import { crossPlatformShadow } from '@/theme/shadow'

export function FinancialPerformance() {
  return (
    <View
      className="mb-4 rounded-lg bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <CurrencyDollarIcon size={18} color="#8b5cf6" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Financial Performance
        </Text>
      </View>
      <View className="mb-4 flex-row justify-between gap-2">
        <View className="bg-gray-50 flex-1 items-center rounded-xl px-2 py-3">
          <Text className="text-xl font-bold text-gray-900">$2,450</Text>
          <Text className="text-gray-500 mt-1 text-xs">Monthly Rent</Text>
        </View>
        <View className="bg-gray-50 flex-1 items-center rounded-xl px-2 py-3">
          <Text className="text-xl font-bold text-gray-900">$320K</Text>
          <Text className="text-gray-500 mt-1 text-xs">Property Value</Text>
        </View>
        <View className="bg-gray-50 flex-1 items-center rounded-xl px-2 py-3">
          <Text className="text-xl font-bold text-gray-900">9.8%</Text>
          <Text className="text-gray-500 mt-1 text-xs">Annual ROI</Text>
        </View>
      </View>
      <View className="mt-1 rounded-xl bg-gray-100 px-2 py-2">
        <View className="mb-1 flex-row items-center justify-center gap-4">
          <View className="mr-2 flex-row items-center">
            <View className="mr-1 h-4 w-4 items-center justify-center rounded-full border-2 border-green-500">
              <View className="h-2 w-2 rounded-full bg-green-500" />
            </View>
            <Text className="text-xs font-semibold text-green-600">Income</Text>
          </View>
          <View className="flex-row items-center">
            <View className="mr-1 h-4 w-4 items-center justify-center rounded-full border-2 border-red-400">
              <View className="h-2 w-2 rounded-full bg-red-400" />
            </View>
            <Text className="text-xs font-semibold text-red-500">Expenses</Text>
          </View>
        </View>
        <View className="h-32 w-full items-end justify-end">
          <Text className="text-gray-400 mt-12 text-center">
            [Line Chart Here]
          </Text>
        </View>
      </View>
    </View>
  )
}
