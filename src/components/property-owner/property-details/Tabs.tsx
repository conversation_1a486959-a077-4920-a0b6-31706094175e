import React from 'react'
import { Pressable, Text, View } from 'react-native'
import Animatable from 'react-native-animatable'

export function Tabs({
  tabs = [
    { key: 'details', label: 'Details' },
    { key: 'maintenance', label: 'Maintenance' }
  ],
  value,
  onChange
}: {
  tabs?: { key: string; label: string }[]
  value: string
  onChange: (key: string) => void
}) {
  return (
    <View className="flex-row rounded-md bg-white p-1">
      {tabs.map(tab => {
        const isActive = value === tab.key
        return (
          <Animatable.View animation={isActive ? 'pulse' : undefined}>
            <Pressable
              key={tab.key}
              className={`mx-1 my-1 flex-1 items-center justify-center rounded-lg py-2 ${isActive ? 'bg-indigo-600 shadow-sm' : 'bg-transparent'}`}
              onPress={() => onChange(tab.key)}
            >
              <Text
                className={`text-base font-semibold ${isActive ? 'text-white' : 'text-gray-700'}`}
              >
                {tab.label}
              </Text>
            </Pressable>
          </Animatable.View>
        )
      })}
    </View>
  )
}
