import React from 'react'
import { Pressable, Text, View } from 'react-native'
import {
  ArrowLeftIcon,
  EllipsisVerticalIcon,
  PencilSquareIcon
} from 'react-native-heroicons/outline'

export function Header({
  onBack,
  onEdit,
  onMore
}: {
  onBack?: () => void
  onEdit?: () => void
  onMore?: () => void
}) {
  return (
    <View
      className="flex-row items-center justify-between bg-white px-4 pb-2 pt-4"
      style={{ top: 0, zIndex: 50 }}
    >
      <Pressable
        className="bg-gray-50 h-10 w-10 items-center justify-center rounded-xl shadow-sm"
        onPress={onBack}
        android_ripple={{ color: '#f3f4f6', borderless: true }}
      >
        <ArrowLeftIcon size={20} color="#222" />
      </Pressable>
      <Text className="-ml-10 flex-1 text-center text-lg font-bold text-black">
        Property Details
      </Text>
      <View className="flex-row gap-2">
        <Pressable
          className="bg-gray-50 h-10 w-10 items-center justify-center rounded-xl shadow-sm"
          onPress={onEdit}
          android_ripple={{ color: '#f3f4f6', borderless: true }}
        >
          <PencilSquareIcon size={18} color="#222" />
        </Pressable>
        <Pressable
          className="bg-gray-50 h-10 w-10 items-center justify-center rounded-xl shadow-sm"
          onPress={onMore}
          android_ripple={{ color: '#f3f4f6', borderless: true }}
        >
          <EllipsisVerticalIcon size={18} color="#222" />
        </Pressable>
      </View>
    </View>
  )
}
