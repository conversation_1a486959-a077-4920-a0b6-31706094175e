import React from 'react'
import { ImageBackground, Text, View } from 'react-native'

import { crossPlatformShadow } from '@/theme/shadow'

export function PropertyOverviewCard() {
  return (
    <View
      className="mx-3 mb-4 mt-3 rounded-md bg-white"
      style={[crossPlatformShadow('default')]}
    >
      <View className="overflow-hidden rounded-md">
        <ImageBackground
          source={{
            uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80'
          }}
          className="h-40 w-full justify-end"
          resizeMode="cover"
        >
          <View className="absolute right-3 top-3 z-10">
            <View className="rounded-full bg-green-100 px-3 py-1">
              <Text className="text-xs font-semibold text-green-700">
                Occupied
              </Text>
            </View>
          </View>
          <View
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              height: 80,
              borderBottomLeftRadius: 16,
              borderBottomRightRadius: 16,
              overflow: 'hidden',
              backgroundColor: 'transparent'
            }}
            pointerEvents="none"
          >
            <View
              style={{
                flex: 1
                // backgroundColor: 'rgba(0,0,0,0.6)'
              }}
            />
          </View>
          <View className="p-4">
            <Text className="mb-1 text-lg font-bold text-white">
              123 Main Street, Austin, TX
            </Text>
            <Text className="text-sm text-gray-100">
              Single Family Home · 3 bed, 2 bath
            </Text>
          </View>
        </ImageBackground>
      </View>
    </View>
  )
}
