import React from 'react'
import { Text, View } from 'react-native'
import { DocumentTextIcon } from 'react-native-heroicons/solid'

import { crossPlatformShadow } from '@/theme/shadow'

export function FinancialDetailsCard() {
  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-3 flex-row items-center">
        <DocumentTextIcon size={18} color="#a78bfa" className="mr-2" />
        <Text className="text-base font-bold text-gray-900">
          Financial Details
        </Text>
      </View>
      <View className="flex-row flex-wrap">
        <View className="min-w-[50%] flex-1">
          <Text className="text-gray-500 mb-0.5 text-xs">Purchase Price</Text>
          <Text className="mb-3 text-lg font-bold text-gray-900">$289,000</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Current Value</Text>
          <Text className="mb-3 text-lg font-bold text-gray-900">$320,000</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">
            Avg. Monthly Maintenance
          </Text>
          <Text className="text-lg font-bold text-gray-900">$180</Text>
        </View>
        <View className="min-w-[50%] flex-1">
          <Text className="text-gray-500 mb-0.5 text-xs">Purchase Date</Text>
          <Text className="mb-3 text-base font-bold text-gray-900">
            Apr 12, 2019
          </Text>
          <Text className="text-gray-500 mb-0.5 text-xs">Monthly Mortgage</Text>
          <Text className="mb-3 text-lg font-bold text-gray-900">$1,450</Text>
          <Text className="text-gray-500 mb-0.5 text-xs">
            Monthly Cash Flow
          </Text>
          <Text className="text-lg font-bold text-gray-900">$470</Text>
        </View>
      </View>
    </View>
  )
}
