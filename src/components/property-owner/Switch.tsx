import React from 'react'
import { Pressable, View } from 'react-native'

import classNames from '@/utils/classname'

export interface SwitchProps {
  /**
   * The value of the switch (on/off)
   */
  value?: boolean
  /**
   * Callback when the value changes
   */
  onChange?: (value: boolean) => void
  /**
   * Whether the switch is disabled
   */
  disabled?: boolean
  /**
   * Optional className for the container
   */
  className?: string
  /**
   * Optional testID for testing
   */
  testID?: string
}

/**
 * PropertyOwnerSwitch - a custom switch component for property owner UI
 * Matches the style in the provided screenshots (green for on, gray for off)
 */
export function Switch({
  value = false,
  onChange,
  disabled = false,
  className,
  testID
}: SwitchProps) {
  const handlePress = () => {
    if (!disabled) {
      onChange?.(!value)
    }
  }

  return (
    <Pressable
      testID={testID}
      onPress={handlePress}
      disabled={disabled}
      className={classNames(
        'h-6 w-12 rounded-full',
        disabled ? 'opacity-50' : '',
        value ? 'bg-emerald-600' : 'border border-[#d1d5db] bg-[#e5e7eb]',
        'transition-colors duration-200',
        className
      )}
      accessibilityRole="switch"
      accessibilityState={{ checked: value, disabled }}
    >
      <View
        className={classNames(
          'absolute top-0.5 h-5 w-5 rounded-full bg-white',
          value ? 'right-0.5' : 'left-0.5',
          'shadow'
        )}
        style={{}}
      />
    </Pressable>
  )
}
