import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome5, Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'

export interface RecentProjectProps {
  type: string
  address: string
  date: string
  status: 'completed' | 'pending' | 'scheduled'
}

export const RecentProjectCard = ({
  type,
  address,
  date,
  status
}: RecentProjectProps) => {
  // Icon and color mapping by type/status
  const iconInfo = {
    completed: {
      icon: <FontAwesome5 name="broom" size={18} color="#22c55e" />,
      bg: 'bg-green-100'
    },
    pending: {
      icon: <FontAwesome5 name="tools" size={18} color="#f59e0b" />,
      bg: 'bg-yellow-100'
    },
    scheduled: {
      icon: <FontAwesome5 name="tree" size={18} color="#38bdf8" />,
      bg: 'bg-sky-100'
    }
  }[status] || {
    // Fallback for any unexpected status
    icon: <FontAwesome5 name="question-circle" size={18} color="#6b7280" />,
    bg: 'bg-gray-100'
  }

  return (
    <View
      className="mb-2 flex-row items-center rounded-[8px] bg-white px-4 py-3"
      style={[crossPlatformShadow('default')]}
    >
      <View
        className={`h-10 w-10 items-center justify-center rounded-[8px] ${iconInfo.bg} mr-3`}
      >
        {iconInfo.icon}
      </View>
      <View className="flex-1">
        <Text className="mb-0.5 text-base font-bold text-black">{type}</Text>
        <Text className="text-gray-500 mb-0.5 text-xs">{address}</Text>
        <Text className="text-gray-500 mb-0.5 text-xs">{date}</Text>
        {status === 'completed' && (
          <View className="mt-1 flex-row items-center">
            <Ionicons name="checkmark-circle" size={14} color="#22c55e" />
            <Text className="ml-1 text-xs font-medium text-green-600">
              Completed
            </Text>
          </View>
        )}
        {status === 'pending' && (
          <View className="mt-1 flex-row items-center">
            <View className="h-3 w-3 rounded-full bg-yellow-400" />
            <Text className="ml-1 text-xs font-medium text-yellow-600">
              In Progress
            </Text>
          </View>
        )}
        {status === 'scheduled' && (
          <View className="mt-1 flex-row items-center">
            <View className="flex-row items-center rounded-md bg-sky-100 px-1.5 py-0.5">
              <Ionicons name="calendar" size={13} color="#38bdf8" />
              <Text className="ml-1 text-xs font-medium text-sky-600">
                Scheduled
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  )
}
