import React, { useState } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'

import { useProjectDetail } from '@/hooks/useProjectDetail'
import { crossPlatformShadow } from '@/theme/shadow'
import { getFullAddr } from '@/utils/addr'

import { ApprovalDetailsModal } from '../approvals/ApprovalDetailsModal'
import { ApprovalModal } from '../projects/ApprovalModal'

export interface PendingApprovalProps {
  title: string
  address: string
  pmAmount: string
  vendorAmount: string
  requestedDate: string
  statusText?: string
  projectId?: number
  onApprove?: () => void
  onDetails?: () => void
}

export const PendingApprovalCard = ({
  title,
  address,
  pmAmount,
  vendorAmount,
  requestedDate,
  statusText = 'Needs Approval',
  projectId,
  onApprove
  // onDetails
}: PendingApprovalProps) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailsVisible, setIsDetailsVisible] = useState(false)
  const [shouldFetchDetails, setShouldFetchDetails] = useState(false)

  // Only fetch project details when shouldFetchDetails is true
  const { project: projectDetail, loading: detailLoading } = useProjectDetail(
    shouldFetchDetails ? projectId || null : null
  )

  const handleApproveClick = () => {
    setIsModalVisible(true)
  }

  const handleModalClose = () => {
    setIsModalVisible(false)
  }

  const handleModalApprove = async () => {
    setIsModalVisible(false)
    onApprove?.()
  }

  const handleViewDetails = () => {
    setShouldFetchDetails(true)
    setIsDetailsVisible(true)
  }

  // Parse amount for modal display
  const parseAmount = (amountStr: string) => {
    const numericValue = parseFloat(amountStr.replace(/[$,]/g, ''))
    return isNaN(numericValue) ? 0 : numericValue
  }

  // Prepare data for ApprovalDetailsModal
  const getModalData = () => {
    if (projectDetail) {
      // Use API data when available
      return {
        projectName: projectDetail.projectName || title,
        propertyAddress: getFullAddr(projectDetail, { showZip: false }),
        projectManager:
          projectDetail.propertyManager?.name || 'Property Manager',
        requestDate: projectDetail.createdTime
          ? new Date(projectDetail.createdTime).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })
          : requestedDate,
        itemName: projectDetail.projectName || title + ' Needs Approval',
        itemCategory: projectDetail.projectType || 'Maintenance',
        priorityLevel: (() => {
          if (projectDetail.priority === 'HIGH') return 'HIGH'
          if (projectDetail.priority === 'MEDIUM') return 'MEDIUM'
          return 'LOW'
        })() as 'HIGH' | 'MEDIUM' | 'LOW',
        status: statusText,
        pmScopeAmount: pmAmount,
        vendorRequestAmount: vendorAmount,
        budgetVariance:
          projectDetail.estimateBudget && projectDetail.budgetUsed
            ? `$${(projectDetail.budgetUsed - projectDetail.estimateBudget).toFixed(2)}`
            : '+$70.00',
        budgetVariancePercent:
          projectDetail.estimateBudget && projectDetail.budgetUsed
            ? `${(((projectDetail.budgetUsed - projectDetail.estimateBudget) / projectDetail.estimateBudget) * 100).toFixed(1)}%`
            : '10.3%',
        budgetVarianceColor:
          projectDetail.estimateBudget && projectDetail.budgetUsed
            ? projectDetail.budgetUsed > projectDetail.estimateBudget
              ? 'text-red-500'
              : 'text-green-500'
            : 'text-orange-500',
        estimatedDuration:
          projectDetail.estimateStartDate && projectDetail.estimateCompleteDate
            ? `${Math.ceil((new Date(projectDetail.estimateCompleteDate).getTime() - new Date(projectDetail.estimateStartDate).getTime()) / (1000 * 60 * 60 * 24))} days`
            : '3-5 business days',
        itemDescription:
          projectDetail.description ||
          'Complete project details including scope, timeline, and budget considerations.'
      }
    } else {
      // Fallback to props data
      return {
        projectName: title,
        propertyAddress: address,
        projectManager: 'Sarah Johnson',
        requestDate: requestedDate,
        itemName: title + ' Needs Approval',
        itemCategory: 'HVAC System',
        priorityLevel: 'HIGH' as const,
        status: statusText,
        pmScopeAmount: pmAmount,
        vendorRequestAmount: vendorAmount,
        budgetVariance: '+$70.00',
        budgetVariancePercent: '10.3%',
        budgetVarianceColor: 'text-orange-500',
        estimatedDuration: '3-5 business days',
        itemDescription:
          'Complete HVAC system repair including ductwork inspection, filter replacement, and thermostat calibration. This work includes diagnostic testing and performance verification.'
      }
    }
  }

  return (
    <>
      <View
        className="mb-4 rounded-[8px] bg-white px-4 py-3"
        style={[crossPlatformShadow('default')]}
      >
        {/* Title and badge */}
        <View className="mb-1 flex-row items-center justify-between">
          <Text className="text-base font-bold text-black">{title}</Text>
          <View className="min-h-[22px] min-w-[90px] items-center justify-center rounded-full bg-sky-50 px-2 py-0.5">
            <Text className="text-xs font-medium text-sky-600">
              {statusText}
            </Text>
          </View>
        </View>
        {/* Address */}
        <Text className="text-gray-500 mb-2 text-xs">{address}</Text>
        {/* Table header and values */}
        <View className="mb-1 flex-row justify-between">
          <Text className="text-gray-400 text-xs">PM Scope Amount</Text>
          <Text className="text-gray-400 text-xs">Vendor Request Amount</Text>
          <Text className="text-gray-400 text-xs">Requested</Text>
        </View>
        <View className="mb-2 flex-row justify-between">
          <Text className="flex-1 text-base font-bold text-gray-900">
            {pmAmount}
          </Text>
          <Text className="flex-1 text-center text-base font-bold text-gray-900">
            {vendorAmount}
          </Text>
          <Text className="flex-1 text-right text-base font-bold text-gray-900">
            {requestedDate}
          </Text>
        </View>
        <View className="my-2 h-px w-full bg-gray-100" />
        {/* Buttons */}
        <View className="mt-2 flex-row gap-2">
          <TouchableOpacity
            className="flex-1 items-center justify-center rounded-xl bg-green-500 py-3"
            activeOpacity={0.8}
            onPress={handleApproveClick}
          >
            <Text className="font-semibold text-white">Approve</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 items-center justify-center rounded-xl bg-indigo-600 py-3"
            activeOpacity={0.8}
            onPress={handleViewDetails}
            disabled={detailLoading}
          >
            <Text className="font-semibold text-white">
              {detailLoading ? 'Loading...' : 'View Details'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <ApprovalModal
        visible={isModalVisible}
        onClose={handleModalClose}
        onApprove={handleModalApprove}
        itemName={title}
        projectName={title}
        property={address}
        amount={parseAmount(vendorAmount)}
      />
      <ApprovalDetailsModal
        visible={isDetailsVisible}
        onClose={() => setIsDetailsVisible(false)}
        data={getModalData()}
      />
    </>
  )
}
