import React from 'react'
import { Text, View } from 'react-native'

import classNames from '@/utils/classname'

export interface HomeHeaderPersonalProps {
  /**
   * User name, e.g. '<PERSON>'
   */
  name: string
  /**
   * Avatar initials, e.g. 'JD'
   */
  avatar: string
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * HomeHeaderPersonal Component
 * Top blue header with welcome text, user name, avatar, and bottom arc
 */
export const DashboardHeaderWelcom = ({
  name,
  avatar,
  className
}: HomeHeaderPersonalProps) => {
  return (
    <View
      className={classNames(
        'relative overflow-hidden rounded-b-[32px] bg-indigo-600 pb-6',
        className
      )}
    >
      <View
        className="absolute left-0 right-0 h-8 overflow-hidden"
        style={{ pointerEvents: 'none' }}
      >
        {/* <View className="absolute bottom-0 left-[-20px] h-16 w-16 rounded-full bg-white" />
        <View className="absolute bottom-0 right-[-20px] h-16 w-16 rounded-full bg-white" /> */}
        <View
          className="mx-auto h-12 w-full bg-indigo-600"
          style={{ top: -8 }}
        />
      </View>
      <View className="flex-row items-center justify-between px-4 pb-4 pt-10">
        <View>
          <Text className="mb-1 text-xl font-bold text-white">
            Hello, {name}
          </Text>
          <Text className="text-sm text-white opacity-90">
            Welcome to your owner dashboard
          </Text>
        </View>
        <View className="h-12 w-12 items-center justify-center rounded-full bg-white">
          <Text className="text-lg font-bold text-indigo-600">{avatar}</Text>
        </View>
      </View>
    </View>
  )
}
