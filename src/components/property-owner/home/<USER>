import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome5, Ionicons } from '@expo/vector-icons'
import AntDesign from '@expo/vector-icons/AntDesign'
import type { ReactNode } from 'react'

import { crossPlatformShadow } from '@/theme/shadow'
import classNames from '@/utils/classname'

export type StatCardIconType =
  | 'dollar'
  | 'credit-card'
  | 'building'
  | 'tools'
  | 'checkcircle'
  | 'custom'

export interface StatCardProps {
  /**
   * Main title, e.g. 'Monthly Income'
   */
  title: string
  /**
   * Main value, e.g. '$42,500'
   */
  value: string | number
  /**
   * Icon type or custom icon
   */
  icon?: StatCardIconType | ReactNode
  /**
   * Trend info, e.g. '+4.5% from last month' or '8 completed'
   */
  trendText?: string
  /**
   * Trend type: 'up', 'down', 'dot', or undefined
   */
  trendType?: 'up' | 'down' | 'dot'
  /**
   * Trend color: 'success', 'danger', 'default'
   */
  trendColor?: 'success' | 'danger' | 'default'
  /**
   * Optional: container className
   */
  className?: string
}

const iconMap: Record<StatCardIconType, ReactNode> = {
  'dollar': <FontAwesome5 name="dollar-sign" size={16} color="#4f46e5" />,
  'credit-card': <FontAwesome5 name="credit-card" size={16} color="#4f46e5" />,
  'building': <FontAwesome5 name="building" size={16} color="#4f46e5" />,
  'tools': <FontAwesome5 name="tools" size={16} color="#4f46e5" />,
  'checkcircle': <AntDesign name="checkcircle" size={16} color="#4f46e5" />,
  'custom': null
}

const trendColorMap = {
  success: 'text-green-600',
  danger: 'text-red-500',
  default: 'text-gray-500'
}

export const StatCard = ({
  title,
  value,
  icon = 'dollar',
  trendText,
  trendType,
  trendColor = 'success',
  className
}: StatCardProps) => {
  return (
    <View
      className={classNames('rounded-[8px] bg-white p-4', className)}
      style={[crossPlatformShadow('default')]}
    >
      {/* Title & Icon */}
      <View className="mb-2 flex-row items-center gap-1">
        {typeof icon === 'string' ? iconMap[icon as StatCardIconType] : icon}
        <Text className="text-gray-500 ml-1 text-xs font-medium">{title}</Text>
      </View>
      {/* Value */}
      <Text className="mb-1 text-2xl font-extrabold text-black">{value}</Text>
      {/* Trend/Extra */}
      {trendText ? (
        <View className="flex-row items-center">
          {trendType === 'up' && (
            <Ionicons
              name="arrow-up"
              size={14}
              color="#22c55e"
              className="mr-1"
            />
          )}
          {trendType === 'down' && (
            <Ionicons
              name="arrow-down"
              size={14}
              color="#ef4444"
              className="mr-1"
            />
          )}
          {trendType === 'dot' && (
            <View className="mb-1 mr-1 mt-1 h-3 w-3 rounded-full bg-green-500" />
          )}
          <Text
            className={classNames(
              'text-xs font-medium',
              trendColorMap[trendColor] || trendColorMap.success
            )}
          >
            {trendText}
          </Text>
        </View>
      ) : null}
    </View>
  )
}
