import React from 'react'

import classNames from '@/utils/classname'

import { DashboardHeaderWelcom } from './DashboardHeaderWelcom'
import type { StatsGridProps } from './StatsGrid'
import { StatsGrid } from './StatsGrid'

export interface DashboardHeaderProps {
  name: string
  avatar: string
  stats: StatsGridProps['items']
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * DashboardHeader Component
 */
export const DashboardHeader = ({
  name,
  avatar,
  stats,
  className
}: DashboardHeaderProps) => {
  return (
    <>
      <DashboardHeaderWelcom name={name} avatar={avatar} />
      <StatsGrid
        items={stats}
        className={classNames('-mt-8 px-3', className)}
      />
    </>
  )
}
