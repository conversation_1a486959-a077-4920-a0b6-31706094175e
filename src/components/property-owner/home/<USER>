import React, { useState } from 'react'
import { Dimensions, Text, TouchableOpacity, View } from 'react-native'
import { <PERSON><PERSON><PERSON> } from 'react-native-chart-kit'
import { <PERSON><PERSON><PERSON> } from 'react-native-chart-kit'
import type { ReactNode } from 'react'

// const chartColors = [
//   '#6366f1', // Plumbing
//   '#f472b6', // HVAC
//   '#22c55e', // Electrical
//   '#f59e0b', // Landscaping
//   '#a78bfa', // Painting
//   '#0ea5e9' // Carpentry
// ]

const legendLabels = [
  { label: 'Plumbing', color: '#6366f1' },
  { label: 'HVAC', color: '#f472b6' },
  { label: 'Electrical', color: '#22c55e' },
  { label: 'Landscaping', color: '#f59e0b' },
  { label: 'Painting', color: '#a78bfa' },
  { label: 'Carpentry', color: '#0ea5e9' }
]

const pieData = [
  {
    name: 'Plumbing',
    population: 4850,
    color: '#6366f1',
    legendFontColor: '#6366f1',
    legendFontSize: 12
  },
  {
    name: 'HVAC',
    population: 6450,
    color: '#f472b6',
    legendFontColor: '#f472b6',
    legendFontSize: 12
  },
  {
    name: 'Electrical',
    population: 4020,
    color: '#22c55e',
    legendFontColor: '#22c55e',
    legendFontSize: 12
  },
  {
    name: 'Landscaping',
    population: 2250,
    color: '#f59e0b',
    legendFontColor: '#f59e0b',
    legendFontSize: 12
  },
  {
    name: 'Painting',
    population: 3680,
    color: '#a78bfa',
    legendFontColor: '#a78bfa',
    legendFontSize: 12
  },
  {
    name: 'Carpentry',
    population: 3900,
    color: '#0ea5e9',
    legendFontColor: '#0ea5e9',
    legendFontSize: 12
  }
]

const tableData = [
  { category: 'Plumbing', projects: 4, budget: '$5,000', actual: '$4,850' },
  { category: 'HVAC', projects: 3, budget: '$6,200', actual: '$6,450' },
  { category: 'Electrical', projects: 2, budget: '$3,800', actual: '$4,020' }
]

const tabs = [
  { key: 'category', label: 'By Category' },
  { key: 'trends', label: 'By Trends' },
  { key: 'month', label: 'By Month' }
]

export const VendorCostAnalyticsCard = () => {
  const [activeTab, setActiveTab] = useState('category')
  const screenWidth = Dimensions.get('window').width - 48

  const barData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        data: [10000, 12000, 12000, 14000, 16000, 16500],
        color: () => '#6366f1' // Budget
      },
      {
        data: [9000, 12000, 12000, 13000, 15000, 16000],
        color: () => '#f472b6' // Actual Cost
      }
    ],
    legend: ['Budget', 'Actual Cost']
  }

  const monthPieData = [
    {
      name: 'Jan',
      population: 4850,
      color: '#6366f1',
      legendFontColor: '#6366f1',
      legendFontSize: 12
    },
    {
      name: 'Feb',
      population: 6450,
      color: '#f472b6',
      legendFontColor: '#f472b6',
      legendFontSize: 12
    },
    {
      name: 'Mar',
      population: 4020,
      color: '#22c55e',
      legendFontColor: '#22c55e',
      legendFontSize: 12
    },
    {
      name: 'Apr',
      population: 2250,
      color: '#f59e0b',
      legendFontColor: '#f59e0b',
      legendFontSize: 12
    },
    {
      name: 'May',
      population: 3680,
      color: '#a78bfa',
      legendFontColor: '#a78bfa',
      legendFontSize: 12
    },
    {
      name: 'Jun',
      population: 3900,
      color: '#0ea5e9',
      legendFontColor: '#0ea5e9',
      legendFontSize: 12
    }
  ]

  const monthLegend = [
    { label: 'Jan', color: '#6366f1' },
    { label: 'Feb', color: '#f472b6' },
    { label: 'Mar', color: '#22c55e' },
    { label: 'Apr', color: '#f59e0b' },
    { label: 'May', color: '#a78bfa' },
    { label: 'Jun', color: '#0ea5e9' }
  ]

  const trendsLegend = [
    { label: 'Budget', color: '#6366f1' },
    { label: 'Actual Cost', color: '#f472b6' }
  ]

  const categoryLegend = legendLabels

  let chart: ReactNode = null
  let legend: { label: string; color: string }[] = []
  if (activeTab === 'category') {
    chart = (
      <View className="items-center">
        <PieChart
          data={pieData}
          width={screenWidth}
          height={180}
          chartConfig={{ color: () => '#000', labelColor: () => '#000' }}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="0"
          hasLegend={false}
          center={[0, 0]}
        />
      </View>
    )
    legend = categoryLegend
  } else if (activeTab === 'trends') {
    chart = (
      <View className="items-center">
        <BarChart
          data={{
            labels: barData.labels,
            datasets: [
              {
                data: barData.datasets[0]?.data || [],
                color: barData.datasets[0]?.color
              },
              {
                data: barData.datasets[1]?.data || [],
                color: barData.datasets[1]?.color
              }
            ]
          }}
          width={screenWidth}
          height={180}
          yAxisLabel=""
          yAxisSuffix=""
          chartConfig={{
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0,0,0,${opacity})`,
            labelColor: (opacity = 1) => `rgba(0,0,0,${opacity})`,
            fillShadowGradient: '#6366f1',
            fillShadowGradientOpacity: 1,
            barPercentage: 0.5,
            propsForBackgroundLines: { stroke: '#e5e7eb' }
          }}
          fromZero
          showBarTops={false}
          withInnerLines={true}
          withHorizontalLabels={true}
          withCustomBarColorFromData={true}
          flatColor={true}
        />
      </View>
    )
    legend = trendsLegend
  } else if (activeTab === 'month') {
    chart = (
      <View className="items-center">
        <PieChart
          data={monthPieData}
          width={screenWidth}
          height={180}
          chartConfig={{ color: () => '#000', labelColor: () => '#000' }}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="0"
          hasLegend={false}
          center={[0, 0]}
        />
      </View>
    )
    legend = monthLegend
  }

  return (
    <View className="mb-4 rounded-2xl bg-white px-4 py-3 shadow-sm">
      {/* Tabs */}
      <View className="mb-2 flex-row rounded-lg bg-gray-100 p-1">
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.key}
            className={
              'flex-1 items-center justify-center rounded-md py-2.5 ' +
              (activeTab === tab.key ? 'bg-white' : '')
            }
            onPress={() => setActiveTab(tab.key)}
          >
            <Text
              className={
                'text-sm font-semibold ' +
                (activeTab === tab.key ? 'text-indigo-600' : 'text-gray-500')
              }
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {/* Chart */}
      {chart}
      {/* Legend */}
      <View className="mb-2 mt-2 flex-row flex-wrap justify-center">
        {(Array.isArray(legend) ? legend : []).map(item => (
          <View
            key={item?.label ?? ''}
            className="mb-1 mr-4 flex-row items-center"
          >
            <View
              style={{ backgroundColor: item?.color ?? '#ccc' }}
              className="mr-2 h-2 w-6 rounded-full"
            />
            <Text className="text-xs" style={{ color: item?.color ?? '#000' }}>
              {item?.label ?? ''}
            </Text>
          </View>
        ))}
      </View>
      {/* Table */}
      <View className="mt-2 border-t border-gray-200 pt-2">
        <View className="mb-1 flex-row">
          <Text className="text-gray-500 flex-1 text-xs font-bold">
            VENDOR CATEGORY
          </Text>
          <Text className="text-gray-500 w-14 text-center text-xs font-bold">
            PROJECTS
          </Text>
          <Text className="text-gray-500 w-16 text-center text-xs font-bold">
            BUDGET
          </Text>
          <Text className="text-gray-500 w-20 text-center text-xs font-bold">
            ACTUAL COST
          </Text>
        </View>
        {tableData.map(row => (
          <View key={row.category} className="mb-0.5 flex-row">
            <Text className="flex-1 text-sm font-semibold">{row.category}</Text>
            <Text className="w-14 text-center text-sm font-semibold">
              {row.projects}
            </Text>
            <Text className="w-16 text-center text-sm font-semibold">
              {row.budget}
            </Text>
            <Text className="w-20 text-center text-sm font-semibold">
              {row.actual}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )
}
