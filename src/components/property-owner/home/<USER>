import React from 'react'
import { View } from 'react-native'

import classNames from '@/utils/classname'

import type { StatCardProps } from './StatCard'
import { StatCard } from './StatCard'

export interface StatsGridProps {
  items: StatCardProps[]
  /**
   * Optional: container className
   */
  className?: string
}

/**
 * StatsGrid Component
 * 2-column grid for StatCard, auto wrap, consistent gap
 */
export const StatsGrid = ({ items, className }: StatsGridProps) => {
  return (
    <View
      className={classNames('flex-row flex-wrap justify-between', className)}
    >
      {items.map((item, idx) => (
        <View
          key={idx}
          className="mb-3 w-full px-0.5 sm:w-1/2 md:w-1/2 lg:w-1/2"
          style={{ width: '48%' }}
        >
          <StatCard {...item} />
        </View>
      ))}
    </View>
  )
}
