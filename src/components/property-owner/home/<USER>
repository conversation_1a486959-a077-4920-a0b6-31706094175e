import React from 'react'
import { Text, View } from 'react-native'
import {
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons
} from '@expo/vector-icons'

export interface ActivityLogItem {
  date: string
  iconType: 'message' | 'alert' | 'payment' | 'photo' | 'budget' | 'other'
  iconBg?: string
  iconColor?: string
  title: string
  address: string
  content: string
  contentIcon?:
    | 'mail'
    | 'calendar'
    | 'check'
    | 'photo'
    | 'exclamation'
    | 'other'
}

export interface ActivityLogProps {
  items: ActivityLogItem[]
}

export const ActivityLogsCard = ({ items }: ActivityLogProps) => {
  const grouped = items.reduce(
    (acc, item) => {
      if (!acc[item.date]) acc[item.date] = []
      acc[item.date]?.push(item)
      return acc
    },
    {} as Record<string, ActivityLogItem[]>
  )

  const renderIcon = (
    type: ActivityLogItem['iconType'],
    bg?: string,
    color?: string
  ) => {
    switch (type) {
      case 'message':
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg ${bg || 'bg-indigo-100'}`}
          >
            <Ionicons
              name="chatbubble-ellipses"
              size={20}
              color={color || '#6366f1'}
            />
          </View>
        )
      case 'alert':
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg ${bg || 'bg-rose-100'}`}
          >
            <Ionicons name="alert" size={20} color={color || '#ef4444'} />
          </View>
        )
      case 'payment':
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg ${bg || 'bg-green-100'}`}
          >
            <FontAwesome5
              name="file-invoice-dollar"
              size={18}
              color={color || '#22c55e'}
            />
          </View>
        )
      case 'photo':
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg ${bg || 'bg-sky-100'}`}
          >
            <Ionicons name="camera" size={20} color={color || '#0ea5e9'} />
          </View>
        )
      case 'budget':
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg ${bg || 'bg-yellow-100'}`}
          >
            <MaterialCommunityIcons
              name="file-document-edit"
              size={20}
              color={color || '#f59e0b'}
            />
          </View>
        )
      default:
        return (
          <View
            className={`h-8 w-8 items-center justify-center rounded-lg bg-gray-200`}
          >
            <Ionicons name="notifications" size={20} color="#6b7280" />
          </View>
        )
    }
  }

  const renderContentIcon = (icon?: ActivityLogItem['contentIcon']) => {
    switch (icon) {
      case 'mail':
        return <Ionicons name="mail" size={14} color="#000" />
      case 'calendar':
        return <Ionicons name="calendar" size={14} color="#000" />
      case 'check':
        return <Ionicons name="checkmark-circle" size={14} color="#22c55e" />
      case 'photo':
        return <Ionicons name="images" size={14} color="#0ea5e9" />
      case 'exclamation':
        return <Ionicons name="alert-circle" size={14} color="#f59e0b" />
      default:
        return null
    }
  }

  const getContentColor = (
    icon?: ActivityLogItem['contentIcon'],
    type?: ActivityLogItem['iconType']
  ) => {
    switch (icon) {
      case 'check':
        return 'text-green-600'
      case 'photo':
        return 'text-sky-600'
      case 'exclamation':
        return 'text-yellow-600'
      case 'mail':
        return 'text-black'
      case 'calendar':
        return 'text-rose-500'
      default:
        if (type === 'alert') return 'text-rose-500'
        return 'text-black'
    }
  }

  return (
    <View>
      {Object.entries(grouped).map(([date, logs]) => (
        <View key={date} className="mb-0">
          <Text className="text-gray-500 mb-1 text-xs font-medium">{date}</Text>
          {logs.map((item, idx) => (
            <View key={idx} className="mb-3">
              <View className="mb-0 flex-row items-center rounded-[8px] bg-white px-4 py-3">
                {renderIcon(item.iconType, item.iconBg, item.iconColor)}
                <View className="ml-3 flex-1">
                  <Text className="mb-0.5 text-sm font-semibold text-black">
                    {item.title}
                  </Text>
                  <Text className="text-gray-500 mb-0.5 text-xs">
                    {item.address}
                  </Text>
                  <View className="mt-0.5 flex-row items-center">
                    {renderContentIcon(item.contentIcon)}
                    <Text
                      className={`ml-1 text-xs ${getContentColor(item.contentIcon, item.iconType)}`}
                    >
                      {item.content}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      ))}
    </View>
  )
}
