import React from 'react'
import { Text, View } from 'react-native'
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON><PERSON> } from 'react-native-chart-kit'
import type { FC } from 'react'

import classNames from '@/utils/classname'

export interface PieChartDataItem extends Record<string, unknown> {
  label: string
  value: number
  color: string
  [key: string]: unknown
}

interface PieChartProps {
  data: PieChartDataItem[]
  width?: number
  height?: number
  legendClassName?: string
  chartClassName?: string
}

export const PieChart: FC<PieChartProps> = ({
  data,
  width = 220,
  height = 220,
  legendClassName,
  chartClassName
}) => {
  const chartData = data.map(item => ({
    name: item.label,
    population: item.value,
    color: item.color,
    legendFontColor: '#888',
    legendFontSize: 12
  }))

  return (
    <View
      className={classNames('items-center justify-center', chartClassName)}
      style={{ width, height: height + 40 }}
    >
      <R<PERSON>hartKitPieChart
        data={chartData}
        width={width}
        height={height}
        chartConfig={{
          color: () => '#888',
          labelColor: () => '#888'
        }}
        accessor="population"
        backgroundColor="transparent"
        paddingLeft={(width / 4).toString()}
        hasLegend={false}
      />
      <View
        className={classNames(
          'mt-4 flex-row flex-wrap justify-center',
          legendClassName
        )}
      >
        {data.map(item => (
          <View key={item.label} className="mb-2 mr-4 flex-row items-center">
            <View
              style={{
                width: 24,
                height: 12,
                backgroundColor: item.color,
                borderRadius: 2
              }}
            />
            <Text className="ml-2 text-xs" style={{ color: '#888' }}>
              {item.label}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )
}
