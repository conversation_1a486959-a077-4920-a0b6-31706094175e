import React from 'react'
import { Text, View } from 'react-native'

import { crossPlatformShadow } from '@/theme/shadow'
interface BudgetProgressProps {
  title: string
  actual: number
  total: number
}

function getColor(progress: number) {
  if (progress >= 0.9) {
    return {
      bar: '#ef4444',
      labelBg: 'bg-red-100',
      labelText: 'text-red-500'
    }
  } else if (progress >= 0.7) {
    return {
      bar: '#f59e0b',
      labelBg: 'bg-yellow-100',
      labelText: 'text-yellow-700'
    }
  } else {
    return {
      bar: '#22c55e',
      labelBg: 'bg-green-100',
      labelText: 'text-green-600'
    }
  }
}

export function BudgetProgressCard({
  title,
  actual,
  total
}: BudgetProgressProps) {
  const percent = Math.min(actual / total, 1)
  const percentText = `${Math.round(percent * 100)}% Used`
  const { bar, labelBg, labelText } = getColor(percent)

  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      <View className="mb-2 flex-row items-center justify-between">
        <Text className="text-base font-semibold text-gray-900">{title}</Text>
        <View className={`rounded-xl px-3 py-1 ${labelBg}`}>
          <Text className={`text-xs font-semibold ${labelText}`}>
            {percentText}
          </Text>
        </View>
      </View>
      <View className="mb-3">
        <View className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
          <View
            className="h-full rounded-full"
            style={{ width: `${percent * 100}%`, backgroundColor: bar }}
          />
        </View>
      </View>
      <View className="flex-row items-baseline justify-between">
        <Text className="text-base font-bold text-gray-900">
          ${actual.toLocaleString()} <Text className="font-normal">spent</Text>
        </Text>
        <Text className="text-gray-500 text-xs">
          of ${total.toLocaleString()} allocated
        </Text>
      </View>
    </View>
  )
}
