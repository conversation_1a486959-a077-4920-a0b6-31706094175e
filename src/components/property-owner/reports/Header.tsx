import React from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { MagnifyingGlassIcon } from 'react-native-heroicons/outline'
import FontAwesome from '@expo/vector-icons/FontAwesome'

import type { IconTypes } from '@/components/Icon'
import { crossPlatformShadow } from '@/theme/shadow'
import classNames from '@/utils/classname'

export interface HeaderAction {
  icon: IconTypes
  onPress: () => void
  key?: string
}

export interface HeaderProps {
  title: string
  actions?: HeaderAction[]
  className?: string
}

// eslint-disable-next-line unused-imports/no-unused-vars
export const Header = ({ title, actions = [], className }: HeaderProps) => {
  return (
    <View
      className={classNames(
        'flex-row items-center justify-between bg-white px-4 py-3',
        className
      )}
      style={[crossPlatformShadow('sm'), { zIndex: 10 }]}
    >
      <Text className="text-lg font-bold text-black">{title}</Text>
      <View className="flex-row gap-3">
        <TouchableOpacity
          className="ml-2 h-9 w-9 items-center justify-center rounded-lg bg-gray-100 active:bg-gray-200"
          activeOpacity={0.7}
        >
          <MagnifyingGlassIcon size={20} color="#222" />
        </TouchableOpacity>
        <TouchableOpacity
          className="ml-2 h-9 w-9 items-center justify-center rounded-lg bg-gray-100 active:bg-gray-200"
          activeOpacity={0.7}
        >
          <FontAwesome name="sliders" size={24} color="#222" />
        </TouchableOpacity>
      </View>
    </View>
  )
}
