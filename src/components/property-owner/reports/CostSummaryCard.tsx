import React, { useState } from 'react'
import { Dimensions, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'

import { crossPlatformShadow } from '@/theme/shadow'

import { <PERSON><PERSON><PERSON> } from './PieChart'
// eslint-disable-next-line unused-imports/no-unused-vars
const chartColors = [
  '#6366f1', // Plumbing
  '#f472b6', // HVAC
  '#22c55e', // Electrical
  '#f59e0b', // Landscaping
  '#a78bfa', // Painting
  '#0ea5e9' // Carpentry
]

// const legendLabels = [
//   { label: 'Plumbing', color: '#6366f1' },
//   { label: 'HVAC', color: '#f472b6' },
//   { label: 'Electrical', color: '#22c55e' },
//   { label: 'Landscaping', color: '#f59e0b' },
//   { label: 'Painting', color: '#a78bfa' },
//   { label: 'Carpentry', color: '#0ea5e9' }
// ]

const pieData = [
  {
    label: 'Plumbing',
    value: 18450,
    color: '#6366f1'
  },
  {
    label: 'HVAC',
    value: 22680,
    color: '#f472b6'
  },
  {
    label: 'Electrical',
    value: 12350,
    color: '#22c55e'
  },
  {
    label: 'Landscaping',
    value: 9780,
    color: '#f59e0b'
  },
  {
    label: 'Painting',
    value: 8000,
    color: '#a78bfa'
  },
  {
    label: 'Carpentry',
    value: 6000,
    color: '#0ea5e9'
  }
]

const tableData = [
  { category: 'Plumbing', cost: '$18,450', change: -8.3 },
  { category: 'HVAC', cost: '$22,680', change: -12.5 },
  { category: 'Electrical', cost: '$12,350', change: 3.2 },
  { category: 'Landscaping', cost: '$9,780', change: -5.4 }
]

const tabs = [
  { key: 'category', label: 'By Category' },
  { key: 'month', label: 'By Month' }
]

export const CostSummaryCard = () => {
  const [activeTab, setActiveTab] = useState('category')
  const screenWidth = Dimensions.get('window').width - 48

  return (
    <View
      className="mb-4 rounded-md bg-white p-4"
      style={[crossPlatformShadow('default')]}
    >
      {/* Header */}
      <View className="mb-2 flex-row items-center justify-between">
        <Text className="text-base font-bold text-gray-900">Cost Summary</Text>
        <Text className="text-gray-500 text-xs font-medium">
          Last 12 months
        </Text>
      </View>
      {/* Tabs */}
      <View className="mb-2 flex-row rounded-xl bg-gray-100 p-1">
        {tabs.map(tab => {
          const isActive = activeTab === tab.key
          return (
            <TouchableOpacity
              key={tab.key}
              className={`flex-1 items-center justify-center rounded-lg py-2 ${isActive ? 'bg-indigo-100' : ''}`}
              onPress={() => setActiveTab(tab.key)}
              activeOpacity={0.8}
            >
              <Text
                className={`text-sm ${isActive ? 'font-bold text-indigo-600' : 'font-normal text-gray-900'}`}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          )
        })}
      </View>
      {/* Chart */}
      {activeTab === 'category' ? (
        <PieChart
          data={pieData}
          width={screenWidth}
          height={180}
          chartClassName=""
          legendClassName="mb-6 mt-2"
        />
      ) : // <BarChart width={screenWidth} height={180} />
      null}
      {/* Table */}
      <View className="mt-4 rounded-md border border-gray-200 bg-white">
        <View className="flex-row border-b border-gray-200 px-2 py-2">
          <Text className="text-gray-500 flex-1 text-xs font-bold">
            CATEGORY
          </Text>
          <Text className="text-gray-500 w-24 text-center text-xs font-bold">
            COST
          </Text>
          <Text className="text-gray-500 w-28 text-center text-xs font-bold">
            VS LAST YEAR
          </Text>
        </View>
        {tableData.map(row => (
          <View
            key={row.category}
            className="flex-row items-center border-b border-gray-100 px-2 py-3 last:border-b-0"
          >
            <Text className="flex-1 text-sm text-gray-900">{row.category}</Text>
            <Text className="w-24 text-center text-base font-extrabold text-gray-900">
              {row.cost}
            </Text>
            <View className="w-28 flex-row items-center justify-center">
              {row.change > 0 ? (
                <Ionicons
                  name="arrow-down"
                  size={16}
                  color="#22c55e"
                  style={{ marginRight: 2 }}
                />
              ) : (
                <Ionicons
                  name="arrow-up"
                  size={16}
                  color="#ef4444"
                  style={{ marginRight: 2 }}
                />
              )}
              <Text
                className={`text-sm font-semibold ${row.change > 0 ? 'text-green-600' : 'text-red-500'}`}
                style={{ marginLeft: 2 }}
              >
                {Math.abs(row.change).toFixed(1)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  )
}
