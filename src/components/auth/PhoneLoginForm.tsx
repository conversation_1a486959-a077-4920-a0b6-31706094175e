import { Fragment, useState } from 'react'
import type { FieldValues } from 'react-hook-form'
import { Pressable, Text, View } from 'react-native'
import { Toast } from 'toastify-react-native'

import { Button, Form, FormItem } from '@/components'
import { VerifyCodeInput } from '@/components/VerifyCodeInput'
import { usePhoneSend } from '@/hooks/usePhoneSend'
import { client } from '@/services/api'
import type { APIRole } from '@/store'
import { useAuth } from '@/store'
import { validateUSPhoneNumber } from '@/utils/validators'

import { PhoneNumberInput } from '../common/PhoneNumberInput'

const PhoneLoginForm = ({
  role,
  onLoggedIn
}: {
  role: APIRole
  onLoggedIn: VoidFunction
}) => {
  const form = Form.useForm()
  const [loading, setLoading] = useState(false)
  const loginByPhone = useAuth(state => state.loginByPhone)
  const {
    loading: sendLoading,
    send,
    sent,
    countdown
  } = usePhoneSend(async () => {
    try {
      const { phone } = form.getValues()
      await form.trigger('phone')
      const state = await form.getFieldState('phone')
      if (!state.invalid) {
        const { error } = await client.POST('/api/v1/sms/send-code', {
          body: { phoneNumber: phone, role }
        })
        return !error
      }
      return false
    } catch (error) {
      return false
    }
  })

  const handleLogin = async (values: FieldValues) => {
    const { phone, code } = values
    setLoading(true)
    const [error, user] = await loginByPhone(role, phone, code)
    if (!error && user) {
      onLoggedIn()
    } else {
      Toast.error(error!.message)
    }
    setLoading(false)
  }

  return (
    <Fragment>
      <Form form={form} onFinish={handleLogin}>
        <FormItem label="Phone Number" noStyle>
          <FormItem
            className="flex-1"
            name="phone"
            rules={{
              validate: validateUSPhoneNumber
            }}
          >
            <PhoneNumberInput autoFocus placeholder="Enter your phone number" />
          </FormItem>
        </FormItem>
        <FormItem
          noStyle
          label="We'll send you a verification code"
          extra={
            <Button
              variant="primary-light"
              size="xs"
              loading={sendLoading}
              onPress={send}
            >
              Send Code
            </Button>
          }
        />
        <FormItem
          label="Verification Code"
          name="code"
          rules={{
            required: {
              value: true,
              message: 'Verification Code is required'
            }
          }}
        >
          <VerifyCodeInput />
        </FormItem>
        {sent && (
          <View className="mt-2 flex flex-row items-center justify-center">
            <Text className="text-sm text-gray">Didn't receive code? </Text>
            <Pressable
              disabled={countdown > 0}
              onPress={send}
              className="border-0 bg-transparent"
            >
              <Text className="text-sm text-primary">
                Resend {countdown > 0 && <Text>({countdown}s)</Text>}
              </Text>
            </Pressable>
          </View>
        )}
      </Form>

      <Button
        variant="primary"
        onPress={form.submit}
        loading={loading}
        disabled={loading}
        className="mb-4 mt-5"
      >
        Verify & Log in
      </Button>
    </Fragment>
  )
}

export default PhoneLoginForm
