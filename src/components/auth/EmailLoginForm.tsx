import { Fragment, useState } from 'react'
import type { FieldValues } from 'react-hook-form'
import { Text } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { Link } from 'expo-router'
import { Toast } from 'toastify-react-native'

import {
  Button,
  Checkbox,
  Form,
  FormItem,
  Input,
  PasswordInput
} from '@/components'
import type { APIRole } from '@/store'
import { useAuth } from '@/store'
import { Colors } from '@/theme/colors'

const EmailLoginForm = ({
  role,
  onLoggedIn
}: {
  role: APIRole
  onLoggedIn: VoidFunction
}) => {
  const form = Form.useForm()
  const [loading, setLoading] = useState(false)
  const loginByPassword = useAuth(state => state.loginByPassword)

  const handleLogin = async (values: FieldValues) => {
    const { email, password } = values
    if (!email || !password) {
      Toast.error('Please enter your email and password')
      return
    }

    setLoading(true)
    const [error, user] = await loginByPassword(role, email, password)
    if (!error && user) {
      onLoggedIn()
    }
    setLoading(false)
  }
  return (
    <Fragment>
      <Form
        initialValues={{ remember: true }}
        form={form}
        onFinish={handleLogin}
      >
        <FormItem
          label="Email Address"
          name="email"
          rules={{
            required: {
              value: true,
              message: 'Email is required'
            },
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Invalid email address'
            }
          }}
        >
          <Input
            keyboardType="email-address"
            placeholder="Enter your email"
            LeftAccessory={() => (
              <FA name="envelope" size={16} color={Colors.gray} />
            )}
          />
        </FormItem>
        <FormItem
          label="Password"
          name="password"
          extra={
            <Link
              href="/auth/forgot-password"
              asChild
              accessible={false}
              importantForAccessibility="no"
              // @ts-ignore
              tabIndex={-1}
            >
              <Text className="text-sm text-primary">Forgot?</Text>
            </Link>
          }
          rules={{
            required: {
              value: true,
              message: 'Password is required'
            }
          }}
        >
          <PasswordInput
            placeholder="Enter your password"
            LeftAccessory={() => (
              <FA name="lock" size={16} color={Colors.gray} />
            )}
          />
        </FormItem>
        <FormItem name="remember">
          <Checkbox>Remember me</Checkbox>
        </FormItem>
      </Form>

      <Button
        variant="primary"
        onPress={form.submit}
        loading={loading}
        disabled={loading}
        className="mb-4"
      >
        Log in
      </Button>
    </Fragment>
  )
}

export default EmailLoginForm
