import React from 'react'
import { Pressable, Text, View } from 'react-native'
import clsx from 'clsx'
import type { FC, ReactNode } from 'react'

import classNames from '@/utils/classname'

export interface CheckboxProps {
  className?: string
  disabled?: boolean
  value?: boolean
  onChange?: (v: boolean) => void
  labelClassName?: string
  children?: ReactNode
  testID?: string
}

export const Checkbox: FC<CheckboxProps> = ({
  value,
  disabled,
  onChange,
  className,
  labelClassName,
  children,
  testID
}) => {
  return (
    <Pressable
      onPress={() => onChange?.(!value)}
      disabled={disabled}
      testID={testID}
      className={clsx(
        'flex flex-row items-center gap-2',
        disabled ? 'opacity-50' : '',
        className
      )}
    >
      <View
        className={classNames(
          'inline-flex h-[18px] w-[18px] items-center justify-center rounded-sm border border-gray bg-white',
          value ? 'border-primary bg-primary' : ''
        )}
      >
        {value && <Text className="text-base font-bold text-white">✓</Text>}
      </View>
      <Text className={classNames('text-sm text-gray', labelClassName)}>
        {children}
      </Text>
    </Pressable>
  )
}
