import React from 'react'
import { ScrollView, Text, TouchableOpacity, View } from 'react-native'

import classNames from '@/utils/classname'

export interface FilterTabOption {
  label: string
  labelCount?: number
  value: string
}

export interface FilterTabsProps {
  options: FilterTabOption[]
  value: string
  onChange: (value: string) => void
  className?: string
  tabClassName?: string
}

/**
 * FilterBar Component
 * Horizontally scrollable filter chips bar, with active highlight
 */
export const FilterTabs = ({
  options,
  value,
  onChange,
  className
}: FilterTabsProps) => {
  return (
    <View
      className={classNames(
        'mb-5 flex-row gap-2.5 rounded-lg bg-white p-2 shadow-sm',
        className
      )}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="flex-row"
      >
        {options.map((option, idx) => {
          const selected = value.includes(option.value)
          return (
            <TouchableOpacity
              key={option.value}
              className={classNames(
                'relative flex-row items-center justify-center gap-2 rounded-full px-4 py-2.5 transition-all duration-300',
                selected ? 'bg-primary' : 'bg-gray-100',
                idx === options.length - 1 ? '' : 'mr-2'
              )}
              activeOpacity={0.7}
              onPress={() => onChange(option.value)}
            >
              <Text
                className={classNames(
                  'text-center text-sm',
                  selected ? 'font-semibold text-white' : 'font-medium'
                )}
              >
                {option.label}
              </Text>
              {option.labelCount && (
                <View className="flex h-[22px] w-[22px] items-center justify-center rounded-full bg-gray-200">
                  <Text
                    className={classNames(
                      'text-xs',
                      selected ? 'text-primary' : 'text-gray-700'
                    )}
                  >
                    {option.labelCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}
