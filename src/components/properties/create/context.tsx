import { createContext, useContext, useState } from 'react'
import { useRout<PERSON> } from 'expo-router'
import type { ReactNode } from 'react'

import { useFormStorage } from '@/hooks/useFormStorage'
import type { components } from '@/services/api/schema'

export type PropertyFormData = components['schemas']['PMPropertySubmitDTO']

const initialFormData: PropertyFormData = {
  // Property basic info
  propertyName: '',
  propertyType: 'OTHER',
  bedroomCount: undefined,
  bathroomCount: undefined,
  sizeSqFt: undefined,
  yearBuilt: undefined,
  streetAddress: '',
  city: '',
  state: '',
  zipCode: '',
  description: undefined,
  currentCondition: undefined,
  propertyValue: undefined,
  additionalNotes: undefined,
  status: '',
  photos: undefined,
  monthlyRent: undefined,
  leaseBeginDate: undefined,
  leaseEndDate: undefined,

  // Owner ID (when selecting existing owner)
  ownerId: undefined,

  // Owner info
  owner: {
    firstName: undefined,
    lastName: undefined,
    email: undefined,
    phoneNumber: undefined,
    ownerType: undefined,
    mailingAddress: undefined,
    ownerNotes: undefined,
    grantPortalAccessFlag: undefined
  },

  // Tenant info (new structure with lease information and tenant list)
  tenant: undefined
}

interface PropertyContextType {
  formData: PropertyFormData
  updateFormData: (data: Partial<PropertyFormData>) => void
  resetFormData: () => void
  clearFormData: () => void
  isLoaded: boolean
  currentStep: number
  setCurrentStep: (step: number) => void
  handleBack: () => void
  handleContinue: () => void
}

const PropertyContext = createContext<PropertyContextType | undefined>(
  undefined
)

export function PropertyProvider({ children }: { children: ReactNode }) {
  const [currentStep, setCurrentStep] = useState(0)
  const router = useRouter()

  const {
    data: formData,
    updateData,
    resetData,
    clearData,
    isLoaded
  } = useFormStorage<PropertyFormData>({
    key: 'property-form',
    initialValue: initialFormData
  })

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    } else {
      router.back()
    }
  }

  const handleContinue = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const value = {
    formData,
    updateFormData: updateData,
    resetFormData: resetData,
    clearFormData: clearData,
    isLoaded,
    currentStep,
    setCurrentStep,
    handleBack,
    handleContinue
  }

  return (
    <PropertyContext.Provider value={value}>
      {children}
    </PropertyContext.Provider>
  )
}

export function useProperty() {
  const context = useContext(PropertyContext)
  if (context === undefined) {
    throw new Error('useProperty must be used within a PropertyProvider')
  }
  return context
}
