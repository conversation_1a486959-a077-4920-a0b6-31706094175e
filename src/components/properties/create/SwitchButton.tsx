import { Text, View } from 'react-native'
import type { ReactNode } from 'react'

import { Button } from '@/components'
import classNames from '@/utils/classname'

interface SwitchButtonOption {
  label: string
  icon: ReactNode
  value: string
  onPress?: () => void
}

interface SwitchButtonProps {
  value: string
  onChange: (val: string) => void
  options: SwitchButtonOption[]
  height?: number
  className?: string
}

const wrapperClass = 'flex flex-row items-center justify-between gap-2'
const baseButtonClass =
  'flex-1 flex-row items-center justify-center rounded-lg border-2 px-4 py-3'
const activeButtonClass = 'bg-[#eff6ff] border-[#3b82f6] text-[#3b82f6]'
const inactiveButtonClass = 'bg-white border-[#e5e7eb] text-[#6b7280]'
const leftButtonGap = 'gap-2'
const rightButtonGap = 'gap-4'
const buttonTextStyle = {
  fontSize: 14,
  fontWeight: '500' as const
}

const SwitchButton = ({
  value,
  onChange,
  options,
  height = 61,
  className
}: SwitchButtonProps) => {
  return (
    <View className={classNames(wrapperClass, className)} style={{ height }}>
      {options.map((option, idx) => (
        <Button
          key={option.value}
          leftIcon={option.icon}
          onPress={() => {
            onChange(option.value)
            option.onPress?.()
          }}
          className={classNames(
            baseButtonClass,
            idx === 0 ? leftButtonGap : rightButtonGap,
            value === option.value ? activeButtonClass : inactiveButtonClass
          )}
          style={{ height }}
        >
          <Text
            style={{
              ...buttonTextStyle,
              color: value === option.value ? '#3b82f6' : '#6b7280'
            }}
          >
            {option.label}
          </Text>
        </Button>
      ))}
    </View>
  )
}

export default SwitchButton
