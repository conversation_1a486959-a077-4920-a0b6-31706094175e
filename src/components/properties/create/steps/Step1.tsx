import React, { Fragment } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { AddPhotos, Form, FormItem, Input, Select } from '@/components'
import { NumberInput } from '@/components/NumberInput'
import SectionTitle from '@/components/SectionTitle'
import { Star } from '@/components/Star'
import { useStateCityZip } from '@/hooks/useStateCityZip'
import { Colors, ShadowStyles } from '@/theme/colors'

import type { PropertyFormData } from '../context'
import { useProperty } from '../context'
import NextButtonGroup from '../NextButtonGroup'

export function Step1() {
  const form = Form.useForm<Partial<PropertyFormData>>()
  const {
    stateOptions,
    cityOptions,
    zipOptions,
    loading,
    setSelectedState,
    setSelectedCity
  } = useStateCityZip({
    onZipChange(zip) {
      form.setValue('zipCode', zip)
    }
  })
  const { formData, updateFormData, handleBack, handleContinue } = useProperty()

  const handleSubmit = (values: Partial<PropertyFormData>) => {
    console.log(values)
    const newFormData = { ...formData, ...values }
    updateFormData(newFormData)
    handleContinue()
  }

  return (
    <Fragment>
      <Form
        form={form}
        style={ShadowStyles.sm}
        onFinish={handleSubmit}
        initialValues={formData || {}}
      >
        <View className="mb-4 rounded-default border border-border bg-white p-5">
          <SectionTitle
            title="Property Details"
            leftIcon={
              <FontAwesome6
                name="building"
                solid
                size={18}
                color={Colors.primary}
              />
            }
          />
          <FormItem
            name="propertyName"
            label="Property Name"
            rules={{
              required: {
                value: true,
                message: 'Please enter Property name'
              }
            }}
          >
            <Input placeholder="Enter Property name" />
          </FormItem>
          <FormItem
            name="propertyType"
            label="Property Type"
            rules={{
              required: { value: true, message: 'Please select property type' }
            }}
          >
            <Select
              placeholder="Select property type"
              dictType="PROPERTY_TYPE"
            />
          </FormItem>
          <View className="flex flex-row gap-3">
            <FormItem className="flex-1" name="bedroomCount" label="Bedrooms">
              <NumberInput min={0} placeholder="Input" />
            </FormItem>
            <FormItem className="flex-1" name="bathroomCount" label="Bathrooms">
              <NumberInput min={0} placeholder="Input" />
            </FormItem>
            <FormItem className="flex-1" name="sizeSqFt" label="Sq. Ft.">
              <NumberInput min={0} placeholder="Input" />
            </FormItem>
          </View>
          <FormItem name="yearBuilt" label="Year Built">
            <NumberInput placeholder="Enter year built" />
          </FormItem>
        </View>
        <View className="mb-4 rounded-default border border-border bg-white p-5">
          <SectionTitle
            title="Location"
            leftIcon={
              <FontAwesome6
                name="location-dot"
                size={18}
                color={Colors.primary}
              />
            }
          />
          <FormItem
            name="streetAddress"
            label="Street Address"
            // rules={[{ required: true, message: 'Please enter Property name' }]}
          >
            <Input placeholder="Enter Street Address" />
          </FormItem>
          <View className="flex flex-row gap-3">
            <FormItem
              className="flex-1"
              name="state"
              label="State"
              rules={{
                required: {
                  value: true,
                  message: 'Please select state'
                }
              }}
              onValuesChange={(name, v) => setSelectedState(v as string)}
            >
              <Select<string>
                loading={loading}
                options={stateOptions}
                placeholder="Select state"
              />
            </FormItem>
            <FormItem
              className="flex-1"
              name="city"
              label="City"
              rules={{
                required: { value: true, message: 'Please select city' }
              }}
              onValuesChange={(name, v) => setSelectedCity(v as string)}
            >
              <Select<string>
                loading={loading}
                options={cityOptions}
                placeholder="Select city"
              />
            </FormItem>
          </View>
          <FormItem
            name="zipCode"
            label="ZIP Code"
            rules={{
              required: { value: true, message: 'Please select Zip code' }
            }}
          >
            <Select<string>
              loading={loading}
              options={zipOptions}
              placeholder="Select ZIP code"
            />
          </FormItem>
        </View>
        <View className="rounded-default border border-border bg-white p-5">
          <SectionTitle
            title="Property Features"
            leftIcon={
              <FontAwesome6 name="list" size={18} color={Colors.primary} />
            }
          />
          <FormItem
            name="description"
            label="Description"
            // rules={[
            //   { required: true, message: 'Please enter Property description' }
            // ]}
          >
            <Input
              placeholder="Enter property description..."
              multiline
              numberOfLines={4}
            />
          </FormItem>
          <FormItem
            name="currentCondition"
            label="Current Condition"
            // rules={[{ required: true, message: 'Please select current condition' }]}
          >
            <Star />
          </FormItem>
          <View className="flex flex-row gap-3">
            {/* <FormItem
              className="flex-1"
              name="monthlyRent"
              label="Monthly Rent"
              // rules={[{ required: true, message: 'Please select start date' }]}
            >
              <NumberInput
                LeftAccessory={() => <Text className="text-gray">$</Text>}
              />
            </FormItem> */}
            <FormItem
              className="flex-1"
              name="propertyValue"
              label="Property Value"
              // rules={[{ required: true, message: 'Please select completion date' }]}
            >
              <NumberInput
                LeftAccessory={() => <Text className="text-gray">$</Text>}
              />
            </FormItem>
          </View>
          <View>
            <SectionTitle
              title="Property Images"
              leftIcon={
                <FontAwesome6 name="camera" size={18} color={Colors.primary} />
              }
            />
            <FormItem
              className="flex-1"
              name="photos"
              label=""
              // rules={[{ required: true, message: 'Please select completion date' }]}
            >
              <AddPhotos title="" />
            </FormItem>
          </View>
        </View>
      </Form>
      <NextButtonGroup
        handleBack={handleBack}
        handleNext={form.handleSubmit(handleSubmit)}
      />
    </Fragment>
  )
}
