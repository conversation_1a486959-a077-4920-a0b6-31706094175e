import React, { useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { useRequest } from 'ahooks'

import { Checkbox, Form, FormItem, Input, Select } from '@/components'
import { PhoneNumberInput } from '@/components/common/PhoneNumberInput'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'
import { Colors, ShadowStyles } from '@/theme/colors'
import type { ID } from '@/types'
import { getFullName } from '@/utils/user'
import { validateUSPhoneNumber } from '@/utils/validators'

import type { PropertyFormData } from '../context'
import { useProperty } from '../context'
import NextButtonGroup from '../NextButtonGroup'
import SearchModal from '../SearchModal'
import SwitchButton from '../SwitchButton'

// Owner search result data type (matching API response structure)
interface OwnerSearchResult {
  ownerId?: number
  firstName?: string
  lastName?: string
  email?: string
  phoneNumber?: string
  ownerType?: string
  mailingAddress?: string
  ownerNotes?: string
  grantPortalAccessFlag?: string
}

export function Step2() {
  const { formData, updateFormData, handleBack, handleContinue } = useProperty()
  const form = Form.useForm<NonNullable<PropertyFormData['owner']>>()
  const [ownerInputType, setOwnerInputType] = useState<'new' | 'existing'>(
    formData.ownerId ? 'existing' : 'new'
  )
  const [showSearchModal, setShowSearchModal] = useState(false)
  const [ownerId, setOwnerId] = useState<null | ID>(formData.ownerId || null)

  // Effect to handle owner input type changes
  useEffect(() => {
    if (ownerInputType === 'new') {
      // Clear ownerId when switching to new owner
      setOwnerId(null)
    }
  }, [ownerInputType])

  // Search request for owners with debouncing
  const searchRequest = useRequest(
    async (searchText: string) => {
      const res = await client.GET('/api/v1/pm/user/property-owner', {
        params: {
          query: {
            searchText
          }
        }
      })
      console.log('Owner search results:', res)
      // Return the search results data
      return res.data?.data || []
    },
    {
      manual: true
    }
  )

  // Handle owner selection from search results
  const handleOwnerSelect = (owner: OwnerSearchResult) => {
    // Auto-fill form fields with selected owner data
    if (owner.firstName) form.setValue('firstName', owner.firstName)
    if (owner.lastName) form.setValue('lastName', owner.lastName)
    if (owner.email) form.setValue('email', owner.email)
    if (owner.phoneNumber) form.setValue('phoneNumber', owner.phoneNumber)
    if (owner.ownerType) form.setValue('ownerType', owner.ownerType)
    if (owner.mailingAddress)
      form.setValue('mailingAddress', owner.mailingAddress)
    if (owner.ownerNotes) form.setValue('ownerNotes', owner.ownerNotes)
    setOwnerId(owner.ownerId!)
  }

  // Default render function for owner items
  const defaultRenderOwnerItem = (owner: OwnerSearchResult) => (
    <View className="flex-col gap-1">
      <View className="flex-row items-center">
        <Text className="text-dark">Name:</Text>
        <Text className="ml-auto font-medium text-dark/90">
          {getFullName(owner)}
        </Text>
      </View>
      <View className="flex-row items-center">
        <Text className="text-dark">Email:</Text>
        <Text className="ml-auto font-medium text-dark/90">
          {owner.email || '-'}
        </Text>
      </View>
      <View className="flex-row items-center">
        <Text className="text-dark">Phone:</Text>
        <Text className="ml-auto font-medium text-dark/90">
          {owner.phoneNumber || '-'}
        </Text>
      </View>
      <View className="flex-row items-center">
        <Text className="text-dark">Type:</Text>
        <Text className="ml-auto font-medium text-dark/90">
          {owner.ownerType || '-'}
        </Text>
      </View>
    </View>
  )

  // Handle form submission
  const handleSubmit = (values: PropertyFormData['owner']) => {
    console.log('Owner form values:', values)
    const newFormData = { ...formData }

    // If an existing owner is selected, include the ownerId
    if (ownerInputType === 'existing' && ownerId) {
      newFormData.ownerId = ownerId
      newFormData.owner = { ...formData.owner, ...values }
    } else {
      // For new owner, don't include ownerId
      newFormData.ownerId = undefined
      newFormData.owner = { ...formData.owner, ...values }
    }

    updateFormData(newFormData)
    handleContinue()
  }

  return (
    <View className="flex flex-col gap-y-4">
      {/* Owner Information Form */}
      <Form
        className="flex-1 rounded-default border border-border bg-white p-5"
        form={form}
        style={ShadowStyles.sm}
        onFinish={console.log}
        initialValues={formData.owner}
      >
        {/* Section Header */}
        <SectionTitle
          title="Property Owner Information"
          leftIcon={<FA name="user-tie" size={18} color={Colors.primary} />}
        />
        <Text className="mb-4 text-sm text-gray">
          Add owner information for this property.
        </Text>

        {/* Owner Input Type Toggle */}
        <SwitchButton
          value={ownerInputType}
          onChange={val => setOwnerInputType(val as 'new' | 'existing')}
          options={[
            {
              label: 'New Owner',
              icon: (
                <FA
                  name="user-plus"
                  size={18}
                  color={ownerInputType === 'new' ? '#3b82f6' : '#6b7280'}
                />
              ),
              value: 'new',
              onPress() {
                setOwnerId(null)
              }
            },
            {
              label: 'Select Existing',
              icon: (
                <FA
                  name="magnifying-glass"
                  size={18}
                  color={ownerInputType === 'existing' ? '#3b82f6' : '#6b7280'}
                />
              ),
              value: 'existing',
              onPress() {
                setShowSearchModal(true)
              }
            }
          ]}
        />

        {/* Owner Details Form Fields */}
        <View className="mt-4 flex flex-row gap-4">
          <FormItem
            name="firstName"
            label="First Name"
            rules={{ required: { value: true, message: 'Required' } }}
            className="flex-1"
          >
            <Input placeholder="Enter first name" disabled={!!ownerId} />
          </FormItem>
          <FormItem
            name="lastName"
            label="Last Name"
            rules={{ required: { value: true, message: 'Required' } }}
            className="flex-1"
          >
            <Input placeholder="Enter last name" disabled={!!ownerId} />
          </FormItem>
        </View>

        {/* Contact Information */}
        <FormItem
          name="email"
          label="Email Address"
          rules={{
            required: { value: true, message: 'Required' },
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Invalid email address'
            }
          }}
        >
          <Input
            placeholder="<EMAIL>"
            keyboardType="email-address"
            disabled={!!ownerId}
          />
        </FormItem>
        <FormItem
          name="phoneNumber"
          label="Phone Number"
          rules={{
            validate: validateUSPhoneNumber
          }}
        >
          <PhoneNumberInput disabled={!!ownerId} />
        </FormItem>

        {/* Owner Type Selection */}
        <FormItem name="ownerType" label="Owner Type">
          <Select
            placeholder="Select owner type"
            dictType="PROPERTY_OWNER_TYPE"
            disabled={!!ownerId}
          />
        </FormItem>

        {/* Additional Information */}
        <FormItem name="mailingAddress" label="Mailing Address">
          <Input
            placeholder="Enter mailing address..."
            multiline
            numberOfLines={3}
            disabled={!!ownerId}
          />
        </FormItem>
        <FormItem name="ownerNotes" label="Owner Notes">
          <Input
            placeholder="Any special notes about the owner..."
            multiline
            numberOfLines={3}
            disabled={!!ownerId}
          />
        </FormItem>

        {/* Portal Access Permission */}
        <FormItem name="grantPortalAccessFlag" label="">
          <Checkbox
          // value={form.watch('grantPortalAccessFlag') === 'true'}
          // onChange={val =>
          //   form.setValue('grantPortalAccessFlag', String(val))
          // }
          >
            Grant owner portal access
          </Checkbox>
        </FormItem>
      </Form>
      <NextButtonGroup
        handleBack={handleBack}
        handleNext={form.handleSubmit(handleSubmit)}
      />

      {/* Owner Search Modal */}
      <SearchModal<OwnerSearchResult>
        visible={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        searchRequest={searchRequest}
        onSelect={handleOwnerSelect}
        renderItem={defaultRenderOwnerItem}
        placeholder="Search by name or email..."
        title="Search Owner"
      />
    </View>
  )
}
