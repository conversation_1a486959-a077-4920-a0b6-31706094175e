import React, { useRef, useState } from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { useRequest } from 'ahooks'

import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  FormItem,
  Input
} from '@/components'
import { PhoneNumberInput } from '@/components/common/PhoneNumberInput'
import { NumberInput } from '@/components/NumberInput'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors, ShadowStyles } from '@/theme/colors'
import { getFullName } from '@/utils/user'
import {
  validateUSPhoneNumber,
  validateUSPhoneNumberWithoutRequired
} from '@/utils/validators'

import { useProperty } from '../context'
import NextButtonGroup from '../NextButtonGroup'
import SearchModal from '../SearchModal'
import StatusToggle from '../StatusToggle'
import SwitchButton from '../SwitchButton'

type TenantSearchResult = components['schemas']['PMAddTenantDTO']

// Extended tenant type for local state management
interface TenantFormData
  extends Omit<components['schemas']['PMAddTenantDTO'], 'tenantId'> {
  tempId: string // Temporary ID for local management
  tenantId?: number // Optional for existing tenants
}

// Lease information based on PropertyLease schema
interface LeaseFormData {
  beginDate?: string
  endDate?: string
  monthlyRent?: number
  securityDeposit?: number
  leaseNotes?: string
}

export function Step3() {
  const { formData, updateFormData, handleContinue, handleBack, isLoaded } =
    useProperty()
  const selectedTenantId = useRef<number | null>(null)
  const [propertyStatus, setPropertyStatus] = useState<string>(
    formData.status || 'VACANT'
  )
  const [tenantInputType, setTenantInputType] = useState<'new' | 'existing'>(
    'new'
  )
  const [showSearchModal, setShowSearchModal] = useState(false)
  const [showTenantModal, setShowTenantModal] = useState(false)

  // Multi-tenant state management
  const [tenants, setTenants] = useState<TenantFormData[]>([])
  const [editingTenant, setEditingTenant] = useState<TenantFormData | null>(
    null
  )
  const [leaseData, setLeaseData] = useState<LeaseFormData>({
    beginDate: formData.leaseBeginDate,
    endDate: formData.leaseEndDate,
    monthlyRent: formData.monthlyRent,
    securityDeposit: undefined,
    leaseNotes: undefined
  })

  // Load existing tenant data from form storage when component mounts
  React.useEffect(() => {
    if (
      isLoaded &&
      formData.tenant?.tenantList &&
      Array.isArray(formData.tenant.tenantList)
    ) {
      console.log(
        'Loading existing tenant data from storage:',
        formData.tenant.tenantList
      )
      // Convert API tenant format to local tenant format
      const existingTenants: TenantFormData[] = formData.tenant.tenantList.map(
        (tenant, index) => ({
          tempId: `existing-${index}-${Date.now()}`,
          tenantId: tenant.tenantId,
          leaseId: tenant.leaseId,
          firstName: tenant.firstName,
          lastName: tenant.lastName,
          email: tenant.email,
          phoneNumber: tenant.phoneNumber,
          emergencyContact: tenant.emergencyContact,
          tenantNotes: tenant.tenantNotes,
          grantPortalAccessFlag: tenant.grantPortalAccessFlag
        })
      )
      setTenants(existingTenants)
    }

    // Load existing lease data
    if (isLoaded && formData.tenant?.leaseInformation) {
      const lease = formData.tenant.leaseInformation
      setLeaseData({
        beginDate: lease.beginDate,
        endDate: lease.endDate,
        monthlyRent: lease.monthlyRent,
        securityDeposit: lease.securityDeposit,
        leaseNotes: lease.leaseNotes
      })
    }
  }, [isLoaded, formData.tenant])

  // Forms for individual tenant and lease data
  const tenantForm = Form.useForm<TenantFormData>()
  const leaseForm = Form.useForm<LeaseFormData>()

  // Render tenant item in search results
  const renderTenantItem = (tenant: unknown) => {
    const typedTenant = tenant as TenantSearchResult
    return (
      <View className="flex-col gap-1">
        <View className="flex-row items-center">
          <Text className="text-dark">Name:</Text>
          <Text className="ml-auto font-medium text-dark/90">
            {typedTenant.firstName || ''} {typedTenant.lastName || ''}
          </Text>
        </View>
        <View className="flex-row items-center">
          <Text className="text-dark">Email:</Text>
          <Text className="ml-auto font-medium text-dark/90">
            {typedTenant.email || '-'}
          </Text>
        </View>
      </View>
    )
  }

  // Handle tenant selection from search
  const handleTenantSelect = (tenant: unknown) => {
    const typedTenant = tenant as TenantSearchResult
    tenantForm.setValue('firstName', typedTenant.firstName || '')
    tenantForm.setValue('lastName', typedTenant.lastName || '')
    tenantForm.setValue('email', typedTenant.email || '')
    tenantForm.setValue('phoneNumber', typedTenant.phoneNumber || '')
    tenantForm.setValue('emergencyContact', typedTenant.emergencyContact || '')
    tenantForm.setValue('tenantNotes', typedTenant.tenantNotes || '')
    tenantForm.setValue('tenantId', typedTenant.tenantId)
    selectedTenantId.current = typedTenant.tenantId || null
    setShowSearchModal(false)
  }

  // Add tenant to the list
  const handleAddTenant = (values: TenantFormData) => {
    if (editingTenant) {
      // Update existing tenant
      setTenants(prev =>
        prev.map(t =>
          t.tempId === editingTenant.tempId
            ? {
                ...values,
                tempId: editingTenant.tempId,
                tenantId: selectedTenantId.current || editingTenant.tenantId
              }
            : t
        )
      )
      setEditingTenant(null)
    } else {
      // Add new tenant
      const newTenant: TenantFormData = {
        ...values,
        tempId: Date.now().toString(),
        tenantId: selectedTenantId.current || undefined
      }
      setTenants(prev => [...prev, newTenant])
    }
    // Reset the tenant form after adding/updating
    tenantForm.reset()
    selectedTenantId.current = null
    // Reset tenant input type to 'new' for next tenant
    setTenantInputType('new')
    // Close the modal
    setShowTenantModal(false)
  }

  // Edit tenant from the list
  const handleEditTenant = (tenant: TenantFormData) => {
    setEditingTenant(tenant)
    // Populate form with tenant data
    tenantForm.setValue('firstName', tenant.firstName || '')
    tenantForm.setValue('lastName', tenant.lastName || '')
    tenantForm.setValue('email', tenant.email || '')
    tenantForm.setValue('phoneNumber', tenant.phoneNumber || '')
    tenantForm.setValue('emergencyContact', tenant.emergencyContact || '')
    tenantForm.setValue('tenantNotes', tenant.tenantNotes || '')
    tenantForm.setValue('grantPortalAccessFlag', tenant.grantPortalAccessFlag)
    selectedTenantId.current = tenant.tenantId || null
    // Set input type based on whether tenant has tenantId (existing vs new)
    setTenantInputType(tenant.tenantId ? 'existing' : 'new')
    // Open the modal
    setShowTenantModal(true)
  }

  // Remove tenant from the list
  const handleRemoveTenant = (tempId: string) => {
    setTenants(prev => prev.filter(t => t.tempId !== tempId))
    // If we're editing this tenant, clear the editing state
    if (editingTenant?.tempId === tempId) {
      setEditingTenant(null)
      tenantForm.reset()
      selectedTenantId.current = null
      setTenantInputType('new')
    }
  }

  // Clear tenant form
  const handleClearTenantForm = () => {
    tenantForm.reset()
    selectedTenantId.current = null
    setEditingTenant(null)
    setTenantInputType('new')
  }

  // Open modal for adding new tenant
  const handleOpenAddTenantModal = () => {
    setEditingTenant(null)
    tenantForm.reset()
    selectedTenantId.current = null
    setTenantInputType('new')
    setShowTenantModal(true)
  }

  // Close tenant modal
  const handleCloseTenantModal = () => {
    setShowTenantModal(false)
    setEditingTenant(null)
    tenantForm.reset()
    selectedTenantId.current = null
    setTenantInputType('new')
  }

  // Search request for tenants - only search if tenantId is needed
  const searchRequest = useRequest(
    async (searchText: string) => {
      const { data } = await client.GET('/api/v1/pm/user/tenant', {
        params: {
          query: {
            searchText: searchText
          }
        }
      })
      return data?.data || []
    },
    { manual: true }
  )

  const handleSubmit = () => {
    // Get lease form values
    const leaseValues = leaseForm.getValues()

    // Convert tenants to the API format
    const tenantList = tenants.map(tenant => ({
      tenantId: tenant.tenantId,
      leaseId: tenant.leaseId,
      firstName: tenant.firstName,
      lastName: tenant.lastName,
      email: tenant.email,
      phoneNumber: tenant.phoneNumber,
      emergencyContact: tenant.emergencyContact,
      tenantNotes: tenant.tenantNotes,
      grantPortalAccessFlag: tenant.grantPortalAccessFlag
    }))

    // Create lease information object
    const leaseInformation =
      leaseValues.beginDate ||
      leaseValues.endDate ||
      leaseValues.monthlyRent ||
      leaseValues.securityDeposit ||
      leaseValues.leaseNotes
        ? {
            beginDate: leaseValues.beginDate,
            endDate: leaseValues.endDate,
            monthlyRent: leaseValues.monthlyRent,
            securityDeposit: leaseValues.securityDeposit,
            leaseNotes: leaseValues.leaseNotes
          }
        : undefined

    // Create tenant object according to new API structure
    const tenantData =
      tenantList.length > 0 || leaseInformation
        ? {
            leaseInformation,
            tenantList
          }
        : undefined

    const newFormData = {
      ...formData,
      status: propertyStatus,
      leaseBeginDate: leaseValues.beginDate,
      leaseEndDate: leaseValues.endDate,
      monthlyRent: leaseValues.monthlyRent,
      tenant: tenantData
    }

    console.log('Step3 - Saving form data:', newFormData)
    console.log('Step3 - Tenant data:', tenantData)
    // @ts-ignore
    updateFormData(newFormData)
    handleContinue()
  }

  return (
    <ScrollView
      className="flex-1"
      contentContainerStyle={{ paddingBottom: 20 }}
    >
      <View className="flex flex-col gap-y-4">
        {/* Property Status Section */}
        <View
          className="rounded-default border border-border bg-white p-5"
          style={ShadowStyles.sm}
        >
          <SectionTitle title="Tenant Information" className="mb-2" />
          <Text className="mb-4 text-sm text-gray">
            Set property occupancy status and tenant details.
          </Text>

          {/* Property Status Toggle */}
          <View className="mb-4">
            <Text className="mb-2 text-base font-medium text-dark">
              Property Status
            </Text>
            <StatusToggle value={propertyStatus} onChange={setPropertyStatus} />
          </View>
        </View>

        {/* Tenant Section - Only show if occupied */}
        {propertyStatus === 'OCCUPIED' && (
          <>
            {/* Lease Information Form */}
            <View
              className="rounded-default border border-border bg-white p-5"
              style={ShadowStyles.sm}
            >
              <Text className="mb-3 text-base font-medium text-dark">
                <FA
                  name="file-contract"
                  size={16}
                  color={Colors.primary}
                  className="mr-2"
                />
                Lease Information (Shared by all tenants)
              </Text>

              <Form
                form={leaseForm}
                initialValues={leaseData}
                onValuesChange={(_, values) => setLeaseData(values)}
              >
                <View className="flex-row gap-3">
                  <FormItem
                    className="flex-1"
                    name="beginDate"
                    label="Lease Start Date"
                  >
                    <DatePicker placeholder="Select start date" />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="endDate"
                    label="Lease End Date"
                  >
                    <DatePicker placeholder="Select end date" />
                  </FormItem>
                </View>

                <View className="flex-row gap-3">
                  <FormItem
                    className="flex-1"
                    name="monthlyRent"
                    label="Monthly Rent (Total)"
                  >
                    <NumberInput placeholder="$0" min={0} />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="securityDeposit"
                    label="Security Deposit (Total)"
                  >
                    <NumberInput placeholder="$0" min={0} />
                  </FormItem>
                </View>

                <FormItem name="leaseNotes" label="Lease Notes">
                  <Input
                    multiline
                    numberOfLines={2}
                    placeholder="Any special terms or conditions..."
                  />
                </FormItem>
              </Form>
            </View>

            {/* Tenant Management */}
            <View
              className="rounded-default border border-border bg-white p-5"
              style={ShadowStyles.sm}
            >
              <Text className="mb-3 text-base font-medium text-dark">
                Tenant Management
              </Text>
              <Text className="mb-4 text-sm text-gray">
                Add tenants to this property. You can add multiple tenants who
                will share the lease terms.
              </Text>

              {/* Added Tenants List */}
              {tenants.length > 0 && (
                <View className="mb-4">
                  <Text className="mb-3 text-sm font-medium text-dark">
                    Added Tenants ({tenants.length})
                  </Text>
                  {tenants.map(tenant => (
                    <View
                      key={tenant.tempId}
                      className="bg-gray-50 mb-3 rounded-lg border border-gray-200 p-3"
                    >
                      <View className="flex-row items-center justify-between">
                        <View className="flex-1">
                          <View className="flex-row items-center">
                            <Text className="text-base font-medium text-dark">
                              {getFullName(tenant)}
                            </Text>
                            {tenant.tenantId && (
                              <View className="ml-2 rounded-full bg-primary-light px-2 py-1">
                                <Text className="text-xs text-primary">
                                  Existing
                                </Text>
                              </View>
                            )}
                          </View>
                          <Text className="text-sm text-gray">
                            {tenant.email}
                          </Text>
                          {tenant.phoneNumber && (
                            <Text className="text-sm text-gray">
                              {tenant.phoneNumber}
                            </Text>
                          )}
                        </View>
                        <View className="flex-row gap-2">
                          <Pressable
                            onPress={() => handleEditTenant(tenant)}
                            className="rounded-full bg-primary-light p-2"
                          >
                            <FA name="pen" size={14} color={Colors.primary} />
                          </Pressable>
                          <Pressable
                            onPress={() => handleRemoveTenant(tenant.tempId)}
                            className="rounded-full bg-danger-light p-2"
                          >
                            <FA name="trash" size={14} color={Colors.danger} />
                          </Pressable>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              )}

              <Button
                variant="primary"
                onPress={handleOpenAddTenantModal}
                className="w-full"
              >
                <FA name="user-plus" size={16} color={Colors.white} />
                <Text className="ml-2 text-white">Add New Tenant</Text>
              </Button>
            </View>
          </>
        )}

        {/* Navigation Buttons */}
        <NextButtonGroup handleBack={handleBack} handleNext={handleSubmit} />

        {/* Tenant Form Modal */}
        <Modal
          visible={showTenantModal}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={handleCloseTenantModal}
        >
          <View className="bg-gray-50 flex-1">
            {/* Modal Header */}
            <View className="border-b border-gray-200 bg-white px-4 py-3">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-lg font-semibold text-dark">
                    {editingTenant ? 'Edit Tenant' : 'Add New Tenant'}
                  </Text>
                  {editingTenant && (
                    <View className="ml-2 rounded-full bg-orange-100 px-2 py-1">
                      <Text className="text-xs text-orange-600">Editing</Text>
                    </View>
                  )}
                </View>
                <Pressable
                  onPress={handleCloseTenantModal}
                  className="rounded-full bg-gray-100 p-2"
                >
                  <FA name="xmark" size={16} color={Colors.gray} />
                </Pressable>
              </View>
            </View>

            {/* Modal Content */}
            <ScrollView
              className="flex-1"
              contentContainerStyle={{ padding: 16 }}
            >
              {/* Tenant Input Type Toggle */}
              <View
                className="mb-4 rounded-default border border-border bg-white p-4"
                style={ShadowStyles.sm}
              >
                <Text className="mb-3 text-base font-medium text-dark">
                  Tenant Source
                </Text>
                <SwitchButton
                  options={[
                    {
                      label: 'New Tenant',
                      value: 'new',
                      icon: (
                        <FA
                          name="user-plus"
                          size={18}
                          color={
                            tenantInputType === 'new' ? '#3b82f6' : Colors.gray
                          }
                        />
                      )
                    },
                    {
                      label: 'Select Existing',
                      value: 'existing',
                      icon: (
                        <FA
                          name="magnifying-glass"
                          size={18}
                          color={
                            tenantInputType === 'existing'
                              ? '#3b82f6'
                              : Colors.gray
                          }
                        />
                      ),
                      onPress() {
                        setShowSearchModal(true)
                      }
                    }
                  ]}
                  value={tenantInputType}
                  onChange={val =>
                    setTenantInputType(val as 'new' | 'existing')
                  }
                />
              </View>

              {/* Individual Tenant Form */}
              <View
                className="rounded-default border border-border bg-white p-4"
                style={ShadowStyles.sm}
              >
                <Text className="mb-3 text-base font-medium text-dark">
                  <FA
                    name="user"
                    solid
                    size={16}
                    color={Colors.primary}
                    className="mr-2"
                  />
                  Tenant Information
                </Text>

                <Form form={tenantForm} onFinish={handleAddTenant}>
                  <View className="flex-row gap-3">
                    <FormItem
                      className="flex-1"
                      name="firstName"
                      label="First Name"
                      rules={{
                        required: {
                          value: true,
                          message: 'First name is required'
                        }
                      }}
                    >
                      <Input placeholder="Enter first name" />
                    </FormItem>
                    <FormItem
                      className="flex-1"
                      name="lastName"
                      label="Last Name"
                      rules={{
                        required: {
                          value: true,
                          message: 'Last name is required'
                        }
                      }}
                    >
                      <Input placeholder="Enter last name" />
                    </FormItem>
                  </View>

                  <FormItem
                    name="email"
                    label="Email Address"
                    rules={{
                      required: { value: true, message: 'Email is required' }
                    }}
                  >
                    <Input placeholder="<EMAIL>" />
                  </FormItem>

                  <FormItem
                    name="phoneNumber"
                    label="Phone Number"
                    rules={{
                      validate: validateUSPhoneNumber
                    }}
                  >
                    <PhoneNumberInput />
                  </FormItem>

                  <FormItem
                    name="emergencyContact"
                    label="Emergency Contact"
                    rules={{
                      validate: validateUSPhoneNumberWithoutRequired
                    }}
                  >
                    <PhoneNumberInput />
                  </FormItem>

                  <FormItem name="tenantNotes" label="Notes">
                    <Input
                      multiline
                      numberOfLines={3}
                      placeholder="Any special notes about this tenant..."
                    />
                  </FormItem>

                  <FormItem name="grantPortalAccessFlag">
                    <Checkbox disabled={!!selectedTenantId.current}>
                      Grant tenant portal access
                    </Checkbox>
                  </FormItem>
                </Form>
              </View>
            </ScrollView>

            {/* Modal Footer */}
            <View className="border-t border-gray-200 bg-white p-4">
              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  onPress={handleClearTenantForm}
                  className="flex-1"
                >
                  <FA name="rotate-left" size={16} color={Colors.gray} />
                  <Text className="ml-2">Clear</Text>
                </Button>
                <Button
                  variant="primary"
                  onPress={() => tenantForm.handleSubmit(handleAddTenant)()}
                  className="flex-1"
                >
                  <FA
                    name={editingTenant ? 'check' : 'plus'}
                    size={16}
                    color={Colors.white}
                  />
                  <Text className="ml-2 text-white">
                    {editingTenant ? 'Update' : 'Add Tenant'}
                  </Text>
                </Button>
              </View>
            </View>

            {/* Search Modal inside Tenant Modal */}
            <SearchModal
              visible={showSearchModal}
              onClose={() => setShowSearchModal(false)}
              searchRequest={searchRequest}
              onSelect={handleTenantSelect}
              renderItem={renderTenantItem}
              placeholder="Search tenants by name or email..."
              title="Select Tenant"
            />
          </View>
        </Modal>
      </View>
    </ScrollView>
  )
}
