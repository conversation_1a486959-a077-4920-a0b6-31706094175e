import React from 'react'
import { ScrollView, Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { useRequest } from 'ahooks'
import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { client } from '@/services/api'
import { usePMStat } from '@/store/pmStat'
import { Colors } from '@/theme/colors'
import { formatDate } from '@/utils/formatDate'
import { getFullName } from '@/utils/user'

import { useProperty } from '../context'
import NextButtonGroup from '../NextButtonGroup'

const sectionClass = 'bg-white rounded-default p-5 mb-4 shadow-sm'
const reviewSectionClass = 'bg-light rounded-default p-4 mb-4'
const reviewTitleClass = 'text-base font-semibold mb-3 text-dark'
const reviewItemClass = 'flex flex-row items-center mb-2'
const reviewLabelClass = 'w-32 text-gray-500 text-sm font-medium'
const reviewValueClass = 'flex-1 text-dark text-sm'

export function Step4() {
  const { formData, handleBack, clearFormData } = useProperty()
  const { refresh } = usePMStat()

  const request = useRequest(
    async data => {
      const { error } = await client.POST('/api/v1/pm/property/add', {
        body: data
      })
      return !error
    },
    {
      manual: true
    }
  )

  const handleCreate = async () => {
    console.log('Form data before submission:', formData)

    // Prepare the data for submission
    const submitData = {
      ...formData,
      // Handle tenant data - if vacant, no tenant; if occupied, use tenant structure
      tenant: formData.status === 'VACANT' ? undefined : formData.tenant
    }

    // If ownerId exists, remove the owner object (use existing owner)
    // If no ownerId, keep the owner object (create new owner)
    if (formData.ownerId) {
      delete submitData.owner
    }

    console.log('Submit data:', submitData)

    const res = await request.runAsync(submitData)
    if (res) {
      Toast.success('Property created successfully!')
      refresh()
      clearFormData()
      router.back()
    }
  }

  return (
    <ScrollView className="space-y-5">
      <View className={sectionClass}>
        <View className="mb-2 flex-row items-center">
          <FA
            name="circle-check"
            solid
            size={20}
            color="#3b82f6"
            style={{ marginRight: 8 }}
          />
          <Text className="text-lg font-semibold">Review & Confirm</Text>
        </View>
        <Text className="mb-4 text-sm text-gray">
          Please review all information before adding the property.
        </Text>
        {/* Property Summary */}
        <View className={reviewSectionClass}>
          <Text className={reviewTitleClass}>Property Details</Text>
          {[
            { label: 'Property Name:', value: formData.propertyName || '-' },
            { label: 'Type:', value: formData.propertyType || '-' },
            { label: 'Address:', value: formData.streetAddress || '-' },
            {
              label: 'Size:',
              value: `${formData.bedroomCount || 'N/A'} bed, ${formData.bathroomCount || 'N/A'} bath, ${formData.sizeSqFt || 'N/A'} sq ft`
            }
          ].map((item, idx) => (
            <View className={reviewItemClass} key={idx}>
              <Text className={reviewLabelClass}>{item.label}</Text>
              <Text className={reviewValueClass}>{item.value}</Text>
            </View>
          ))}
        </View>
        {/* Owner Summary */}
        <View className={reviewSectionClass}>
          <Text className={reviewTitleClass}>Owner Information</Text>
          {[
            {
              label: 'Name:',
              value: getFullName(formData.owner)
            },
            { label: 'Email:', value: formData.owner?.email || '-' },
            { label: 'Phone:', value: formData.owner?.phoneNumber || '-' }
          ].map((item, idx) => (
            <View className={reviewItemClass} key={idx}>
              <Text className={reviewLabelClass}>{item.label}</Text>
              <Text className={reviewValueClass}>{item.value}</Text>
            </View>
          ))}
        </View>
        {/* Tenant Summary */}
        {formData.status === 'OCCUPIED' && (
          <View className={reviewSectionClass}>
            <Text className={reviewTitleClass}>Tenant Information</Text>
            {/* Lease Information */}
            {formData.tenant?.leaseInformation && (
              <View className="bg-gray-50 mb-4 rounded-lg p-3">
                <Text className="mb-2 text-sm font-medium text-dark">
                  Lease Details
                </Text>
                {[
                  {
                    label: 'Monthly Rent:',
                    value: formData.tenant.leaseInformation.monthlyRent
                      ? `$${formData.tenant.leaseInformation.monthlyRent}`
                      : '-'
                  },
                  {
                    label: 'Lease Period:',
                    value:
                      formData.tenant.leaseInformation.beginDate &&
                      formData.tenant.leaseInformation.endDate
                        ? `${formatDate(formData.tenant.leaseInformation.beginDate)} to ${formatDate(formData.tenant.leaseInformation.endDate)}`
                        : '-'
                  },
                  {
                    label: 'Security Deposit:',
                    value: formData.tenant.leaseInformation.securityDeposit
                      ? `$${formData.tenant.leaseInformation.securityDeposit}`
                      : '-'
                  }
                ].map((item, idx) => (
                  <View className={reviewItemClass} key={idx}>
                    <Text className={reviewLabelClass}>{item.label}</Text>
                    <Text className={reviewValueClass}>{item.value}</Text>
                  </View>
                ))}
                {formData.tenant.leaseInformation.leaseNotes && (
                  <View className="mt-2">
                    <Text className="text-gray-500 text-xs font-medium">
                      Notes:
                    </Text>
                    <Text className="text-xs italic text-gray">
                      {formData.tenant.leaseInformation.leaseNotes}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Tenant List */}
            {formData.tenant?.tenantList &&
            formData.tenant.tenantList.length > 0 ? (
              <View>
                <Text className="mb-3 text-sm font-medium text-dark">
                  Tenants ({formData.tenant.tenantList.length})
                </Text>
                {formData.tenant.tenantList.map((tenant, index) => (
                  <View
                    key={index}
                    className="bg-gray-50 mb-3 rounded-lg border border-gray-200 p-3"
                  >
                    <View className="flex-row items-center justify-between">
                      <View className="flex-1">
                        <View className="flex-row items-center">
                          <Text className="text-sm font-medium text-dark">
                            {getFullName(tenant)}
                          </Text>
                          {tenant.tenantId && (
                            <View className="ml-2 rounded-full bg-blue-100 px-2 py-1">
                              <Text className="text-xs text-primary">
                                Existing
                              </Text>
                            </View>
                          )}
                        </View>
                        <Text className="text-xs text-gray">
                          {tenant.email || '-'}
                        </Text>
                        {tenant.phoneNumber && (
                          <Text className="text-xs text-gray">
                            {tenant.phoneNumber}
                          </Text>
                        )}
                        {tenant.emergencyContact && (
                          <Text className="text-xs text-gray">
                            Emergency: {tenant.emergencyContact}
                          </Text>
                        )}
                        {tenant.tenantNotes && (
                          <Text className="text-xs italic text-gray">
                            Note: {tenant.tenantNotes}
                          </Text>
                        )}
                      </View>
                      <View className="ml-2">
                        <FA
                          name="user"
                          size={16}
                          solid
                          color={Colors.primary}
                        />
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : formData.tenant ? (
              <View className="rounded-lg bg-yellow-50 p-3">
                <Text className="text-sm text-yellow-700">
                  Lease information provided but no tenants added yet.
                </Text>
              </View>
            ) : (
              <View className="rounded-lg bg-yellow-50 p-3">
                <Text className="text-sm text-yellow-700">
                  No tenant information provided. Property is marked as occupied
                  but no tenant or lease information added.
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
      <NextButtonGroup
        handleBack={handleBack}
        handleNext={handleCreate}
        loading={request.loading}
      />
    </ScrollView>
  )
}
