import React from 'react'
import { Pressable, Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'

import classNames from '@/utils/classname'

const statusToggleBg =
  'bg-[#f8fafc] border border-[#e2e8f0] rounded-lg overflow-hidden flex flex-row'
const statusOptionBase =
  'flex-1 flex flex-col items-center py-4 px-3 transition-all'
const statusOptionActive = 'bg-[#3b82f6] text-white'
const statusOptionInactive = 'bg-white text-[#0f172a]'
const statusOptionFirst = 'border-r border-[#e2e8f0]'
const statusIconStyle = { fontSize: 20, marginBottom: 4 }
const statusLabelStyle = {
  fontSize: 12,
  lineHeight: 16,
  fontWeight: '500' as const
}

interface StatusToggleProps {
  value: string
  onChange: (v: 'OCCUPIED' | 'VACANT') => void
}

const StatusToggle = ({ value, onChange }: StatusToggleProps) => {
  return (
    <View className={statusToggleBg}>
      <Pressable
        className={classNames(
          statusOptionBase,
          statusOptionFirst,
          value === 'OCCUPIED' ? statusOptionActive : statusOptionInactive
        )}
        onPress={() => onChange('OCCUPIED')}
        style={{ borderTopLeftRadius: 8, borderBottomLeftRadius: 8 }}
      >
        <FA
          name="house"
          size={20}
          color={value === 'OCCUPIED' ? 'white' : '#0f172a'}
          style={statusIconStyle}
        />
        <Text
          style={{
            ...statusLabelStyle,
            color: value === 'OCCUPIED' ? 'white' : '#0f172a'
          }}
        >
          Occupied
        </Text>
      </Pressable>
      <Pressable
        className={classNames(
          statusOptionBase,
          value === 'VACANT' ? statusOptionActive : statusOptionInactive
        )}
        onPress={() => onChange('VACANT')}
        style={{ borderTopRightRadius: 8, borderBottomRightRadius: 8 }}
      >
        <FA
          name="door-open"
          size={20}
          color={value === 'VACANT' ? 'white' : '#0f172a'}
          style={statusIconStyle}
        />
        <Text
          style={{
            ...statusLabelStyle,
            color: value === 'VACANT' ? 'white' : '#0f172a'
          }}
        >
          Vacant
        </Text>
      </Pressable>
    </View>
  )
}

export default StatusToggle
