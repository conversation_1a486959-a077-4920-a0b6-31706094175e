import React from 'react'
import { renderHook } from '@testing-library/react-native'

import { PropertyProvider, useProperty } from '../context'

// Mock the router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    back: jest.fn()
  })
}))

// Mock the storage utilities
jest.mock('@/utils/storage', () => ({
  load: jest.fn(() => null),
  save: jest.fn(),
  remove: jest.fn()
}))

describe('PropertyContext', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <PropertyProvider>{children}</PropertyProvider>
  )

  it('initializes with undefined values for numeric fields instead of 0', () => {
    const { result } = renderHook(() => useProperty(), { wrapper })

    const { formData } = result.current

    // Check that numeric fields are undefined instead of 0
    expect(formData.bedroomCount).toBeUndefined()
    expect(formData.bathroomCount).toBeUndefined()
    expect(formData.sizeSqFt).toBeUndefined()
    expect(formData.propertyValue).toBeUndefined()
    expect(formData.monthlyRent).toBeUndefined()
    expect(formData.tenant?.leaseInformation?.monthlyRent).toBeUndefined()
    expect(formData.tenant?.leaseInformation?.securityDeposit).toBeUndefined()
    expect(formData.ownerId).toBeUndefined()
  })

  it('initializes with correct string values', () => {
    const { result } = renderHook(() => useProperty(), { wrapper })

    const { formData } = result.current

    // Check that string fields are initialized correctly
    expect(formData.propertyName).toBe('')
    expect(formData.propertyType).toBe('OTHER')
    expect(formData.streetAddress).toBe('')
    expect(formData.city).toBe('')
    expect(formData.state).toBe('')
    expect(formData.status).toBe('')
  })

  it('provides all required context methods', () => {
    const { result } = renderHook(() => useProperty(), { wrapper })

    const context = result.current

    // Check that all required methods are available
    expect(typeof context.updateFormData).toBe('function')
    expect(typeof context.resetFormData).toBe('function')
    expect(typeof context.clearFormData).toBe('function')
    expect(typeof context.handleBack).toBe('function')
    expect(typeof context.handleContinue).toBe('function')
  })

  it('initializes with correct default step', () => {
    const { result } = renderHook(() => useProperty(), { wrapper })

    expect(result.current.currentStep).toBe(0)
  })

  it('initializes with isLoaded as true', () => {
    const { result } = renderHook(() => useProperty(), { wrapper })

    expect(result.current.isLoaded).toBe(true)
  })
})
