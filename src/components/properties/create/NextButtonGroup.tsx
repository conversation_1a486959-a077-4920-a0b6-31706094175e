import { View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Button } from '@/components'
import { Colors } from '@/theme/colors'

import { useProperty } from './context'

const NextButtonGroup = ({
  handleBack,
  handleNext,
  loading
}: {
  handleBack: () => void
  handleNext: () => void
  loading?: boolean
}) => {
  const { currentStep } = useProperty()

  const renderNextBtnText = () => {
    switch (currentStep) {
      case 0:
        return 'Next: Owner Information'
      case 1:
        return 'Next: Tenant Information'
      case 2:
        return 'Next: Review & Confirm'
      case 3:
        return 'Add Property'
      default:
        return null
    }
  }
  return (
    <>
      {currentStep < 4 && (
        <View className="mt-6 flex flex-row gap-x-4 bg-white">
          {currentStep > 0 && (
            <Button
              onPress={handleBack}
              className="flex-1"
              variant="outline"
              leftIcon={
                <FontAwesome6 name="arrow-left" size={14} color={Colors.dark} />
              }
            >
              Previous
            </Button>
          )}
          <Button
            onPress={handleNext}
            className="flex-1"
            variant="primary"
            loading={loading}
            style={
              currentStep === 3
                ? { backgroundColor: Colors.success }
                : undefined
            }
            leftIcon={currentStep === 3 ? 'plus' : undefined}
            rightIcon={
              currentStep === 3 ? undefined : (
                <FontAwesome6
                  name="arrow-right"
                  size={14}
                  color={Colors.white}
                />
              )
            }
          >
            {renderNextBtnText()}
          </Button>
        </View>
      )}
    </>
  )
}

export default NextButtonGroup
