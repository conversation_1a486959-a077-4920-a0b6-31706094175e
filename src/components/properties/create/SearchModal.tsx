import React, { useState } from 'react'
import {
  FlatList,
  Modal,
  Pressable,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useDebounceFn } from 'ahooks'
import type { Result } from 'ahooks/lib/useRequest/src/types'

import { Input } from '@/components/Input'
import { Colors, ShadowStyles } from '@/theme/colors'

export interface SearchModalProps<T> {
  visible: boolean
  onClose: () => void
  searchRequest: Result<T[] | undefined, [searchText: string]>
  onSelect: (item: T) => void
  renderItem: (item: T, index: number) => React.ReactNode
  placeholder?: string
  title?: string
}

export default function SearchModal<T>({
  visible,
  onClose,
  searchRequest,
  onSelect,
  renderItem,
  placeholder = 'Search...',
  title = 'Search'
}: SearchModalProps<T>) {
  const [searchInput, setSearchInput] = useState('')

  // 防抖搜索函数
  const { run: debouncedSearch } = useDebounceFn(
    (searchText: string) => {
      if (searchText.trim()) {
        searchRequest.runAsync(searchText.trim())
      }
    },
    {
      wait: 500 // 500ms 防抖延迟
    }
  )

  // 处理输入变化
  const handleInputChange = (text: string) => {
    setSearchInput(text)
    if (text.trim()) {
      debouncedSearch(text)
    }
  }

  const handleSelect = (item: T) => {
    onSelect(item)
    setSearchInput('')
    onClose()
  }

  const handleClose = () => {
    setSearchInput('')
    onClose()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View className="flex-1 items-center justify-center bg-black/30 px-4">
        <View
          className="min-h-[60vh] w-full max-w-md rounded-2xl bg-white"
          style={ShadowStyles.default}
        >
          {/* Header */}
          <View className="flex-row items-center justify-between border-b border-border px-6 py-4">
            <Text className="text-lg font-bold text-dark">{title}</Text>
            <Pressable onPress={handleClose} className="p-1">
              <FontAwesome6 name="xmark" size={22} color={Colors.primary} />
            </Pressable>
          </View>

          {/* Search Input */}
          <View className="px-6 pt-4">
            <Input
              placeholder={placeholder}
              value={searchInput}
              onChange={handleInputChange}
              LeftAccessory={() => (
                <FontAwesome6
                  name="magnifying-glass"
                  size={16}
                  color="#6b7280"
                />
              )}
              onClear={() => {
                setSearchInput('')
              }}
            />
          </View>

          {/* Search Results */}
          <View className="max-h-80 px-6 pb-4 pt-2">
            {searchRequest.loading ? (
              <View className="py-8">
                <Text className="text-center text-gray">Searching...</Text>
              </View>
            ) : searchRequest.data && searchRequest.data.length > 0 ? (
              <FlatList
                data={searchRequest.data || []}
                keyExtractor={(_, index) => index.toString()}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    onPress={() => handleSelect(item)}
                    className="border-b border-gray-100 py-3"
                  >
                    {renderItem(item, index)}
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
              />
            ) : searchInput.trim() ? (
              <View className="py-8">
                <Text className="text-center text-gray">No results found</Text>
              </View>
            ) : (
              <View className="py-4">
                <Text className="text-center text-gray">
                  Enter search terms to find results
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  )
}
