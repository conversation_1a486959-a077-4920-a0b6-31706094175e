import React, {
  Children,
  cloneElement,
  createContext,
  isValidElement,
  useContext,
  useEffect,
  useRef
} from 'react'
import type {
  DefaultValues,
  FieldValues,
  Path,
  PathValue,
  UseFormReturn
} from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import type { StyleProp, ViewStyle } from 'react-native'
import { findNodeHandle, Platform, ScrollView, View } from 'react-native'
import type { ReactNode, RefObject } from 'react'

import classNames from '@/utils/classname'

export type FormLayout = 'vertical' | 'horizontal'

export interface FormProps<T extends FieldValues = FieldValues> {
  form?: UseFormReturn<T>
  initialValues?: DefaultValues<T>
  layout?: FormLayout
  labelWidth?: number
  scrollToFirstError?: boolean
  onFinish?: (values: T) => void
  onValuesChange?: (changedValues: Partial<T>, allValues: T) => void
  children?: ReactNode
  className?: string
  style?: StyleProp<ViewStyle>
}

// extend UseFormReturn
export type FormInstance<T extends FieldValues = FieldValues> =
  UseFormReturn<T> & {
    submit: () => void
  }

const FormContext = createContext<{
  layout: 'vertical' | 'horizontal'
  labelWidth?: number
  registerFieldRef: (name: string, ref: RefObject<View | null>) => void
}>({
  layout: 'vertical',
  registerFieldRef: () => {}
})

export const useFormContext = () => useContext(FormContext)

// Add deep comparison function
const deepCompare = <T extends Record<string, unknown>>(
  obj1: T,
  obj2: T
): Record<string, unknown> => {
  const differences: Record<string, unknown> = {}

  const compare = (current1: unknown, current2: unknown, path: string = '') => {
    if (
      typeof current1 !== 'object' ||
      current1 === null ||
      typeof current2 !== 'object' ||
      current2 === null
    ) {
      if (current1 !== current2) {
        differences[path] = current1
      }
      return
    }

    const keys = new Set([
      ...Object.keys(current1 as object),
      ...Object.keys(current2 as object)
    ])

    for (const key of keys) {
      const newPath = path ? `${path}.${key}` : key
      if (!(key in (current1 as object))) {
        differences[newPath] = undefined
      } else if (!(key in (current2 as object))) {
        differences[newPath] = (current1 as Record<string, unknown>)[key]
      } else {
        compare(
          (current1 as Record<string, unknown>)[key],
          (current2 as Record<string, unknown>)[key],
          newPath
        )
      }
    }
  }

  compare(obj1, obj2)
  return differences
}

export const Form = <T extends FieldValues>({
  form,
  initialValues,
  layout = 'vertical',
  labelWidth,
  scrollToFirstError = true,
  onFinish,
  onValuesChange,
  children,
  className,
  style
}: FormProps<T>) => {
  const methods = useForm<T>({
    defaultValues: initialValues
  })
  const hasSetInitialValues = useRef<DefaultValues<T> | undefined>(undefined)
  const formInstance = (form || methods) as FormInstance<T>
  const scrollViewRef = useRef<ScrollView>(null)
  const fieldRefs = useRef<Map<string, RefObject<View | null>>>(new Map())

  const registerFieldRef = (name: string, ref: RefObject<View | null>) => {
    fieldRefs.current.set(name, ref)
  }

  const scrollToField = (name: Path<T>) => {
    const fieldRef = fieldRefs.current.get(name as string)
    if (!fieldRef?.current || !scrollViewRef.current) return

    if (Platform.OS === 'web') {
      // use scrollIntoView on web
      const element = fieldRef.current as unknown as HTMLElement
      element?.scrollIntoView?.({ behavior: 'smooth', block: 'center' })
    } else {
      // use measureLayout on native
      const nodeHandle = findNodeHandle(fieldRef.current)
      if (nodeHandle) {
        scrollViewRef.current.scrollTo({ y: 0, animated: true })
        // setTimeout(() => {
        //   fieldRef.current?.measureLayout(
        //     findNodeHandle(scrollViewRef.current)!,
        //     (x, y) => {
        //       scrollViewRef.current?.scrollTo({ y, animated: true })
        //     },
        //     () => {}
        //   )
        // }, 100)
      }
    }
  }

  const handleValuesChange = (name: Path<T>, value: T[Path<T>]) => {
    const allValues = formInstance.getValues()
    const changedValues = { [name]: value } as unknown as Partial<T>
    onValuesChange?.(changedValues, allValues)
  }

  // add submit method to form instance
  Object.defineProperty(formInstance, 'submit', {
    value: () => {
      formInstance.handleSubmit(
        data => {
          onFinish?.(data)
        },
        errors => {
          if (scrollToFirstError) {
            const firstErrorField = Object.keys(errors)[0] as Path<T>
            if (firstErrorField) {
              scrollToField(firstErrorField)
            }
          }
        }
      )()
    },
    writable: true,
    configurable: true
  })

  useEffect(() => {
    if (!hasSetInitialValues.current && initialValues) {
      // Initial setup
      Object.entries(initialValues).forEach(([key, value]) => {
        formInstance.setValue(key as Path<T>, value)
      })
      hasSetInitialValues.current = initialValues
    } else if (hasSetInitialValues.current && initialValues) {
      // Update on changes, compare differences
      const differences = deepCompare(
        initialValues,
        hasSetInitialValues.current
      )
      Object.entries(differences).forEach(([key, value]) => {
        if (value !== undefined) {
          formInstance.setValue(key as Path<T>, value as PathValue<T, Path<T>>)
        }
      })
      hasSetInitialValues.current = initialValues
    }
  }, [initialValues, formInstance])

  return (
    <FormContext.Provider value={{ layout, labelWidth, registerFieldRef }}>
      <FormProvider {...formInstance}>
        <ScrollView ref={scrollViewRef}>
          <View className={classNames('flex-1', className)} style={style}>
            {Children.map(children, child => {
              if (isValidElement(child)) {
                return cloneElement(child, {
                  onValuesChange: handleValuesChange
                } as Partial<typeof child.props>)
              }
              return child
            })}
          </View>
        </ScrollView>
      </FormProvider>
    </FormContext.Provider>
  )
}

// modify useForm type
Form.useForm = useForm as <T extends FieldValues>() => FormInstance<T>
