import type { StyleProp, ViewStyle } from 'react-native'
import { Platform } from 'react-native'

import { Shadows } from './colors'

/**
 * cross platform shadow, web: boxShadow，iOS: shadow*，android: elevation
 * @param level 'sm' | 'default' | 'md' | 'lg', default: 'default'
 * @returns style object
 */
export function crossPlatformShadow(level: keyof typeof Shadows = 'default') {
  const boxShadow = Shadows[level]
  switch (Platform.OS) {
    case 'web':
      return { boxShadow }
    case 'ios':
      if (level === 'sm') {
        return {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 2
        }
      }
      if (level === 'md') {
        return {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 6
        }
      }
      if (level === 'lg') {
        return {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 15
        }
      }
      // default
      return {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8
      }
    case 'android':
      if (level === 'sm') return { elevation: 1 }
      if (level === 'md') return { elevation: 4 }
      if (level === 'lg') return { elevation: 8 }
      return { elevation: 2 }
    default:
      return {}
  }
}

export const ShadowStyles: Record<'default', StyleProp<ViewStyle>> = {
  default: crossPlatformShadow('default')
}
