import type { components } from '@/services/api/schema'

export type DictItemType = components['schemas']['DictionaryItem']

export type DictType =
  | 'USER_STATUS'
  | 'GENDER'
  | 'CONFIG_STATUS'
  | 'FILE_TYPE'
  | 'ORDER_STATUS'
  | 'PROPERTY_TYPE'
  | 'PROPERTY_STATUS'
  | 'PROPERTY_STATE'
  | 'PROJECT_TYPE'
  | 'PROJECT_STATUS'
  | 'ROLE'
  | 'PROPERTY_OWNER_TYPE'
  | 'ITEM_STATUS'
  | 'ITEM_ALLOCATIONS_STATUS'
  | 'ITEM_QUOTES_STATUS'
  | 'PRIORITY'
  | 'CONTACT_METHOD'
  | 'PROPERTY_AREA'
  | 'PROJECT_ITEM_CONDITION'
  | 'PREFERRED_CONTACT_METHOD'
  | 'LANGUAGE'
  | 'ITEM_AREA_EXTERIOR'
  | 'ITEM_AREA_LIVING_ROOM'
  | 'ITEM_AREA_KITCHEN'
  | 'ITEM_AREA_BEDROOMS'
  | 'ITEM_AREA_BATHROOMS'
  | 'ITEM_AREA_BASEMENT'
  | 'ITEM_AREA_MECHANICAL'
  | 'ITEM_AREA_GARAGE'
  | 'ITEM_AREA_OTHER'

// Dictionary item union types by type
export type DICT_ITEM_USER_STATUS = 'ACTIVE' | 'BLOCKED' | 'INACTIVE'

export type DICT_ITEM_GENDER = '1' | '2' | '9'

export type DICT_ITEM_CONFIG_STATUS = '0' | '1'

export type DICT_ITEM_FILE_TYPE = 'AUDIO' | 'DOCUMENT' | 'IMAGE' | 'VIDEO'

export type DICT_ITEM_ORDER_STATUS =
  | 'CANCELLED'
  | 'COMPLETED'
  | 'PENDING'
  | 'PROCESSING'

export type DICT_ITEM_PROPERTY_TYPE = 'MULTI_FAMILY' | 'SINGLE_FAMILY'

export type DICT_ITEM_PROPERTY_STATUS = 'DRAFT' | 'OCCUPIED' | 'VACANT'

export type DICT_ITEM_PROPERTY_STATE =
  | 'ALABAMA'
  | 'CALIFORNIA'
  | 'FLORIDA'
  | 'NEW_YORK'

export type DICT_ITEM_PROJECT_TYPE =
  | 'MAINTENANCE_REQUEST'
  | 'REHAB'
  | 'VENDOR_PRIVATE'
  | 'WORK_ORDER'

export type DICT_ITEM_PROJECT_STATUS =
  | 'CANCELLED'
  | 'COMPLETED'
  | 'DRAFT'
  | 'IN_PROGRESS'
  | 'PENDING_QUOTES'
  | 'SUBMITTED'

export type DICT_ITEM_ROLE = 'pm' | 'property_owner' | 'tenant' | 'vendor'

export type DICT_ITEM_PROPERTY_OWNER_TYPE =
  | 'COMPANY'
  | 'INDIVIDUAL'
  | 'LLC'
  | 'TRUST'

export type DICT_ITEM_ITEM_STATUS =
  | 'ALLOCATING'
  | 'MANAGER_COMPLETED'
  | 'PENDING_ALLOCATING'
  | 'PENDING_QUOTE'
  | 'STARTED'
  | 'TERMINATE'
  | 'UNASSIGNED'
  | 'VENDOR_COMPLETED'

export type DICT_ITEM_ITEM_ALLOCATIONS_STATUS =
  | 'ACCEPTED'
  | 'COMPLETED'
  | 'PENDING'
  | 'REJECTED'
  | 'STARTED'
  | 'TERMINATE'
  | 'VENDOR_COMPLETED'

export type DICT_ITEM_ITEM_QUOTES_STATUS =
  | 'APPROVED'
  | 'PENDING_MANAGER'
  | 'PENDING_OWNER'
  | 'PENDING_VENDOR'
  | 'REJECTED'
  | 'TERMINATE'

export type DICT_ITEM_PRIORITY = 'CRITICAL' | 'HIGH' | 'LOW' | 'MEDIUM'

export type DICT_ITEM_CONTACT_METHOD =
  | 'APP_NOTIFICATION'
  | 'CHAT_MESSAGE'
  | 'EMAIL'
  | 'PHONE_CALL'
  | 'SMS'

export type DICT_ITEM_PROPERTY_AREA =
  | 'BASEMENT'
  | 'BATHROOMS'
  | 'BEDROOMS'
  | 'EXTERIOR'
  | 'GARAGE'
  | 'KITCHEN'
  | 'LIVING_ROOM'
  | 'MECHANICAL'
  | 'OTHER'

export type DICT_ITEM_PROJECT_ITEM_CONDITION =
  | 'EXCELLENT'
  | 'FAIR'
  | 'GOOD'
  | 'POOR'

export type DICT_ITEM_PREFERRED_CONTACT_METHOD =
  | 'APP'
  | 'CHAT_MESSAGE'
  | 'EMAIL'
  | 'PHONE_CALL'
  | 'SMS'

export type DICT_ITEM_LANGUAGE =
  | 'de'
  | 'en'
  | 'es'
  | 'fr'
  | 'ja'
  | 'ko'
  | 'zh-CN'
  | 'zh-TW'

export type DICT_ITEM_ITEM_AREA_EXTERIOR =
  | 'Exterior Lighting'
  | 'Landscaping'
  | 'Mailbox'
  | 'Mouldings and Trim'
  | 'Painting'
  | 'Pool and Equipment'
  | 'Pressure Wash'
  | 'Windows Repair'

export type DICT_ITEM_ITEM_AREA_LIVING_ROOM =
  | 'Ceiling Fan'
  | 'Electrical Outlets'
  | 'Flooring'
  | 'Light Fixtures'
  | 'Painting Windows'

export type DICT_ITEM_ITEM_AREA_KITCHEN =
  | 'Appliances'
  | 'Backsplash'
  | 'Cabinets'
  | 'Countertops'
  | 'Flooring'
  | 'Lighting'
  | 'Sink and Faucet'

export type DICT_ITEM_ITEM_AREA_BEDROOMS =
  | 'Ceiling Fan'
  | 'Closet'
  | 'Flooring'
  | 'Lighting'
  | 'Painting'
  | 'Windows'

export type DICT_ITEM_ITEM_AREA_BATHROOMS =
  | 'Exhaust Fan'
  | 'Flooring'
  | 'Lighting'
  | 'Mirror/Medicine Cabinet'
  | 'Shower/Tub'
  | 'Toilet'
  | 'Vanity'

export type DICT_ITEM_ITEM_AREA_BASEMENT =
  | 'Ceiling'
  | 'Dehumidifier'
  | 'Flooring'
  | 'Lighting'
  | 'Smoke and CO'
  | 'Stairs'
  | 'Sump Pump'
  | 'Switches and Outlets'
  | 'Walls'
  | 'Waterproofing'
  | 'Windows'

export type DICT_ITEM_ITEM_AREA_MECHANICAL =
  | 'Air Conditioning'
  | 'Electrical Box'
  | 'Gas Lines'
  | 'Heating System'
  | 'Laundry Machines'
  | 'Major Plumbing'
  | 'Ventilation System'
  | 'Water Filtration'
  | 'Water Heater'

export type DICT_ITEM_ITEM_AREA_GARAGE =
  | 'Door Knobs and Locks'
  | 'Garage Door'
  | 'Garage Floor'
  | 'Garage Lifts and Accessories'
  | 'Garage Storage'
  | 'Lighting'
  | 'Service Door'
  | 'Thresholds and Door Sweep'
  | 'Weatherstripping'

export type DICT_ITEM_ITEM_AREA_OTHER =
  | 'Cleaning'
  | 'Energy Efficiency'
  | 'Minor Repairs'
  | 'Painting'
  | 'Pest Control'
  | 'Safety Features'
  | 'Smart Home'
  | 'Trash Removal'
