import createClient from 'openapi-fetch'
import { Toast } from 'toastify-react-native'

import Config from '@/config'
import { useAuth } from '@/store'

import type { paths } from './schema' // generated by openapi-typescript

export const client = createClient<paths>({
  baseUrl: Config.API_URL
})

client.use({
  onRequest({ request }) {
    const token = useAuth.getState().accessToken
    if (token) {
      request.headers.set('Authorization', `Bearer ${token}`)
    }
    return request
  },

  async onResponse({ request, response }) {
    const contentType = response.headers.get('content-type')
    if (contentType === 'text/html') {
      const message = 'Error content type.'
      Toast.error(message)
      return new Response(JSON.stringify({ code: 500, message }), {
        status: 500
      })
    } else if (contentType?.includes('application/json')) {
      const clone = response.clone()
      const json = await clone.json()
      if (json.code !== 200) {
        const message = json.message || 'Server error'
        Toast.error(message)
        console.error(json)
        if (json.code === 401) {
          if (
            !request.url.endsWith('/login') &&
            !request.url.endsWith('/send-code')
          ) {
            useAuth.getState().logout(false)
          }
        } else {
          return new Response(
            JSON.stringify({ code: json.code || 500, message }),
            {
              status: json.code || 500
            }
          )
        }
        // } else {
        //   return new Response(JSON.stringify(json.data), { status: 200 })
      }
    }
    return response
  },
  async onError({ error }) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return new Response(JSON.stringify({ code: -1, message: 'Canceled' }), {
          status: 500
        })
      }
      const message = error.message || 'Request failed'
      Toast.error(message)
      return new Response(
        JSON.stringify({
          code: 500,
          message
        }),
        {
          status: 500
        }
      )
    }
    return new Response(
      JSON.stringify({ code: 500, message: 'Unknown error' }),
      {
        status: 500
      }
    )
  }
})
