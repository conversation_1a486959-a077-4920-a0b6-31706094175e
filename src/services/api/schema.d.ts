/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/api/v1/vendor/profile': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /**
     * Update vendor profile
     * @description Update current vendor's profile information
     */
    put: operations['updateProfile']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/profile/privacySettings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /** Update privacy settings */
    put: operations['editPrivacySettings']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/profile/personalInfo': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /** Update personal information */
    put: operations['editPersonalInfo']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/profile/notificationSettings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /** Update notification settings */
    put: operations['editNoticeSettings']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/profile/my': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get current manager's profile
     * @description Retrieves the profile of the currently authenticated manager
     */
    get: operations['queryMy']
    /**
     * Update current manager's profile
     * @description Updates current pm profile with the provided information
     */
    put: operations['editMy']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/{configId}/soft-delete': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /**
     * Soft delete system parameter
     * @description Soft deletes a system parameter by ID
     */
    put: operations['softDeleteConfig']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/{configId}/restore': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /**
     * Restore soft deleted system parameter
     * @description Restores a soft deleted system parameter by ID
     */
    put: operations['restoreConfig']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions/{sessionId}/read': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    /**
     * Mark messages as read
     * @description Mark messages of specified session as read
     */
    put: operations['markMessagesAsRead']
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/upcoming': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get vendor upcoming
     * @description Get vendor upcoming
     */
    post: operations['upcoming']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/register': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Vendor Registration
     * @description Vendor registration
     */
    post: operations['register']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/recent': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project timeline
     * @description Retrieve the timeline/activity log for a specific project
     */
    post: operations['recentActivity']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/quote/submit': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Submit quotes for items
     * @description Submit a list of item quotes for processing
     */
    post: operations['submitQuotes']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/project/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project statistics for vendor
     * @description Get project statistics for vendor
     */
    post: operations['vendorProjectStat']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/project/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get a paginated overview of all projects with filtering and management options
     * @description Retrieve a paginated overview of all projects for a project manager, allowing filtering and management
     */
    post: operations['vendorProjectList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/project/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add a new project
     * @description Create a new project using the provided information
     */
    post: operations['addProject']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/profile/verify': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Verify vendor account
     * @description Verify vendor account by email or phone verification code
     */
    post: operations['verifyAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/profile/resend': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Resend verification code
     * @description Resend verification code via specified contact method (email/phone)
     */
    post: operations['resendVerificationCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/item/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add a new item
     * @description Create a new item under a project using the provided information
     */
    post: operations['addItem']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/allocation/submit': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Submit allocations for items
     * @description Submit a list of item allocations for processing
     */
    post: operations['submitAllocations']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/upcoming': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get upcoming events
     * @description Retrieve a list of upcoming events, optionally filtered by month
     */
    post: operations['upcomingEvents']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/notifications': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get notifications
     * @description Retrieve a list of notifications for the tenant
     */
    post: operations['notifications']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/submit': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Submit maintenance request
     * @description Submit a maintenance request with item quotes for processing
     */
    post: operations['submitMaintenance']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project statistics for tenant
     * @description Retrieve statistical data regarding projects managed by the tenant manager
     */
    post: operations['tenantProjectStat']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * List projects for tenant
     * @description Retrieve a paginated overview of all projects for a tenant with filtering options
     */
    post: operations['tenantProjectList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/activity': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get maintenance activity log
     * @description Retrieve the activity log for a specific project
     */
    post: operations['maintenanceActivity']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/sms/verify-code': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Verify code
     * @description Verify SMS verification code
     */
    post: operations['verifyCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/sms/send-code': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Send verification code
     * @description Send SMS verification code to phone number
     */
    post: operations['sendVerificationCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/sms/can-send': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Check if can send code
     * @description Check if verification code can be sent (not in cooldown)
     */
    post: operations['canSendCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/quotes/approve': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Approve item quotes from vendors
     * @description Approve a list of item quotes submitted by vendors. This endpoint is currently in prototype phase and needs implementation.
     */
    post: operations['quotesApprove']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/property/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * List properties for owner
     * @description Retrieve a paginated list of properties with optional filtering based on provided criteria
     */
    post: operations['propertyOwnerPropertyList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * List projects for owner
     * @description Retrieve a paginated overview of all projects for an property owner with filtering options
     */
    post: operations['projectList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/profile': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Create property owner profile
     * @description Create a new property owner profile
     */
    post: operations['createProfile']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/vendor/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get vendor list
     * @description Get vendor list
     */
    post: operations['vendorList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/quotes/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get quotes list
     * @description Get quotes list
     */
    post: operations['quotesList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/quotes/approve': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Approve item quotes from vendors
     * @description Approve a list of item quotes submitted by vendors.
     */
    post: operations['quotesApprove_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/{propertyId}/updateTenant': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property Tenant
     * @description update property Tenant
     */
    post: operations['updateTenant']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/{propertyId}/updateOwner': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property Owner
     * @description update property Owner
     */
    post: operations['updateOwner']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/{propertyId}/deleteTenant': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property Tenant
     * @description update property Tenant
     */
    post: operations['deleteTenant']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/{propertyId}/addTenant': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * add property Tenant
     * @description add property Tenant
     */
    post: operations['addTenant']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/updateLocationInfo': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property location
     * @description update property location
     */
    post: operations['updateLocationInfo']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/updateLeaseInformation': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property Features
     * @description update property Features
     */
    post: operations['updateLeaseInformation']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/updateFeatures': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property Features
     * @description update property Features
     */
    post: operations['addPMProperty']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/updateBasicInfo': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * update property basic
     * @description update property basic
     */
    post: operations['updateBasicInfo']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get property list for PM
     * @description Retrieve a paginated list of properties with optional filtering based on provided criteria
     */
    post: operations['pmPropertyList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add a new property
     * @description Create a new property using the provided information, defaulting to 'vacant' status
     */
    post: operations['addPMProperty_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project statistics for PM
     * @description Retrieve statistical data regarding projects managed by the project manager
     */
    post: operations['pmPropertyStat']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project list for PM
     * @description Retrieve a paginated overview of all projects with filtering and management options. Allows PM to view and manage their assigned projects.
     */
    post: operations['projectList_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add a new project
     * @description Create a new project using the provided details
     */
    post: operations['addPMProject']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/item/assign': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Assign items to vendors
     * @description Assign one or more items to specific users or roles based on the provided assignment list.
     */
    post: operations['itemPMAssign']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/item/allocation': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * allocation item to vendors
     * @description allocation item to vendors
     */
    post: operations['itemAllocation']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/item/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add a new item
     * @description Create a new item under a project using the provided information.
     */
    post: operations['addPMProjectItem']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/activity': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get project timeline activity
     * @description Retrieve the timeline/activity log for a specific project with filtering options.
     */
    post: operations['recentActivity_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/login': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Login
     * @description Returns a login response
     */
    post: operations['login']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/login/getVerifyCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get Verify Code
     * @description Returns a verify code
     */
    post: operations['getVerifyCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/file/presigned-urls/upload': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Generate Upload Presigned URLs
     * @description Generates pre-signed URLs for uploading files. Each URL is valid for a limited time and can be used to upload one file.
     */
    post: operations['generateUploadUrl']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/file/presigned-urls/download': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Generate Download Presigned URLs
     * @description Generates pre-signed URLs for downloading files. Each URL is valid for a limited time and can be used to download one file.
     */
    post: operations['generateDownloadUrl']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/file/presigned-url/upload': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Generate Upload Presigned URL
     * @description Generates pre-signed URL for uploading a file. The URL is valid for a limited time and can be used to upload one file.
     */
    post: operations['generateUploadUrl_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get system parameter data
     * @description Returns a paginated list of system parameters
     */
    post: operations['list']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/deleted': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Get soft deleted system parameters
     * @description Returns a paginated list of soft deleted system parameters
     */
    post: operations['getSoftDeletedConfigs']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get user chat sessions
     * @description Get all chat sessions for current user
     */
    get: operations['getUserChatSessions']
    put?: never
    /**
     * Create chat session
     * @description Create a new chat session
     */
    post: operations['createChatSession']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions/{sessionId}/members': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Add session members
     * @description Add new members to specified session
     */
    post: operations['addSessionMembers']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/messages': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Send message
     * @description Send message to specified session
     */
    post: operations['sendMessage']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/admin/user/add': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Create new user
     * @description Create a new system user account
     */
    post: operations['add']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/me/password': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update Vendor Password
     * @description Update current vendor's password
     */
    patch: operations['updatePassword']
    trace?: never
  }
  '/api/v1/tenant/me/password': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update Tenant Password
     * @description Update current tenant's password
     */
    patch: operations['updatePassword_1']
    trace?: never
  }
  '/api/v1/property-owner/profile/{ownerId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get property owner profile
     * @description Retrieve property owner profile by owner ID
     */
    get: operations['getProfile']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update property owner profile
     * @description Partially update an existing property owner profile
     */
    patch: operations['updateProfile_1']
    trace?: never
  }
  '/api/v1/property-owner/me/password': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update Property Owner Password
     * @description Update current property owner's password
     */
    patch: operations['updatePassword_2']
    trace?: never
  }
  '/api/v1/pm/me/password': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update PM Password
     * @description Update current property manager's password
     */
    patch: operations['updatePassword_3']
    trace?: never
  }
  '/api/v1/admin/user/{userId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update user
     * @description Partially update existing user information
     */
    patch: operations['edit']
    trace?: never
  }
  '/api/v1/admin/user/me': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get current user base info
     * @description Retrieve current user base info
     */
    get: operations['getMyInfo']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update current user profile
     * @description Partially update current user's profile information
     */
    patch: operations['updateMyProfile']
    trace?: never
  }
  '/api/v1/admin/user/me/password': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update Password
     * @description Update current user's password
     */
    patch: operations['updatePassword_4']
    trace?: never
  }
  '/api/v1/admin/user/me/avatar': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Update avatar
     * @description Update current user's avatar
     */
    patch: operations['updateAvatar']
    trace?: never
  }
  '/api/v1/vendor/project/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project details
     * @description Retrieve detailed information about a specific project, optionally filtered by item name
     */
    get: operations['vendorProjectDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/profile/info': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get vendor information
     * @description Retrieve vendor's base information and profile information
     */
    get: operations['getInfo']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/performance': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get vendor performance
     * @description Get vendor performance
     */
    get: operations['performance']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/overview': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get vendor overview
     * @description Get vendor overview
     */
    get: operations['overview']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/vendor/opportunities': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get vendor opportunities
     * @description Get vendor opportunities
     */
    get: operations['opportunities']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/property': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Retrieve property information for tenant
     * @description Get statistical data regarding projects managed by the tenant manager
     */
    get: operations['myProperty']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/profile/my': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /** Get current user's profile */
    get: operations['getMyProfile']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project details
     * @description Retrieve detailed information about a specific project by ID
     */
    get: operations['maintenanceDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/tenant/maintenance/cancel/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * cancel project
     * @description cancel project
     */
    get: operations['maintenanceCancel']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/quotes/list': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Approve item quotes
     * @description Approve item quotes
     */
    get: operations['propertyOwnerQuotesList']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/quotes/detail/{quoteId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * item quotes detail
     * @description item quotes detail
     */
    get: operations['propertyOwnerQuotesDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/property/{propertyId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * property detail info
     * @description property detail info
     */
    get: operations['propertyOwnerPropertyDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/property/{propertyId}/detail-with-lease': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * property detail with lease info
     * @description property detail with lease info
     */
    get: operations['propertyOwnerPropertyDetailWithLease']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/property/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get property statistics for owner
     * @description Retrieve statistical information about properties managed by the owner
     */
    get: operations['propertyOwnerPropertyStat']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project details
     * @description Retrieve detailed information about a specific project, optionally filtered by item name
     */
    get: operations['projectOverview']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/timeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project timeline
     * @description Retrieve the timeline/activity log for a specific project
     */
    get: operations['projectTimeline']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project statistics for owner
     * @description Retrieve statistical data regarding projects managed by the owner
     */
    get: operations['propertyOwnerProjectStat']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/complete': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * List completed projects
     * @description Retrieve all completed projects for an owner
     */
    get: operations['completeProjectList']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/project/active': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * List active projects
     * @description Retrieve all active projects for an owner
     */
    get: operations['activeProjectList']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/dashboard': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get homepage statistics for property owner
     * @description Get homepage statistics for property owner
     */
    get: operations['dashboard']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/property-owner/cost/category': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Vendor Cost Analytics
     * @description Vendor Cost Analytics
     */
    get: operations['costAnalytics']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/user/tenant': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get tenant user list
     * @description Get tenant user list
     */
    get: operations['tenantUserList']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/user/property-owner': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get property owner user list
     * @description Get property owner user list
     */
    get: operations['propertyOwnerUserList']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/quotes/timeline/{quoteId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get quotes timeline
     * @description Get quotes timeline
     */
    get: operations['quotesTimeline']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/{propertyId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Retrieve property detail information
     * @description Retrieve property detail information
     */
    get: operations['propertyDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/property/stat': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get property statistics for PM
     * @description Get property statistics for PM
     */
    get: operations['propertyOverview']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/timeline/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project timeline
     * @description Retrieve the timeline/activity log for a specific project. Provides chronological record of all events related to the project.
     */
    get: operations['projectTimeline_1']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/overview/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get project details
     * @description Retrieve detailed information about a specific project, including all associated properties and items. Optionally filtered by item name.
     */
    get: operations['projectOverview_1']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/item/{projectId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get detailed project information
     * @description Retrieve comprehensive information about a specific project, including property details, item allocations, and vendor assignments. Optionally filtered by item name.
     */
    get: operations['projectDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/item/detail/{itemId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get item detail information
     * @description Get item detail information
     */
    get: operations['projectItemDetail']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/pm/project/item/delete/{itemId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * delete item
     * @description delete item
     */
    get: operations['deleteItem']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/oauth/render/{source}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Redirect to OAuth provider
     * @description Redirects user to the specified OAuth provider's authorization page
     */
    get: operations['renderAuth']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/oauth/callback/{source}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * OAuth callback endpoint
     * @description Handles OAuth provider callback and returns login response with JWT token
     */
    get: operations['callback']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/oauth/authorize-url/{source}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get authorization URL
     * @description Returns the authorization URL for the specified OAuth provider
     */
    get: operations['getAuthorizeUrl']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get All Dictionary Items Grouped by Type
     * @description Returns all dictionary items grouped by their type
     */
    get: operations['getAllGroupedByType']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries/{type}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Dictionary Items by Type
     * @description Returns all dictionary items for the specified type
     */
    get: operations['getByType']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries/{type}/{code}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Dictionary Item by Type and Code
     * @description Returns a specific dictionary item by type and code
     */
    get: operations['getByTypeAndCode']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries/us-states-cities': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get US States with Cities and Zipcodes
     * @description Returns all US states with their major cities and zipcodes, sorted with Eastern states first
     */
    get: operations['getUSStatesWithCities']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries/types': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get All Dictionary Types
     * @description Returns a list of all available dictionary types
     */
    get: operations['getAllTypes']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/dictionaries/all': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get All Dictionary Items
     * @description Returns all dictionary items as a flat list
     */
    get: operations['getAllItems']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/{configId}/is-deleted': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Check if system parameter is soft deleted
     * @description Returns whether a system parameter is soft deleted
     */
    get: operations['isSoftDeleted']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/value/{configKey}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get system parameter value by key
     * @description Returns the value of a system parameter by its key
     */
    get: operations['getConfigValue']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/key/{configKey}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get system parameter by key
     * @description Returns a system parameter by its key
     */
    get: operations['getByKey']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/enabled': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get enabled system parameters
     * @description Returns a list of enabled system parameters
     */
    get: operations['getEnabledConfigs']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/unread-count': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get unread message count
     * @description Get unread message count for current user
     */
    get: operations['getUnreadMessageCount']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions/{sessionId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get session details
     * @description Get detailed information of specified chat session
     */
    get: operations['getChatSession']
    put?: never
    post?: never
    /**
     * Delete chat session
     * @description Delete specified chat session
     */
    delete: operations['deleteChatSession']
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions/{sessionId}/messages': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get session messages
     * @description Get message list of specified session
     */
    get: operations['getSessionMessages']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/admin/user': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Search users with pagination
     * @description Retrieve paged user list with search criteria
     */
    get: operations['queryByPage']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/admin/user/{id}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get user by ID
     * @description Retrieve single user details by user ID
     */
    get: operations['queryById']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/config/{configId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    /**
     * Hard delete system parameter
     * @description Permanently deletes a system parameter by ID
     */
    delete: operations['hardDeleteConfig']
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/api/v1/chat/sessions/{sessionId}/members/{userId}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post?: never
    /**
     * Remove session member
     * @description Remove member from specified session
     */
    delete: operations['removeSessionMember']
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
}
export type webhooks = Record<string, never>
export interface components {
  schemas: {
    WrapperResponseString: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: string
    }
    /** @description Edit vendor profile input object */
    VendorProfileVO: {
      /** @description vendor user full name */
      fullName?: string
      /** @description vendor user phone number */
      phoneNumber?: string
      /** @description vendor service area */
      serviceArea?: string
      /** @description vendor offer service types, multiple connected by ',' */
      serviceTypes?: string
      /** @description vendor experience level */
      experienceLevel?: string
      /** @description vendor business name */
      businessName?: string
      /** @description vendor license number */
      licenseNumber?: string
      /** @description type of license, e.g. Plumbing */
      licenseType?: string
      /**
       * Format: date-time
       * @description License Expiry Date
       */
      licenseExpiryDate?: string
      /** @description Insurance Provider */
      insuranceProvider?: string
      /** @description Insurance Policy Number */
      insurancePolicyNumber?: string
      /** @description Insurance Coverage */
      insuranceCoverage?: string
      /**
       * Format: date-time
       * @description Insurance Expiry Date
       */
      insuranceExpiryDate?: string
    }
    /** @description Vendor Profile Entity Class */
    VendorProfile: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description vendor user id
       */
      vendorId?: number
      /** @description is vendor profile opened to all manager */
      isProfileOpened?: boolean
      /** @description is vendor top rated */
      isTopRated?: boolean
      /** @description vendor title */
      title?: string
      /**
       * Format: float
       * @description vendor experience years
       */
      experienceYear?: number
      /** @description vendor introduction */
      aboutMe?: string
      /** @description vendor offer service types, multiple connected by ',' */
      serviceTypes?: string
      /** @description vendor business name */
      businessName?: string
      /** @description vendor service area */
      serviceArea?: string
      /** @description vendor license number */
      licenseNumber?: string
      /** @description vendor specialties, multiple connected by ',' */
      specialties?: string
      /** @description vendor experience level */
      experienceLevel?: string
      /** @description type of license, e.g. Plumbing */
      licenseType?: string
      /**
       * Format: date-time
       * @description License Expiry Date
       */
      licenseExpiryDate?: string
      /** @description Insurance Provider */
      insuranceProvider?: string
      /** @description Insurance Policy Number */
      insurancePolicyNumber?: string
      /** @description Insurance Coverage */
      insuranceCoverage?: string
      /**
       * Format: date-time
       * @description Insurance Expiry Date
       */
      insuranceExpiryDate?: string
      /** @description Get notified when new jobs matching skills are available */
      receiveNewJobNotice?: boolean
      /** @description Receive alerts when bids are accepted or declined */
      receiveBidUpdateNotice?: boolean
      /** @description Get reminders before scheduled project start dates */
      receiveProjectStartNotice?: boolean
      /** @description Receive push notifications on device */
      receivePushNotice?: boolean
      /** @description Receive notifications via email */
      receiveEmailNotice?: boolean
      /** @description Receive urgent alerts via SMS */
      receiveSmsNotice?: boolean
    }
    WrapperResponseVendorProfile: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorProfile']
    }
    /** @description Tenant privacy settings configuration */
    TenantPrivacySettings: {
      /**
       * @description Allow app to collect usage data for improvement
       * @example true
       */
      allowDataCollection?: boolean
      /**
       * @description Receive marketing communications and offers
       * @example false
       */
      receiveMarketingCommunications?: boolean
    }
    WrapperResponseBoolean: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: boolean
    }
    /** @description Personal information data */
    TenantPersonalInfo: {
      /** @description Full name of the tenant */
      fullName?: string
      /** @description Phone number of the tenant */
      phoneNumber?: string
      /**
       * Format: date-time
       * @description Date of birth of the tenant
       */
      birthday?: string
    }
    /** @description Tenant notification settings */
    TenantNotificationSettings: {
      /** @description Whether to receive email notifications */
      receiveEmailNotice?: boolean
      /** @description Whether to receive emergency alerts */
      receiveEmergencyAlerts?: boolean
      /** @description Whether to receive general announcements */
      receiveGeneralAnnouncements?: boolean
      /** @description Whether to receive maintenance request updates */
      receiveMrUpdates?: boolean
      /** @description Whether to receive push notifications */
      receivePushNotice?: boolean
      /** @description Whether to receive scheduled inspection alerts */
      receiveScheduledInspections?: boolean
      /** @description Whether to receive SMS notifications */
      receiveSmsNotice?: boolean
    }
    /** @description Updated manager profile information */
    PMProfileVO: {
      /** @description Full name of the property manager */
      fullName?: string
      /** @description Phone number of the property manager */
      phoneNumber?: string
      /** @description Company name of the property manager */
      company?: string
      /** @description location of the property manager */
      location?: string
      /** @description auto approve switch setting */
      enableAutoApprove?: boolean
      /** @description property owner approve switch setting */
      enableOwnerApprove?: boolean
      /**
       * Format: double
       * @description manager approve threshold
       */
      pmThreshold?: number
      /**
       * Format: double
       * @description property owner approve threshold
       */
      ownerThreshold?: number
      /** @description default contact of owner */
      defaultOwnerContact?: string
      /** @description Receive notifications via email */
      receiveEmailNotice?: boolean
      /** @description Receive urgent property alerts */
      receiveEmergencyAlerts?: boolean
      /** @description Get notified of new maintenance requests */
      receiveMaintenanceRequests?: boolean
      /** @description Receive alerts for new vendor quotes */
      receiveNewQuotes?: boolean
      /** @description Get notified when project status changes */
      receivePsUpdates?: boolean
      /** @description Receive push notifications on device */
      receivePushNotice?: boolean
      /** @description Receive urgent alerts via SMS */
      receiveSmsNotice?: boolean
    }
    WrapperResponseListString: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: string[]
    }
    WrapperResponseVoid: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: Record<string, never>
    }
    /** @description VendorVO */
    VendorVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: date-time */
      startDate?: string
      /** Format: date-time */
      endDate?: string
      /** Format: date-time */
      currentDate?: string
    }
    /** @description VendorScheduleDTO */
    VendorScheduleDTO: {
      /** Format: int32 */
      inDays?: number
      priority?: string
      /** Format: int64 */
      projectId?: number
      projectName?: string
      /** Format: int32 */
      completeItemCount?: number
      /** Format: int32 */
      itemCount?: number
      itemIds?: string
      completeRate?: number
      /** Format: date-time */
      startDate?: string
      /** Format: date-time */
      endDate?: string
    }
    WrapperResponseListVendorScheduleDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorScheduleDTO'][]
    }
    /** @description RegistryVO, used for vendor registry */
    VendorRegistryVO: {
      /**
       * @description Vendor's Full Name
       * @example Michael Jackson
       */
      userName: string
      /**
       * @description Vendor's Email address
       * @example <EMAIL>
       */
      email: string
      /**
       * @description Vendor's Phone number
       * @example +16502530000
       */
      phoneNumber: string
      /**
       * @description Vendor's Password
       * @example A123456a
       */
      password: string
      /**
       * @description Vendor's Service Type
       * @example Plumbing, Electrical, Painting
       */
      serviceType: string
      /**
       * @description Vendor's specialties
       * @example Bathroom Rehab, Kitchen Remodeling
       */
      specialties?: string
      /**
       * @description Vendor's Service Area
       * @example Austin, TX
       */
      serviceArea: string
      /**
       * @description Vendor's Experience Level
       * @example New
       */
      experienceLevel: string
      /**
       * @description Vendor's License Number
       * @example TX-PL-12345
       */
      licenseNumber?: string
      /**
       * @description Vendor's License Type
       * @example Plumbing
       */
      licenseType?: string
      /**
       * Format: date-time
       * @description Vendor's License Expiry Date
       */
      licenseExpiryDate?: string
      /**
       * @description Vendor's Business Name
       * @example State Farm
       */
      insuranceProvider?: string
      /**
       * @description Insurance Policy Number
       * @example SF-123456789
       */
      insurancePolicyNumber?: string
      /**
       * @description Insurance Coverage
       * @example $2M Coverage
       */
      insuranceCoverage?: string
      /**
       * Format: date-time
       * @description Insurance Expiry Date
       */
      insuranceExpiryDate?: string
      profileOpened?: boolean
      noticeAgreed?: boolean
    }
    WrapperResponseObject: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: Record<string, never>
    }
    /** @description ActivityDTO */
    ActivityVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      activityId?: number
      activityType?: string
      /**
       * Format: date-time
       * @description activity log start date
       */
      startDate?: string
      /**
       * Format: date-time
       * @description activity log end date
       */
      endDate?: string
    }
    /** @description ActivityDTO */
    ActivityDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      activityId?: number
      activityType?: string
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      tenantId?: number
      /** Format: int64 */
      vendorId?: number
      title?: string
      description?: string
    }
    WrapperResponseListActivityDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ActivityDTO'][]
    }
    /** @description ItemQuotes */
    VendorItemQuotesSubmitDTO: {
      /** Format: int64 */
      projectId: number
      /** Format: int64 */
      itemId: number
      /** Format: int64 */
      vendorId: number
      /** @description quote number submitted by vendor */
      submittedQuote?: number
      status?: string
    }
    /** @description ProjectInfo VO */
    ProjectInfoVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      propertyId?: number
      /** Format: int64 */
      projectId?: number
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType?:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      /**
       * @description Project status
       * @example DRAFT
       * @enum {string}
       */
      projectStatus?:
        | 'DRAFT'
        | 'SUBMITTED'
        | 'PENDING_QUOTES'
        | 'IN_PROGRESS'
        | 'COMPLETED'
        | 'CANCELLED'
      /**
       * @description Project status list for filtering
       * @example [
       *       "DRAFT",
       *       "SUBMITTED"
       *     ]
       */
      projectStatusList?: string[]
      /**
       * @description Project type list for filtering
       * @example [
       *       "MAINTENANCE_REQUEST",
       *       "WORK_ORDER"
       *     ]
       */
      projectTypeList?: string[]
      projectManagerList?: number[]
      /** Format: date-time */
      dateRangeStart?: string
      /** Format: date-time */
      dateRangeEnd?: string
      budgetRangeMin?: number
      budgetRangeMax?: number
      vendorSelfProjectFlag?: string
      vendorType?: string
    }
    WrapperResponseJSONObject: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: {
        innerMap?: {
          [key: string]: Record<string, never>
        }
        empty?: boolean
      } & {
        [key: string]: Record<string, never>
      }
    }
    /** @description FileInfo */
    FileInfo: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description unique id of file
       */
      fileId?: number
      /** @description associated business entity type,  property/project/item/message/user */
      entityType?: string
      /**
       * Format: int64
       * @description associated business entity id
       */
      entityId?: number
      /** @description fie key path in s3 */
      fileKey: string
      /** @description original file name */
      fileName?: string
      /** @description file category, avatar/main_photo/other_photo/create_photo/finish_photo/unknown */
      category?: string
      fileUrl?: string
    }
    /** @description ItemAllocations */
    ItemAllocations: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description allocation_id
       */
      allocationId?: number
      /**
       * Format: int64
       * @description project_id
       */
      projectId?: number
      /**
       * Format: int64
       * @description property_id
       */
      propertyId?: number
      /**
       * Format: int64
       * @description item_id
       */
      itemId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      vendorId?: number
      /** @description final_amount */
      finalAmount?: number
      /** @description [enum] pending/accepted/rejected */
      allocationStatus?: string
      /**
       * Format: date-time
       * @description vendor_response_time
       */
      vendorResponseTime?: string
      /** @description e.g. reject reason */
      vendorResponseNotes?: string
      /**
       * Format: date-time
       * @description plan_start_date
       */
      planStartDate?: string
      /**
       * Format: date-time
       * @description actual_start_date
       */
      actualStartDate?: string
      /**
       * Format: date-time
       * @description vendor_completed_time
       */
      vendorCompletedTime?: string
      /**
       * Format: date-time
       * @description manager_confirmed_time
       */
      managerConfirmedTime?: string
      vendorName?: string
      vendorAvatar?: string
      vendorBusinessName?: string
      /** Format: int32 */
      vendorReviewCount?: number
      vendorRating?: number
      projectName?: string
      itemName?: string
      cancelled?: boolean
    }
    /** @description item info */
    ItemInfoDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      propertyId: number
      /** Format: int64 */
      projectId: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId: number
      /** Format: int64 */
      leaseId?: number
      status?: string
      areaType: string
      areaName: string
      itemName: string
      /** @description item description in project */
      itemDesc?: string
      /** @description [dict] */
      condition?: string
      /**
       * @description Priority level
       * @example LOW
       * @enum {string}
       */
      priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      /** @description special instructions */
      specialInstruction?: string
      /**
       * Format: date-time
       * @description expected completion date
       */
      expectedCompletionDate?: string
      /** @description budget number */
      budget?: number
      /** @description option value of item */
      option?: string
      photos?: components['schemas']['FileInfo'][]
      /**
       * Format: int32
       * @description Return the number of approved vendors
       */
      approvedVendors?: number
      /**
       * Format: int32
       * @description Return the number of rejected vendors
       */
      rejectedVendors?: number
      /**
       * Format: int32
       * @description Return the number of pending vendors
       */
      pendingVendors?: number
      /**
       * Format: int32
       * @description Return the number of vendors
       */
      totalVendors?: number
      vendorName?: string
      vendorBusinessName?: string
      quotes?: components['schemas']['ItemQuotes'][]
      allocations?: components['schemas']['ItemAllocations'][]
    }
    /** @description ItemQuotes */
    ItemQuotes: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description unique id of quote
       */
      quoteId?: number
      /**
       * Format: int64
       * @description item_id
       */
      itemId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      vendorId?: number
      /**
       * Format: int64
       * @description project_id
       */
      projectId?: number
      /**
       * Format: int64
       * @description property_id
       */
      propertyId?: number
      /** @description quote number submitted by vendor */
      submittedQuote?: number
      /**
       * Format: date-time
       * @description submitted_time
       */
      submittedTime?: string
      /** @description enum, e.g. pending_vendor/pending_manager/pending_owner/approved/rejected */
      quoteStatus?: string
      /**
       * Format: int64
       * @description approvedBy
       */
      approvedBy?: number
      /**
       * Format: date-time
       * @description approved_time
       */
      approvedTime?: string
      /** @description quote number approved by pm */
      approvedQuote?: number
      vendorName?: string
      vendorAvatar?: string
      vendorBusinessName?: string
      /** Format: int32 */
      vendorReviewCount?: number
      vendorRating?: number
      projectName?: string
      itemName?: string
      canceled?: boolean
    }
    PageInfoProjectInfoDTO: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['ProjectInfoDTO'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    /** @description ProjectInfo */
    ProjectInfoDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId: number
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /**
       * @description Project type
       * @example MAINTENANCE_REQUEST
       * @enum {string}
       */
      projectType:
        | 'MAINTENANCE_REQUEST'
        | 'WORK_ORDER'
        | 'REHAB'
        | 'VENDOR_PRIVATE'
      /**
       * @description Project status
       * @example DRAFT
       * @enum {string}
       */
      status?:
        | 'DRAFT'
        | 'SUBMITTED'
        | 'PENDING_QUOTES'
        | 'IN_PROGRESS'
        | 'COMPLETED'
        | 'CANCELLED'
      /**
       * @description Priority level
       * @example LOW
       * @enum {string}
       */
      priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      /** @description affected_units */
      affectedUnits?: string
      /** @description description */
      description?: string
      /** @description project_name */
      projectName: string
      /** Format: int64 */
      emptyRehabVendorId?: number
      /** Format: date-time */
      estimateStartDate?: string
      /** Format: date-time */
      estimateCompleteDate?: string
      estimateBudget?: number
      additionalNotes?: string
      items?: components['schemas']['ItemInfoDTO'][]
      vendorPropertyAddress?: string
      vendorPropertyType?: string
      projectTitle?: string
      itemIds?: string
      allocationVendorIds?: string
      assignedVendorIds?: string
      /** Format: int32 */
      startItemCount?: number
      /** Format: int32 */
      completeItemCount?: number
      budgetUsed?: number
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType?:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      propertyName?: string
      sizeSqFt?: number
      yearBuilt?: string
      streetAddress?: string
      city?: string
      state?: string
      delay?: string
      propertyManager?: components['schemas']['PropertyManager']
      timeline?: components['schemas']['ActivityDTO'][]
      /** Format: int32 */
      itemCount?: number
      /** Format: int32 */
      vendorCount?: number
      budget?: number
      /** Format: date-time */
      startDate?: string
      /** Format: date-time */
      endDate?: string
      /** Format: int32 */
      assignedVendorCount?: number
      /** Format: int32 */
      allocationVendorCount?: number
      itemIdList?: number[]
      allocationVendorIdList?: number[]
      assignedVendorIdList?: number[]
      vendorPhotoList?: string[]
      completedPercent?: string
    }
    /** @description PropertyManager */
    PropertyManager: {
      /**
       * Format: int64
       * @description Manager user ID
       */
      managerId?: number
      /** @description Manager name */
      name?: string
      /** @description Manager phone number */
      phoneNumber?: string
      /** @description Manager company name */
      companyName?: string
      /** @description Manager job title */
      jobTitle?: string
      /** @description Manager business address */
      businessAddress?: string
      /** @description Manager website */
      website?: string
      /** @description Manager avatar file key */
      avatar?: string
    }
    WrapperResponsePageInfoProjectInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoProjectInfoDTO']
    }
    /** @description ProjectInfo */
    ProjectInfo: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId: number
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /**
       * @description Project type
       * @example MAINTENANCE_REQUEST
       * @enum {string}
       */
      projectType:
        | 'MAINTENANCE_REQUEST'
        | 'WORK_ORDER'
        | 'REHAB'
        | 'VENDOR_PRIVATE'
      /**
       * @description Project status
       * @example DRAFT
       * @enum {string}
       */
      status?:
        | 'DRAFT'
        | 'SUBMITTED'
        | 'PENDING_QUOTES'
        | 'IN_PROGRESS'
        | 'COMPLETED'
        | 'CANCELLED'
      /**
       * @description Priority level
       * @example LOW
       * @enum {string}
       */
      priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      /** @description affected_units */
      affectedUnits?: string
      /** @description description */
      description?: string
      /** @description project_name */
      projectName: string
      /** Format: int64 */
      emptyRehabVendorId?: number
      /** Format: date-time */
      estimateStartDate?: string
      /** Format: date-time */
      estimateCompleteDate?: string
      estimateBudget?: number
      additionalNotes?: string
      items?: components['schemas']['ItemInfoDTO'][]
      vendorPropertyAddress?: string
      vendorPropertyType?: string
      projectTitle?: string
      itemIds?: string
      allocationVendorIds?: string
      assignedVendorIds?: string
      /** Format: int32 */
      startItemCount?: number
      /** Format: int32 */
      completeItemCount?: number
      budgetUsed?: number
    }
    WrapperResponseLong: {
      /** Format: int32 */
      code?: number
      message?: string
      /** Format: int64 */
      data?: number
    }
    /** @description ItemQuotes */
    VendorItemAllocationsDTO: {
      /** Format: int64 */
      projectId: number
      /** Format: int64 */
      itemId: number
      /** Format: int64 */
      vendorId: number
      allocationStatus?: string
      vendorResponseNotes?: string
      /** Format: date-time */
      actualStartDate?: string
      actualCost?: number
    }
    /** @description TenantProjectDTO */
    TenantProjectSubmitDTO: {
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId?: number
      requestTitle?: string
      areaType?: string
      areaName?: string
      itemName?: string
      description?: string
      condition?: string
      /**
       * @description Priority level
       * @example LOW
       * @enum {string}
       */
      priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      specialInstruction?: string
      option?: string
      preferredContactMethod?: string
      files?: components['schemas']['FileInfo'][]
    }
    /** @description Verify Code Validation Request */
    VerifyCodeValidationRequest: {
      /**
       * @description Phone number
       * @example +8615295785750
       */
      phoneNumber: string
      /**
       * @description Role
       * @example pm
       */
      role: string
      /**
       * @description Verification code
       * @example 123456
       */
      verifyCode: string
    }
    /** @description Verify Code Request */
    VerifyCodeRequest: {
      /**
       * @description Phone number
       * @example +8615295785750
       */
      phoneNumber: string
      /**
       * @description Role
       * @example pm
       */
      role: string
    }
    /** @description ItemQuotes */
    PMItemQuotesApproveDTO: {
      /** Format: int64 */
      projectId: number
      /** Format: int64 */
      itemId: number
      /** Format: int64 */
      vendorId: number
      approveAmount?: number
      approveStatus?: string
      description?: string
    }
    PropertyInfoVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      propertyId?: number
      /**
       * @description Property status
       * @example DRAFT
       * @enum {string}
       */
      propertyStatus?: 'DRAFT' | 'VACANT' | 'OCCUPIED'
      /**
       * @description Property status list for filtering
       * @example [
       *       "DRAFT",
       *       "VACANT"
       *     ]
       */
      propertyStatusList?: string[]
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType?:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      /**
       * @description Property type list for filtering
       * @example [
       *       "SINGLE_FAMILY",
       *       "MULTI_FAMILY"
       *     ]
       */
      propertyTypeList?: string[]
      /**
       * @description Project status
       * @example DRAFT
       * @enum {string}
       */
      projectStatus?:
        | 'DRAFT'
        | 'SUBMITTED'
        | 'PENDING_QUOTES'
        | 'IN_PROGRESS'
        | 'COMPLETED'
        | 'CANCELLED'
      /**
       * @description Project status list for filtering
       * @example [
       *       "DRAFT",
       *       "SUBMITTED"
       *     ]
       */
      projectStatusList?: string[]
    }
    PageInfoPropertyInfoDTO: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['PropertyInfoDTO'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    /** @description PropertyInfoDTO For PM */
    PropertyInfoDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      propertyId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      curLeaseId?: number
      propertyName: string
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      /**
       * Format: int32
       * @description bedroom_count
       */
      bedroomCount?: number
      /**
       * Format: int32
       * @description bathroom_count
       */
      bathroomCount?: number
      /** @description size_sq_ft */
      sizeSqFt?: number
      /** @description year_built */
      yearBuilt?: string
      /** @description street_address */
      streetAddress: string
      /** @description city */
      city: string
      /** @description dict */
      state: string
      /** @description zip_code */
      zipCode: string
      /** @description description */
      description?: string
      /** @description enum, zero/one/two/three/four/five */
      currentCondition?: string
      propertyValue?: number
      purchasePrice?: number
      /** @description additional_notes */
      additionalNotes?: string
      /** @description enum, occupied/vacant/renovation */
      status: string
      /** @description property photos */
      photos?: components['schemas']['FileInfo'][]
      monthlyRent?: number
      /** Format: date-time */
      leaseBeginDate?: string
      /** Format: date-time */
      leaseEndDate?: string
      /** Format: int32 */
      projectCount?: number
      /** Format: int32 */
      completeProjectCount?: number
      fileKey?: string
      mainPhoto?: string
      propertyOwner?: components['schemas']['SysUserVo']
      tenantList?: components['schemas']['SysUserVo'][]
      propertyManager?: components['schemas']['PropertyManager']
      roi?: number
      projectList?: components['schemas']['ProjectInfoDTO'][]
      /** Format: int32 */
      activeProjectCount?: number
      completedPercent?: string
    }
    /** @description User basic Information */
    SysUserVo: {
      /**
       * Format: int64
       * @description User ID
       */
      userId?: number
      /** @description User Full Name */
      userName?: string
      /** @description User Role */
      role?: string
      /** @description User Email */
      email?: string
      /** @description User Phone Number */
      phoneNumber?: string
      /**
       * Format: date-time
       * @description User Birthday
       */
      birthday?: string
      /** @description User Gender */
      sex?: string
      /** @description User Avatar */
      avatar?: string
      /**
       * Format: date-time
       * @description User Last Login Date
       */
      loginDate?: string
      /**
       * Format: date-time
       * @description User Password Last Update Date
       */
      pwdUpdateDate?: string
      /** @description User Remark */
      remark?: string
      /** @description User Active Status */
      isActive?: boolean
      /** @description User Deleted Status */
      isDeleted?: boolean
      /** @description User Verified Status */
      isVerified?: boolean
      /** @description User Emergency Contact */
      emergencyContact?: string
      /** @description User Notes */
      userNotes?: string
      firstName?: string
      lastName?: string
    }
    WrapperResponsePageInfoPropertyInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoPropertyInfoDTO']
    }
    /** @description PropertyOwnerProfile */
    PropertyOwnerProfile: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description Property owner ID
       */
      ownerId?: number
      /** @description Property owner address */
      address?: string
      /** @description Preferred property types */
      preferredPropertyTypes?: string
      /** @description Investment goal */
      investmentGoal?: string
      /** @description Risk tolerance */
      riskTolerance?: string
      /** @description Target ROI */
      targetRoi?: number
      /** @description Language preference */
      language?: string
      /** @description Currency preference */
      currency?: string
      /** @description Enable two-factor authentication */
      enableTwoFactor?: boolean
      /** @description Open project updates */
      openProjectUpdate?: boolean
      /** @description Open approval requests */
      openApprovalRequest?: boolean
      /** @description Open financial updates */
      openFinancialUpdate?: boolean
      /** @description Open email notices */
      openEmailNotice?: boolean
      /** @description Open push notices */
      openPushNotice?: boolean
      /** @description Open property alerts */
      openPropertyAlert?: boolean
    }
    PageInfoVendorSelectDTO: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['VendorSelectDTO'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    VendorSelectDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description vendor user id
       */
      vendorId?: number
      /** @description is vendor profile opened to all manager */
      isProfileOpened?: boolean
      /** @description is vendor top rated */
      isTopRated?: boolean
      /** @description vendor title */
      title?: string
      /**
       * Format: float
       * @description vendor experience years
       */
      experienceYear?: number
      /** @description vendor introduction */
      aboutMe?: string
      /** @description vendor offer service types, multiple connected by ',' */
      serviceTypes?: string
      /** @description vendor business name */
      businessName?: string
      /** @description vendor service area */
      serviceArea?: string
      /** @description vendor license number */
      licenseNumber?: string
      /** @description vendor specialties, multiple connected by ',' */
      specialties?: string
      /** @description vendor experience level */
      experienceLevel?: string
      /** @description type of license, e.g. Plumbing */
      licenseType?: string
      /**
       * Format: date-time
       * @description License Expiry Date
       */
      licenseExpiryDate?: string
      /** @description Insurance Provider */
      insuranceProvider?: string
      /** @description Insurance Policy Number */
      insurancePolicyNumber?: string
      /** @description Insurance Coverage */
      insuranceCoverage?: string
      /**
       * Format: date-time
       * @description Insurance Expiry Date
       */
      insuranceExpiryDate?: string
      /** @description Get notified when new jobs matching skills are available */
      receiveNewJobNotice?: boolean
      /** @description Receive alerts when bids are accepted or declined */
      receiveBidUpdateNotice?: boolean
      /** @description Get reminders before scheduled project start dates */
      receiveProjectStartNotice?: boolean
      /** @description Receive push notifications on device */
      receivePushNotice?: boolean
      /** @description Receive notifications via email */
      receiveEmailNotice?: boolean
      /** @description Receive urgent alerts via SMS */
      receiveSmsNotice?: boolean
      userName?: string
      email?: string
      phoneNumber?: string
      /** Format: date-time */
      birthday?: string
      sex?: string
      avatar?: string
    }
    WrapperResponsePageInfoVendorSelectDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoVendorSelectDTO']
    }
    /** @description item info */
    ItemInfoVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      propertyId?: number
      /** Format: int64 */
      projectId?: number
      itemName?: string
      quotesStatus?: string
    }
    /** @description ItemReviewQuotes */
    ItemReviewQuotesDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      quoteId?: number
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId?: number
      submittedQuote?: number
      pmThreshold?: number
      /** Format: date-time */
      submittedTime?: string
      quoteStatus?: string
      vendorName?: string
      vendorServiceType?: string
      vendorAvatar?: string
      vendorBusinessName?: string
      /** Format: int32 */
      vendorReviewCount?: number
      vendorRating?: number
      projectName?: string
      itemName?: string
      areaType?: string
      areaName?: string
      itemDesc?: string
      itemBudget?: number
      sizeSqFt?: number
      yearBuilt?: string
      streetAddress?: string
      city?: string
      state?: string
      zipCode?: string
    }
    PageInfoItemReviewQuotesDTO: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['ItemReviewQuotesDTO'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    WrapperResponsePageInfoItemReviewQuotesDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoItemReviewQuotesDTO']
    }
    SysUser: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      isDeleted?: boolean
      /** Format: int64 */
      userId?: number
      userName?: string
      role?: string
      email?: string
      phoneNumber?: string
      sex?: string
      avatar?: string
      password?: string
      salt?: string
      loginIp?: string
      /** Format: date-time */
      loginDate?: string
      /** Format: date-time */
      pwdUpdateDate?: string
      remark?: string
      authType?: string
      isActive?: boolean
      isVerified?: boolean
      /** Format: date-time */
      birthday?: string
      /** @description User Emergency Contact */
      emergencyContact?: string
      /** @description User Notes */
      userNotes?: string
      firstName?: string
      lastName?: string
    }
    /** @description property info */
    PropertyInfo: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      propertyId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      curLeaseId?: number
      propertyName: string
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      /**
       * Format: int32
       * @description bedroom_count
       */
      bedroomCount?: number
      /**
       * Format: int32
       * @description bathroom_count
       */
      bathroomCount?: number
      /** @description size_sq_ft */
      sizeSqFt?: number
      /** @description year_built */
      yearBuilt?: string
      /** @description street_address */
      streetAddress: string
      /** @description city */
      city: string
      /** @description dict */
      state: string
      /** @description zip_code */
      zipCode: string
      /** @description description */
      description?: string
      /** @description enum, zero/one/two/three/four/five */
      currentCondition?: string
      propertyValue?: number
      purchasePrice?: number
      /** @description additional_notes */
      additionalNotes?: string
      /** @description enum, occupied/vacant/renovation */
      status: string
      /** @description property photos */
      photos?: components['schemas']['FileInfo'][]
      monthlyRent?: number
      /** Format: date-time */
      leaseBeginDate?: string
      /** Format: date-time */
      leaseEndDate?: string
      /** Format: int32 */
      projectCount?: number
      /** Format: int32 */
      completeProjectCount?: number
    }
    /** @description PropertyLease */
    PropertyLease: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      propertyId?: number
      propertyName?: string
      /** Format: int64 */
      tenantId?: number
      /** Format: date-time */
      beginDate: string
      /** Format: date-time */
      endDate: string
      isEnded?: boolean
      monthlyRent: number
      securityDeposit?: number
      leaseNotes?: string
    }
    /** @description PMAddTenantDTO For PM */
    PMAddTenantDTO: {
      /** Format: int64 */
      tenantId?: number
      /** Format: int64 */
      leaseId?: number
      firstName: string
      lastName: string
      email: string
      phoneNumber?: string
      emergencyContact?: string
      tenantNotes?: string
      grantPortalAccessFlag?: string
    }
    /** @description PropertyInfoDTO For PM */
    PMPropertyOwnerSubmitDTO: {
      /** Format: int64 */
      ownerId?: number
      firstName?: string
      lastName?: string
      email?: string
      phoneNumber?: string
      ownerType?: string
      mailingAddress?: string
      ownerNotes?: string
      grantPortalAccessFlag?: string
    }
    /** @description PropertyInfoDTO For PM */
    PMPropertySubmitDTO: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      propertyId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      curLeaseId?: number
      propertyName: string
      /**
       * @description Property type
       * @example SINGLE_FAMILY
       * @enum {string}
       */
      propertyType:
        | 'SINGLE_FAMILY'
        | 'MULTI_FAMILY'
        | 'APARTMENT'
        | 'CONDOMINIUM'
        | 'COMMERCIAL'
        | 'OTHER'
      /**
       * Format: int32
       * @description bedroom_count
       */
      bedroomCount?: number
      /**
       * Format: int32
       * @description bathroom_count
       */
      bathroomCount?: number
      /** @description size_sq_ft */
      sizeSqFt?: number
      /** @description year_built */
      yearBuilt?: string
      /** @description street_address */
      streetAddress: string
      /** @description city */
      city: string
      /** @description dict */
      state: string
      /** @description zip_code */
      zipCode: string
      /** @description description */
      description?: string
      /** @description enum, zero/one/two/three/four/five */
      currentCondition?: string
      propertyValue?: number
      purchasePrice?: number
      /** @description additional_notes */
      additionalNotes?: string
      /** @description enum, occupied/vacant/renovation */
      status: string
      /** @description property photos */
      photos?: components['schemas']['FileInfo'][]
      monthlyRent?: number
      /** Format: date-time */
      leaseBeginDate?: string
      /** Format: date-time */
      leaseEndDate?: string
      /** Format: int32 */
      projectCount?: number
      /** Format: int32 */
      completeProjectCount?: number
      owner?: components['schemas']['PMPropertyOwnerSubmitDTO']
      tenant?: components['schemas']['PMPropertyTenantSubmitDTO']
    }
    /** @description PropertyInfoDTO For PM */
    PMPropertyTenantSubmitDTO: {
      leaseInformation?: components['schemas']['PropertyLease']
      tenantList?: components['schemas']['PMAddTenantDTO'][]
    }
    /** @description ItemQuotes */
    ItemAssignDTO: {
      /** Format: int64 */
      projectId?: number
      /**
       * Format: int64
       * @description vendor_id
       */
      vendorId?: number
      itemIdList?: number[]
    }
    /** @description Login Request */
    LoginRequest: {
      /**
       * @description Role, allowed values: admin, vendor, pm, owner, tenant
       * @example pm
       */
      role: string
      /**
       * @description Grant Type, allowed values: email, phone
       * @example email
       */
      grantType: string
      /**
       * @description Email is required when using email/password grant type
       * @example <EMAIL>
       */
      email: string
      /**
       * @description Password is required when using email/password grant type
       * @example password
       */
      password: string
      /**
       * @description Phone number is required when using phone/verifyCode grant type
       * @example +12137166828
       */
      phoneNumber: string
      /**
       * @description Verify code is required when using phone/verifyCode grant type
       * @example 123456
       */
      verifyCode: string
    }
    /** @description Response structure for user login success */
    LoginResponse: {
      /**
       * @description token (JWT), used for authentication in subsequent requests
       * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.xxxxx
       */
      token?: string
    }
    WrapperResponseLoginResponse: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['LoginResponse']
    }
    /** @description File Base DTO */
    FileBaseDTO: {
      /** @description file original name */
      fileName: string
      /** @description file content type, like 'image/jpeg', 'image/png', 'application/pdf' */
      contentType: string
      /**
       * Format: int32
       * @description file size, in bytes, must be less than 50MB
       */
      fileSize: number
    }
    /** @description Acquire Upload Presigned URL request */
    UploadUrlRequest: {
      fileList?: components['schemas']['FileBaseDTO'][]
    }
    /** @description Presigned URL Response */
    PresignedUrlResponse: {
      /** @description presigned url */
      url: string
      /** @description file key, client use this key to notify server that file upload finished */
      fileKey: string
      /** @description file origin name */
      fileName?: string
      /**
       * Format: int64
       * @description presigned url expire time in seconds
       */
      expireInSeconds?: number
    }
    WrapperResponseListPresignedUrlResponse: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PresignedUrlResponse'][]
    }
    /** @description Acquire Download Presigned URL request */
    DownloadUrlRequest: {
      fileKeys?: string[]
    }
    WrapperResponsePresignedUrlResponse: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PresignedUrlResponse']
    }
    /** @description System parameter configuration VO */
    SysConfigVO: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /**
       * Format: int32
       * @description System parameter ID
       */
      configId?: number
      /** @description System parameter name */
      configName?: string
      /** @description System parameter key */
      configKey?: string
      /** @description System parameter value */
      configValue?: string
      /**
       * Format: int32
       * @description Sort order
       */
      configSort?: number
      /** @description Configuration status */
      configStatus?: string
      /** @description Configuration icon */
      configIcon?: string
      /** @description soft delete flag */
      isDeleted?: boolean
    }
    /** @description Create chat session request */
    CreateChatSessionRequest: {
      /** @description Session name */
      sessionName: string
      /** @description Session description */
      description?: string
      /** @description Session type: private/group/project */
      sessionType: string
      /**
       * Format: int64
       * @description Associated project ID (only for project type sessions)
       */
      projectId?: number
      /**
       * Format: int64
       * @description Associated project item ID (only for project type sessions)
       */
      itemId?: number
      /** @description Member user ID list */
      memberUserIds: number[]
      /** @description Whether to enable SMS notifications */
      enableSmsNotification?: boolean
      /** @description Whether to enable email notifications */
      enableEmailNotification?: boolean
    }
    /** @description Chat member */
    ChatMember: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description Member ID
       */
      memberId?: number
      /**
       * Format: int64
       * @description Session ID
       */
      sessionId?: number
      /**
       * Format: int64
       * @description User ID
       */
      userId?: number
      /**
       * Format: int32
       * @description Unread message count
       */
      unreadCount?: number
      /**
       * Format: date-time
       * @description Last read time
       */
      lastReadTime?: string
      /**
       * Format: date-time
       * @description Join time
       */
      joinedTime?: string
      /** @description Member role: admin/member/readonly */
      role?: string
      /** @description Twilio participant SID */
      twilioParticipantSid?: string
      /** @description Whether to enable SMS notifications */
      enableSmsNotification?: boolean
      /** @description Whether to enable email notifications */
      enableEmailNotification?: boolean
      /** @description Whether user is online */
      isOnline?: boolean
      /**
       * Format: date-time
       * @description Last activity time
       */
      lastActivityTime?: string
    }
    /** @description Chat message */
    ChatMessage: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /**
       * Format: int64
       * @description Message ID
       */
      messageId?: number
      /**
       * Format: int64
       * @description Session ID
       */
      sessionId?: number
      /**
       * Format: int64
       * @description Sender user ID
       */
      senderId?: number
      /** @description Message content */
      content?: string
      /**
       * Format: date-time
       * @description Sent time
       */
      sentTime?: string
      /** @description Message type: text/image/file/system */
      messageType?: string
      /** @description Twilio message SID */
      twilioMessageSid?: string
      /** @description Message status: sent/delivered/read/failed */
      status?: string
      /** @description Whether SMS notification has been sent */
      smsSent?: boolean
      /** @description Whether email notification has been sent */
      emailSent?: boolean
      /** @description Attachment file ID list (JSON format) */
      attachmentFileIds?: string
      /** @description Message metadata (JSON format) */
      metadata?: string
    }
    /** @description Chat session DTO */
    ChatSessionDTO: {
      /**
       * Format: int64
       * @description Session ID
       */
      sessionId?: number
      /** @description Session name */
      sessionName?: string
      /** @description Session description */
      description?: string
      /** @description Session type */
      sessionType?: string
      /**
       * Format: int64
       * @description Creator user ID
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description Creation time
       */
      createdTime?: string
      lastMessage?: components['schemas']['ChatMessage']
      /**
       * Format: int32
       * @description Unread message count
       */
      unreadCount?: number
      /** @description Member list */
      members?: components['schemas']['ChatMember'][]
      /** @description Whether to enable SMS notifications */
      enableSmsNotification?: boolean
      /** @description Whether to enable email notifications */
      enableEmailNotification?: boolean
    }
    WrapperResponseChatSessionDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ChatSessionDTO']
    }
    /** @description Send message request */
    SendMessageRequest: {
      /**
       * Format: int64
       * @description Session ID
       */
      sessionId: number
      /** @description Message content */
      content: string
      /** @description Message type: text/image/file/system */
      messageType?: string
      /** @description Attachment file ID list */
      attachmentFileIds?: number[]
      /** @description Whether to send SMS notification */
      sendSmsNotification?: boolean
      /** @description Whether to send email notification */
      sendEmailNotification?: boolean
      /** @description Message metadata (JSON format) */
      metadata?: string
    }
    /** @description Chat message DTO */
    ChatMessageDTO: {
      /**
       * Format: int64
       * @description Message ID
       */
      messageId?: number
      /**
       * Format: int64
       * @description Session ID
       */
      sessionId?: number
      /**
       * Format: int64
       * @description Sender user ID
       */
      senderId?: number
      /** @description Sender name */
      senderName?: string
      /** @description Sender avatar */
      senderAvatar?: string
      /** @description Message content */
      content?: string
      /**
       * Format: date-time
       * @description Sent time
       */
      sentTime?: string
      /** @description Message type */
      messageType?: string
      /** @description Message status */
      status?: string
      /** @description Attachment file list */
      attachmentFileIds?: number[]
      /** @description Whether message has been read */
      isRead?: boolean
      /** @description Whether this is own message */
      isOwnMessage?: boolean
    }
    WrapperResponseChatMessageDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ChatMessageDTO']
    }
    WrapperResponseSysUserVo: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['SysUserVo']
    }
    /** @description Update Password Request */
    UpdatePasswordRequest: {
      /**
       * @description Old Password
       * @example oldPassword
       */
      oldPassword: string
      /**
       * @description New Password
       * @example newPassword
       */
      newPassword: string
    }
    /** @description PropertyOwnerProfile Update DTO */
    PropertyOwnerProfileUpdateDTO: {
      /** @description Property owner address */
      address?: string
      /** @description Preferred property types */
      preferredPropertyTypes?: string
      /** @description Investment goal */
      investmentGoal?: string
      /** @description Risk tolerance */
      riskTolerance?: string
      /** @description Target ROI */
      targetRoi?: number
      /** @description Language preference */
      language?: string
      /** @description Currency preference */
      currency?: string
      /** @description Enable two-factor authentication */
      enableTwoFactor?: boolean
      /** @description Enable project update notifications */
      openProjectUpdate?: boolean
      /** @description Enable approval request notifications */
      openApprovalRequest?: boolean
      /** @description Enable financial update notifications */
      openFinancialUpdate?: boolean
      /** @description Enable email notifications */
      openEmailNotice?: boolean
      /** @description Enable push notifications */
      openPushNotice?: boolean
      /** @description Enable property alerts */
      openPropertyAlert?: boolean
    }
    /** @description SysUser Update DTO */
    SysUserUpdateDTO: {
      /** @description User name */
      userName?: string
      /** @description User role */
      role?: string
      /** @description Phone number */
      phoneNumber?: string
      /** @description User gender */
      sex?: string
      /** @description Avatar file key */
      avatar?: string
      /** @description User remark */
      remark?: string
      /** @description User active status */
      isActive?: boolean
      /**
       * Format: date-time
       * @description User birthday
       */
      birthday?: string
    }
    /** @description File Key VO */
    FileKeyVO: {
      /** @description File key */
      fileKey: string
    }
    WrapperResponseProjectInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ProjectInfoDTO']
    }
    /** @description Vendor Information */
    VendorInfo: {
      baseInfo?: components['schemas']['SysUserVo']
      profileInfo?: components['schemas']['VendorProfile']
      /** Format: int32 */
      vendorReviewCount?: number
      vendorRating?: number
    }
    WrapperResponseVendorInfo: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorInfo']
    }
    /** @description PropertyOwnerPerformanceDTO */
    VendorPerformanceDTO: {
      averageRating?: number
      /** Format: int32 */
      quotesCount?: number
      /** Format: int32 */
      approvedQuotesCount?: number
      /** Format: int32 */
      acceptedOnTimeCount?: number
      /** Format: int32 */
      acceptedCount?: number
      onTimeRate?: number
      quoteSuccessRate?: number
      monthRevenueList?: components['schemas']['VendorRevenueDTO'][]
    }
    /** @description PropertyOwnerRevenueDTO */
    VendorRevenueDTO: {
      completeMonth?: string
      actualCost?: number
    }
    WrapperResponseVendorPerformanceDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorPerformanceDTO']
    }
    /** @description VendorOverViewDTO */
    VendorOverViewDTO: {
      income?: number
      incomeSr?: number
      /** Format: int32 */
      activeProjectCount?: number
      /** Format: int32 */
      pendingQuotesCount?: number
      completeRate?: number
      /** Format: int32 */
      itemCount?: number
      /** Format: int32 */
      completeItemCount?: number
    }
    WrapperResponseVendorOverViewDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorOverViewDTO']
    }
    /** @description VendorOpportunityDTO */
    VendorOpportunityDTO: {
      /** Format: int64 */
      projectId?: number
      projectName?: string
      projectType?: string
      pmName?: string
      areaType?: string
      areaName?: string
      itemName?: string
      /** Format: date-time */
      startDate?: string
      /** Format: date-time */
      endDate?: string
    }
    WrapperResponseListVendorOpportunityDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['VendorOpportunityDTO'][]
    }
    WrapperResponsePropertyInfo: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyInfo']
    }
    /** @description Tenant profile information */
    TenantProfileDTO: {
      personalInfo?: components['schemas']['SysUserVo']
      currentLease?: components['schemas']['PropertyLease']
      notificationSettings?: components['schemas']['TenantNotificationSettings']
      privacySettings?: components['schemas']['TenantPrivacySettings']
    }
    WrapperResponseTenantProfileDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['TenantProfileDTO']
    }
    WrapperResponseItemInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ItemInfoDTO']
    }
    PageInfoPropertyOwnerQuotesDTO: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['PropertyOwnerQuotesDTO'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    /** @description PropertyOwnerQuotesDTO */
    PropertyOwnerQuotesDTO: {
      propertyName?: string
      projectName?: string
      streetAddress?: string
      city?: string
      state?: string
      zipCode?: string
      pmScopeAmount?: number
      vendorRequestAmount?: number
      /** Format: date-time */
      vendorRequestDate?: string
      approvalStatus?: string
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      quoteId?: number
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId?: number
    }
    WrapperResponsePageInfoPropertyOwnerQuotesDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoPropertyOwnerQuotesDTO']
    }
    /** @description PropertyOwnerQuotesDetailDTO */
    PropertyOwnerQuotesDetailDTO: {
      propertyName?: string
      projectName?: string
      streetAddress?: string
      city?: string
      state?: string
      zipCode?: string
      pmName?: string
      /** Format: date-time */
      projectRequestDate?: string
      itemName?: string
      itemType?: string
      itemPriority?: string
      itemStatus?: string
      itemDescription?: string
      pmScopeAmount?: number
      ownerScopeAmount?: number
      vendorRequestAmount?: number
      /** Format: date-time */
      vendorRequestDate?: string
      budgetVariance?: number
      budgetVariancePercent?: number
      approvalStatus?: string
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      quoteId?: number
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      propertyId?: number
    }
    WrapperResponsePropertyOwnerQuotesDetailDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerQuotesDetailDTO']
    }
    /** @description PropertyOwnerCostStatDTO */
    PropertyOwnerCostStatDTO: {
      areaType?: string
      completeMonth?: string
      /** Format: int32 */
      projectCount?: number
      actualCost?: number
      budget?: number
    }
    /** @description PropertyOwnerQuotesDTO */
    PropertyOwnerMaintenanceDTO: {
      /** Format: int64 */
      projectId?: number
      projectName?: string
      description?: string
      /** Format: date-time */
      startDate?: string
      /** Format: date-time */
      endDate?: string
      cost?: number
    }
    /** @description PropertyOwnerPropertyDetailDTO */
    PropertyOwnerPropertyDetailDTO: {
      financial?: components['schemas']['PropertyOwnerPropertyDetailFinancialDTO']
      details?: components['schemas']['PropertyOwnerPropertyDetailInfoDTO']
      history?: components['schemas']['PropertyOwnerMaintenanceDTO'][]
      scheduled?: components['schemas']['PropertyOwnerMaintenanceDTO'][]
    }
    /** @description PropertyOwnerPropertyDetailFinancialDTO */
    PropertyOwnerPropertyDetailFinancialDTO: {
      monthlyRent?: number
      propertyValue?: number
      annualRoi?: number
      monthList?: components['schemas']['PropertyOwnerCostStatDTO'][]
      purchasePrice?: number
      /** Format: date-time */
      purchaseDate?: string
      currentValue?: number
      monthlyMortgage?: number
      avgMonthlyMaintenance?: number
      monthlyCashFlow?: number
    }
    /** @description PropertyOwnerPropertyDetailDTO */
    PropertyOwnerPropertyDetailInfoDTO: {
      propertyType?: string
      propertyStatus?: string
      yearBuilt?: string
      propertyName?: string
      sizeSqFt?: number
      streetAddress?: string
      city?: string
      state?: string
      propertyManager?: components['schemas']['PropertyManager']
      photos?: components['schemas']['FileInfo'][]
    }
    WrapperResponsePropertyOwnerPropertyDetailDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerPropertyDetailDTO']
    }
    /** @description PropertyCountStat */
    PropertyOwnerPropertyStatDTO: {
      /** Format: int32 */
      propertyCount?: number
      /** Format: int32 */
      singleFamilyCount?: number
      /** Format: int32 */
      multiFamilyCount?: number
    }
    WrapperResponsePropertyOwnerPropertyStatDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerPropertyStatDTO']
    }
    WrapperResponseListProjectInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ProjectInfoDTO'][]
    }
    WrapperResponsePropertyOwnerProfile: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerProfile']
    }
    /** @description PropertyOwnerDashboardDTO */
    PropertyOwnerDashboardDTO: {
      monthlyIncome?: number
      monthlyIncomeSr?: number
      projectExpense?: number
      projectExpenseSr?: number
      /** Format: int32 */
      propertiesCount?: number
      /** Format: int32 */
      newPropertiesCount?: number
      /** Format: int32 */
      activeProjectCount?: number
      /** Format: int32 */
      completedProjectCount?: number
    }
    WrapperResponsePropertyOwnerDashboardDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerDashboardDTO']
    }
    WrapperResponseListPropertyOwnerCostStatDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyOwnerCostStatDTO'][]
    }
    WrapperResponseListPMAddTenantDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PMAddTenantDTO'][]
    }
    WrapperResponseListPMPropertyOwnerSubmitDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PMPropertyOwnerSubmitDTO'][]
    }
    /** @description ItemQuotes */
    ItemQuoteLog: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      quoteLogId?: number
      /** Format: int64 */
      quoteId?: number
      /** Format: int64 */
      projectId?: number
      /** Format: int64 */
      itemId?: number
      /** Format: int64 */
      userId?: number
      amount?: number
      /** Format: date-time */
      quoteTime?: string
    }
    WrapperResponseListItemQuoteLog: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ItemQuoteLog'][]
    }
    WrapperResponsePropertyInfoDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PropertyInfoDTO']
    }
    /** @description PmPropertyOverviewDTO */
    PmPropertyOverviewDTO: {
      /** Format: int32 */
      totalPropertyCount?: number
      /** Format: int32 */
      activeProjectCount?: number
      /** Format: int32 */
      pendingQuotesCount?: number
      /** Format: int32 */
      completeThisMonthProjectCount?: number
      /** Format: int32 */
      vendorCount?: number
    }
    WrapperResponsePmPropertyOverviewDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PmPropertyOverviewDTO']
    }
    PMProjectDetailDTO: {
      overview?: components['schemas']['ProjectInfoDTO']
      items?: components['schemas']['ItemInfoDTO'][]
      vendorItems?: components['schemas']['ProjectItemVendorViewDTO'][]
    }
    /** @description Project Vendor item info */
    ProjectItemVendorViewDTO: {
      /** Format: int64 */
      vendorId?: number
      vendorName?: string
      vendorProjectStatus?: string
      vendorItems?: components['schemas']['ItemInfoDTO'][]
    }
    WrapperResponsePMProjectDetailDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PMProjectDetailDTO']
    }
    /** @description profile info of the property manager */
    ManagerProfile: {
      /**
       * Format: int64
       * @description created user id
       */
      createdBy?: number
      /**
       * Format: date-time
       * @description created time
       */
      createdTime?: string
      /**
       * Format: int64
       * @description updated user id
       */
      updatedBy?: number
      /**
       * Format: date-time
       * @description updated time
       */
      updatedTime?: string
      /** @description soft delete flag */
      isDeleted?: boolean
      /** Format: int64 */
      managerId?: number
      location?: string
      enableAutoApprove?: boolean
      enableOwnerApprove?: boolean
      /** Format: double */
      pmThreshold?: number
      /** Format: double */
      ownerThreshold?: number
      defaultOwnerContact?: string
      company?: string
      receiveEmailNotice?: boolean
      receiveEmergencyAlerts?: boolean
      receiveMaintenanceRequests?: boolean
      receiveNewQuotes?: boolean
      receivePsUpdates?: boolean
      receivePushNotice?: boolean
      receiveSmsNotice?: boolean
    }
    /** @description Personal information of the property manager */
    PMProfileInfo: {
      baseInfo?: components['schemas']['SysUserVo']
      profileInfo?: components['schemas']['ManagerProfile']
    }
    WrapperResponsePMProfileInfo: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PMProfileInfo']
    }
    AuthCallback: {
      code?: string
      auth_code?: string
      state?: string
      authorization_code?: string
      oauth_token?: string
      oauth_verifier?: string
      user?: string
      error?: string
    }
    /** @description Dictionary Item */
    DictionaryItem: {
      /** @description Dictionary Type */
      type?: string
      /** @description Dictionary Code */
      code?: string
      /** @description Dictionary Label */
      label?: string
      /**
       * Format: int32
       * @description Sort Order
       */
      sort?: number
      /** @description Dictionary Options */
      options?: components['schemas']['DictionaryOption'][]
      /**
       * @description Dictionary Status
       * @enum {string}
       */
      status?: 'success' | 'error' | 'default' | 'processing' | 'warning'
      /** @description Dictionary Icon */
      icon?: string
    }
    /** @description Dictionary Option */
    DictionaryOption: {
      /** @description Option Label */
      label: string
      /** @description Option Value */
      value: string
      /**
       * @description Option Status
       * @enum {string}
       */
      status?: 'success' | 'error' | 'default' | 'processing' | 'warning'
      /** @description Option Icon */
      icon?: string
    }
    WrapperResponseMapStringListDictionaryItem: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: {
        [key: string]: components['schemas']['DictionaryItem'][]
      }
    }
    /** @description Wrapper Response for List of Dictionary Items */
    WrapperResponseListDictionaryItem: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['DictionaryItem'][]
    }
    WrapperResponseDictionaryItem: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['DictionaryItem']
    }
    /** @description US City with Zipcodes */
    USCityDTO: {
      /** @description City Name */
      cityName?: string
      /** @description List of zipcodes for this city */
      zipcodes?: string[]
      /**
       * Format: int32
       * @description Sort order for display
       */
      sortOrder?: number
    }
    /** @description US State with Cities and Zipcodes */
    USStateCityDTO: {
      /** @description State Code (e.g., NY, CA) */
      stateCode?: string
      /** @description State Name (e.g., New York, California) */
      stateName?: string
      /** @description State Region (e.g., Northeast, West) */
      region?: string
      /**
       * Format: int32
       * @description Sort order for display (Eastern states first)
       */
      sortOrder?: number
      /** @description List of major cities in this state */
      cities?: components['schemas']['USCityDTO'][]
    }
    WrapperResponseListUSStateCityDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['USStateCityDTO'][]
    }
    WrapperResponseInteger: {
      /** Format: int32 */
      code?: number
      message?: string
      /** Format: int32 */
      data?: number
    }
    WrapperResponseListChatSessionDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ChatSessionDTO'][]
    }
    WrapperResponseListChatMessageDTO: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['ChatMessageDTO'][]
    }
    /** @description User Query */
    QueryUserVo: {
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int64 */
      managerId?: number
      /** Format: int64 */
      ownerId?: number
      /** Format: int64 */
      vendorId?: number
      /** Format: int64 */
      tenantId?: number
      searchText?: string
      sortBy?: string
      isAsc?: string
      /** Format: int64 */
      leaseId?: number
      /** Format: int64 */
      userId?: number
      userName?: string
      role?: string
      email?: string
      phoneNumber?: string
      authType?: string
      isActive?: boolean
      isVerified?: boolean
    }
    PageInfoSysUser: {
      /** Format: int64 */
      total?: number
      list?: components['schemas']['SysUser'][]
      /** Format: int32 */
      pageNum?: number
      /** Format: int32 */
      pageSize?: number
      /** Format: int32 */
      size?: number
      /** Format: int64 */
      startRow?: number
      /** Format: int64 */
      endRow?: number
      /** Format: int32 */
      pages?: number
      /** Format: int32 */
      prePage?: number
      /** Format: int32 */
      nextPage?: number
      isFirstPage?: boolean
      isLastPage?: boolean
      hasPreviousPage?: boolean
      hasNextPage?: boolean
      /** Format: int32 */
      navigatePages?: number
      navigatepageNums?: number[]
      /** Format: int32 */
      navigateFirstPage?: number
      /** Format: int32 */
      navigateLastPage?: number
    }
    WrapperResponsePageInfoSysUser: {
      /** Format: int32 */
      code?: number
      message?: string
      data?: components['schemas']['PageInfoSysUser']
    }
  }
  responses: never
  parameters: never
  requestBodies: never
  headers: never
  pathItems: never
}
export type $defs = Record<string, never>
export interface operations {
  updateProfile: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VendorProfileVO']
      }
    }
    responses: {
      /** @description Vendor profile updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVendorProfile']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  editPrivacySettings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['TenantPrivacySettings']
      }
    }
    responses: {
      /** @description Successfully updated privacy settings */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  editPersonalInfo: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['TenantPersonalInfo']
      }
    }
    responses: {
      /** @description Successfully updated personal information */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  editNoticeSettings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['TenantNotificationSettings']
      }
    }
    responses: {
      /** @description Successfully updated notification settings */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  queryMy: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved the current manager's profile */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['WrapperResponsePMProfileInfo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  editMy: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PMProfileVO']
      }
    }
    responses: {
      /** @description Successfully updated the manager profile */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  softDeleteConfig: {
    parameters: {
      query?: never
      header?: never
      path: {
        configId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully soft deleted system parameter */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  restoreConfig: {
    parameters: {
      query?: never
      header?: never
      path: {
        configId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully restored system parameter */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  markMessagesAsRead: {
    parameters: {
      query?: {
        /** @description Message ID (optional) */
        messageId?: number
      }
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVoid']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  upcoming: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VendorVO']
      }
    }
    responses: {
      /** @description Get vendor upcoming */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListVendorScheduleDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  register: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VendorRegistryVO']
      }
    }
    responses: {
      /** @description Vendor registered successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseObject']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  recentActivity: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ActivityVO']
      }
    }
    responses: {
      /** @description Retrieve the timeline/activity log for a specific project */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  submitQuotes: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VendorItemQuotesSubmitDTO'][]
      }
    }
    responses: {
      /** @description Successfully submitted quotes */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  vendorProjectStat: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Get project statistics for vendor */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseJSONObject']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  vendorProjectList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned project list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addProject: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfo']
      }
    }
    responses: {
      /** @description Successfully created project */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLong']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  verifyAccount: {
    parameters: {
      query: {
        /** @description Contact method type */
        contactMethod: 'email' | 'phone'
        /** @description Verify Code, must be 6 digits */
        verifyCode: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Account verified successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  resendVerificationCode: {
    parameters: {
      query: {
        /** @description Contact method type */
        contactMethod: 'email' | 'phone'
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Verification code resent successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addItem: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ItemInfoDTO'][]
      }
    }
    responses: {
      /** @description Successfully created project item */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  submitAllocations: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VendorItemAllocationsDTO'][]
      }
    }
    responses: {
      /** @description Successfully submitted allocations */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  upcomingEvents: {
    parameters: {
      query?: {
        month?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved upcoming events */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  notifications: {
    parameters: {
      query: {
        activityVO: components['schemas']['ActivityVO']
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved notifications */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  submitMaintenance: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['TenantProjectSubmitDTO']
      }
    }
    responses: {
      /** @description Successfully submitted quotes */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLong']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  tenantProjectStat: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned property list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseJSONObject']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  tenantProjectList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned project list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  maintenanceActivity: {
    parameters: {
      query: {
        projectId: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved activity log */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  verifyCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VerifyCodeValidationRequest']
      }
    }
    responses: {
      /** @description Code verification completed */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  sendVerificationCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VerifyCodeRequest']
      }
    }
    responses: {
      /** @description Verification code sent successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  canSendCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VerifyCodeRequest']
      }
    }
    responses: {
      /** @description Check completed */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  quotesApprove: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PMItemQuotesApproveDTO']
      }
    }
    responses: {
      /** @description Successfully approved quotes */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerPropertyList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned property list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoPropertyInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description List projects for owner */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  createProfile: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyOwnerProfile']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  vendorList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully retrieved vendor list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoVendorSelectDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  quotesList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ItemInfoVO']
      }
    }
    responses: {
      /** @description Successfully retrieved quotes list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoItemReviewQuotesDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  quotesApprove_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PMItemQuotesApproveDTO'][]
      }
    }
    responses: {
      /** @description Successfully approved quotes */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateTenant: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUser']
      }
    }
    responses: {
      /** @description update property Tenant */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateOwner: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUser']
      }
    }
    responses: {
      /** @description update property Owner */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  deleteTenant: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUser']
      }
    }
    responses: {
      /** @description update property Tenant */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addTenant: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUser']
      }
    }
    responses: {
      /** @description add property Tenant */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateLocationInfo: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyInfo']
      }
    }
    responses: {
      /** @description update property location */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateLeaseInformation: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyLease']
      }
    }
    responses: {
      /** @description update property Features */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addPMProperty: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyInfo']
      }
    }
    responses: {
      /** @description update property Features */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateBasicInfo: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyInfo']
      }
    }
    responses: {
      /** @description update property basic */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  pmPropertyList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned property list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoPropertyInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addPMProperty_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PMPropertySubmitDTO']
      }
    }
    responses: {
      /** @description Property was successfully created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLong']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  pmPropertyStat: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully returned property list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseJSONObject']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectList_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfoVO']
      }
    }
    responses: {
      /** @description Successfully retrieved project list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addPMProject: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectInfo']
      }
    }
    responses: {
      /** @description project was successfully created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLong']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  itemPMAssign: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ItemAssignDTO'][]
      }
    }
    responses: {
      /** @description Successfully assigned items */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  itemAllocation: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PMItemQuotesApproveDTO'][]
      }
    }
    responses: {
      /** @description allocation item to vendors */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addPMProjectItem: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ItemInfoDTO']
      }
    }
    responses: {
      /** @description Successfully created project item */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  recentActivity_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ActivityVO']
      }
    }
    responses: {
      /** @description Successfully retrieved activity list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  login: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['LoginRequest']
      }
    }
    responses: {
      /** @description Successfully logged in */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLoginResponse']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getVerifyCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VerifyCodeRequest']
      }
    }
    responses: {
      /** @description Successfully get verify code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  generateUploadUrl: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UploadUrlRequest']
      }
    }
    responses: {
      /** @description Successfully generated upload URLs */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPresignedUrlResponse']
        }
      }
      /** @description Bad request - Invalid input parameters */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Internal server error - Failed to generate URLs */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPresignedUrlResponse']
        }
      }
    }
  }
  generateDownloadUrl: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['DownloadUrlRequest']
      }
    }
    responses: {
      /** @description Successfully generated download URLs */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPresignedUrlResponse']
        }
      }
      /** @description Bad request - Invalid file keys provided */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Internal server error - Failed to generate download URLs */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPresignedUrlResponse']
        }
      }
    }
  }
  generateUploadUrl_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['FileBaseDTO']
      }
    }
    responses: {
      /** @description Successfully generated upload URL */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePresignedUrlResponse']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  list: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysConfigVO']
      }
    }
    responses: {
      /** @description Successfully retrieved system parameters */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getSoftDeletedConfigs: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysConfigVO']
      }
    }
    responses: {
      /** @description Successfully retrieved soft deleted system parameters */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getUserChatSessions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListChatSessionDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  createChatSession: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateChatSessionRequest']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseChatSessionDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  addSessionMembers: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': number[]
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVoid']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  sendMessage: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SendMessageRequest']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseChatMessageDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  add: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUser']
      }
    }
    responses: {
      /** @description User created successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseSysUserVo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updatePassword: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdatePasswordRequest']
      }
    }
    responses: {
      /** @description Password updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updatePassword_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdatePasswordRequest']
      }
    }
    responses: {
      /** @description Password updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getProfile: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Property owner ID */
        ownerId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerProfile']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateProfile_1: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Property owner ID */
        ownerId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['PropertyOwnerProfileUpdateDTO']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseBoolean']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updatePassword_2: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdatePasswordRequest']
      }
    }
    responses: {
      /** @description Password updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updatePassword_3: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdatePasswordRequest']
      }
    }
    responses: {
      /** @description Password updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  edit: {
    parameters: {
      query?: never
      header?: never
      path: {
        userId: number
      }
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUserUpdateDTO']
      }
    }
    responses: {
      /** @description User updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseSysUserVo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description User not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          /** @example {
           *       "code": 404,
           *       "message": "User not found"
           *     } */
          'application/hal+json': unknown
        }
      }
    }
  }
  getMyInfo: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved current user info */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseSysUserVo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateMyProfile: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['SysUserUpdateDTO']
      }
    }
    responses: {
      /** @description Profile updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseSysUserVo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updatePassword_4: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdatePasswordRequest']
      }
    }
    responses: {
      /** @description Password updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  updateAvatar: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['FileKeyVO']
      }
    }
    responses: {
      /** @description Avatar updated successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  vendorProjectDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description projectId */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get project details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getInfo: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Vendor information retrieved successfully */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVendorInfo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Only vendor can access this API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVendorInfo']
        }
      }
    }
  }
  performance: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get vendor performance */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVendorPerformanceDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  overview: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get vendor overview */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVendorOverViewDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  opportunities: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get vendor opportunities */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListVendorOpportunityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  myProperty: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned property list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyInfo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getMyProfile: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved tenant profile */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseTenantProfileDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  maintenanceDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description ID of the project to retrieve */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned project details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseItemInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  maintenanceCancel: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description project ID */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned project details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerQuotesList: {
    parameters: {
      query?: {
        approvalStatus?: string
        pageSize?: number
        pageNum?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Approve item quotes */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoPropertyOwnerQuotesDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerQuotesDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description quoteId */
        quoteId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description item quotes detail */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerQuotesDetailDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerPropertyDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description property detail info */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerPropertyDetailDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerPropertyDetailWithLease: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description property detail with lease info */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerPropertyDetailDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerPropertyStat: {
    parameters: {
      query?: {
        ownerId?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned property statistics */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerPropertyStatDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectOverview: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description projectId */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get project details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectTimeline: {
    parameters: {
      query: {
        projectId: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get project timeline */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerProjectStat: {
    parameters: {
      query?: {
        ownerId?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned project statistics */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseJSONObject']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  completeProjectList: {
    parameters: {
      query?: {
        ownerId?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description List completed projects */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  activeProjectList: {
    parameters: {
      query?: {
        ownerId?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description List active projects */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  dashboard: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned statistics result */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyOwnerDashboardDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  costAnalytics: {
    parameters: {
      query?: {
        dimType?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Vendor Cost Analytics */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPropertyOwnerCostStatDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  tenantUserList: {
    parameters: {
      query?: {
        searchText?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved tenant user list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPMAddTenantDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOwnerUserList: {
    parameters: {
      query?: {
        searchText?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved property owner user list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListPMPropertyOwnerSubmitDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  quotesTimeline: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description quoteId */
        quoteId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved quotes timeline */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListItemQuoteLog']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description propertyId */
        propertyId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned property info */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePropertyInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  propertyOverview: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned property stat */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePmPropertyOverviewDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectTimeline_1: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description projectId */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved project timeline */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListActivityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectOverview_1: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description projectId */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved project details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseProjectInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectDetail: {
    parameters: {
      query?: {
        itemName?: string
      }
      header?: never
      path: {
        /** @description projectId */
        projectId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved detailed project information */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePMProjectDetailDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  projectItemDetail: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description itemId */
        itemId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Get item detail information */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseItemInfoDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  deleteItem: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description itemId */
        itemId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description delete item */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  renderAuth: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description OAuth provider name (google/apple) */
        source: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Redirect to OAuth provider authorization page */
      302: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  callback: {
    parameters: {
      query: {
        /** @description OAuth callback parameters */
        callback: components['schemas']['AuthCallback']
      }
      header?: never
      path: {
        /** @description OAuth provider name (google/apple/gitee) */
        source: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully authenticated via OAuth */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseLoginResponse']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getAuthorizeUrl: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description OAuth provider name (google/apple) */
        source: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully returned authorization URL */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': string
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getAllGroupedByType: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved grouped dictionary items */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseMapStringListDictionaryItem']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getByType: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Dictionary Type */
        type: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved dictionary items */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListDictionaryItem']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getByTypeAndCode: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Dictionary Type */
        type: string
        /** @description Dictionary Code */
        code: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved dictionary item */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseDictionaryItem']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getUSStatesWithCities: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved US states and cities data */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListUSStateCityDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getAllTypes: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved dictionary types */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getAllItems: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved all dictionary items */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListDictionaryItem']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  isSoftDeleted: {
    parameters: {
      query?: never
      header?: never
      path: {
        configId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully checked soft delete status */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getConfigValue: {
    parameters: {
      query?: never
      header?: never
      path: {
        configKey: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved system parameter value */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getByKey: {
    parameters: {
      query?: never
      header?: never
      path: {
        configKey: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved system parameter */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getEnabledConfigs: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved enabled system parameters */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getUnreadMessageCount: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseInteger']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getChatSession: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseChatSessionDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  deleteChatSession: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVoid']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  getSessionMessages: {
    parameters: {
      query?: {
        /** @description Page number */
        page?: number
        /** @description Page size */
        size?: number
      }
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListChatMessageDTO']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  queryByPage: {
    parameters: {
      query: {
        queryUserVo: components['schemas']['QueryUserVo']
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved user list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponsePageInfoSysUser']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  queryById: {
    parameters: {
      query?: never
      header?: never
      path: {
        id: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully retrieved user details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseSysUserVo']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  hardDeleteConfig: {
    parameters: {
      query?: never
      header?: never
      path: {
        configId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successfully hard deleted system parameter */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseListString']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
  removeSessionMember: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Session ID */
        sessionId: number
        /** @description User ID */
        userId: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseVoid']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/hal+json': components['schemas']['WrapperResponseString']
        }
      }
    }
  }
}
