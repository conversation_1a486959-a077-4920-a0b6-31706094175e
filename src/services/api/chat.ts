import { client } from './api'
import type { paths } from './schema'

// Type definitions
type ChatSessionDTO = NonNullable<
  paths['/api/v1/chat/sessions']['get']['responses']['200']['content']['application/hal+json']['data']
>[0]
type ChatMessageDTO = NonNullable<
  paths['/api/v1/chat/sessions/{sessionId}/messages']['get']['responses']['200']['content']['application/hal+json']['data']
>[0]
type CreateChatSessionRequest =
  paths['/api/v1/chat/sessions']['post']['requestBody']['content']['application/json']
type SendMessageRequest =
  paths['/api/v1/chat/messages']['post']['requestBody']['content']['application/json']

// Chat session related APIs
export const chatApi = {
  // Get user's chat sessions
  getUserChatSessions: () => client.GET('/api/v1/chat/sessions'),

  // Create chat session
  createChatSession: (data: CreateChatSessionRequest) =>
    client.POST('/api/v1/chat/sessions', { body: data }),

  // Get specific chat session details
  getChatSession: (sessionId: number) =>
    client.GET('/api/v1/chat/sessions/{sessionId}', {
      params: { path: { sessionId } }
    }),

  // Get session messages list
  getSessionMessages: (sessionId: number, page = 1, size = 20) =>
    client.GET('/api/v1/chat/sessions/{sessionId}/messages', {
      params: {
        path: { sessionId },
        query: { page, size }
      }
    }),

  // Send message
  sendMessage: (data: SendMessageRequest) =>
    client.POST('/api/v1/chat/messages', { body: data }),

  // Mark messages as read
  markMessagesAsRead: (sessionId: number, messageId?: number) =>
    client.PUT('/api/v1/chat/sessions/{sessionId}/read', {
      params: {
        path: { sessionId },
        query: messageId ? { messageId } : {}
      }
    }),

  // Add session members
  addSessionMembers: (sessionId: number, userIds: number[]) =>
    client.POST('/api/v1/chat/sessions/{sessionId}/members', {
      params: { path: { sessionId } },
      body: userIds
    }),

  // Remove session member
  removeSessionMember: (sessionId: number, userId: number) =>
    client.DELETE('/api/v1/chat/sessions/{sessionId}/members/{userId}', {
      params: { path: { sessionId, userId } }
    }),

  // Delete chat session
  deleteChatSession: (sessionId: number) =>
    client.DELETE('/api/v1/chat/sessions/{sessionId}', {
      params: { path: { sessionId } }
    }),

  // Get unread message count
  getUnreadMessageCount: () => client.GET('/api/v1/chat/unread-count')
}

export type {
  ChatMessageDTO,
  ChatSessionDTO,
  CreateChatSessionRequest,
  SendMessageRequest
}
