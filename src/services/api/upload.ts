import type { ImagePickerAsset } from 'expo-image-picker'

import type { components } from './schema'

import { client } from '.'

export async function uploadFiles(files: ImagePickerAsset[]) {
  const { error, data } = await client.POST(
    '/api/v1/file/presigned-urls/upload',
    {
      body: {
        fileList: files.map<components['schemas']['FileBaseDTO']>(file => ({
          contentType: file.mimeType || 'application/octet-stream',
          fileName: file.fileName || file.uri.split('/').pop() || '',
          fileSize: file.fileSize || 0
        }))
      }
    }
  )
  if (!error) {
    return Promise.all(
      data!.data!.map(async (item, index) => {
        if (files[index]?.file) {
          const res = await fetch(item.url, {
            method: 'PUT',
            body: files[index]?.file
          })
          if (res.ok) {
            return item
          }
          return Promise.reject(res)
        }
        return Promise.resolve(null)
      })
    )
  }
  return []
}
