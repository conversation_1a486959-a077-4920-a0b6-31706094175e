import { useEffect, useState } from 'react'

import { load, remove, save } from '@/utils/storage'

interface UseFormStorageOptions<T> {
  key: string
  initialValue: T
  // change read data
  reviver?: (data: T) => T
}

export function useFormStorage<T>({
  key,
  initialValue,
  reviver
}: UseFormStorageOptions<T>) {
  const [data, setData] = useState<T>(initialValue)
  const [isLoaded, setIsLoaded] = useState(false)

  // Load data from storage on mount
  useEffect(() => {
    const loadData = () => {
      try {
        let storedData = load(key)
        console.log(`Loading data for key "${key}":`, storedData)
        if (storedData) {
          storedData = reviver?.(storedData as T)
          console.log(`Setting loaded data for key "${key}":`, storedData)
          setData(storedData as T)
        }
      } catch (error) {
        console.error('Error loading form data:', error)
      } finally {
        setIsLoaded(true)
      }
    }

    loadData()
  }, [key])

  // Save data to storage whenever it changes
  useEffect(() => {
    const saveData = () => {
      if (!isLoaded) return // Skip initial save
      try {
        console.log(`Saving data for key "${key}":`, data)
        save(key, data)
      } catch (error) {
        console.error('Error saving form data:', error)
      }
    }

    saveData()
  }, [data, key, isLoaded])

  const updateData = (newData: Partial<T>) => {
    setData(prev => ({ ...prev, ...newData }))
  }

  const resetData = () => {
    setData(initialValue)
  }

  const clearData = () => {
    try {
      remove(key)
      setData(initialValue)
    } catch (error) {
      console.error('Error clearing form data:', error)
    }
  }

  return {
    data,
    updateData,
    resetData,
    clearData,
    isLoaded
  }
}
