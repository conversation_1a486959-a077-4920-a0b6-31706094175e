import { useEffect, useState } from 'react'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import type {
  DICT_ITEM_PROPERTY_STATUS,
  DICT_ITEM_PROPERTY_TYPE
} from '@/types'

type PropertyInfoDTO = components['schemas']['PropertyInfoDTO']
type PropertyInfoVO = components['schemas']['PropertyInfoVO']

interface UsePropertiesOptions {
  pageNum?: number
  pageSize?: number
  ownerId?: number
  managerId?: number
  searchText?: string
  propertyStatus?: 'DRAFT' | 'VACANT' | 'OCCUPIED'
  propertyStatusList?: ('DRAFT' | 'VACANT' | 'OCCUPIED')[]
  propertyType?:
    | 'SINGLE_FAMILY'
    | 'MULTI_FAMILY'
    | 'APARTMENT'
    | 'CONDOMINIUM'
    | 'COMMERCIAL'
    | 'OTHER'
  propertyTypeList?: (
    | 'SINGLE_FAMILY'
    | 'MULTI_FAMILY'
    | 'APARTMENT'
    | 'CONDOMINIUM'
    | 'COMMERCIAL'
    | 'OTHER'
  )[]
}

interface UsePropertiesResult {
  properties: PropertyInfoDTO[]
  total: number
  loading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Hook to fetch properties data for property owner
 */
export function useProperties(
  options: UsePropertiesOptions = {}
): UsePropertiesResult {
  const [properties, setProperties] = useState<PropertyInfoDTO[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProperties = async () => {
    try {
      setLoading(true)
      setError(null)

      const requestBody: PropertyInfoVO = {
        pageNum: options.pageNum || 1,
        pageSize: options.pageSize || 10,
        ownerId: options.ownerId,
        managerId: options.managerId,
        searchText: options.searchText,
        propertyStatus: options.propertyStatus as DICT_ITEM_PROPERTY_STATUS,
        propertyStatusList: options.propertyStatusList,
        propertyType: options.propertyType as DICT_ITEM_PROPERTY_TYPE,
        propertyTypeList: options.propertyTypeList
      }

      const { data, error: apiError } = await client.POST(
        '/api/v1/property-owner/property/list',
        { body: requestBody }
      )

      if (apiError) {
        throw new Error(apiError.message || 'Failed to fetch properties')
      }

      if (data?.data) {
        setProperties(data.data.list || [])
        setTotal(data.data.total || 0)
      } else {
        setProperties([])
        setTotal(0)
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch properties'
      )
      setProperties([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProperties()
  }, [
    options.pageNum,
    options.pageSize,
    options.ownerId,
    options.managerId,
    options.searchText,
    options.propertyStatus,
    options.propertyStatusList,
    options.propertyType,
    options.propertyTypeList
  ])

  return {
    properties,
    total,
    loading,
    error,
    refetch: fetchProperties
  }
}
