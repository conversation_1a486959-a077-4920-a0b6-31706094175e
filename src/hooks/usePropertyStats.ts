import { useEffect, useState } from 'react'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

type PropertyOwnerPropertyStatDTO =
  components['schemas']['PropertyOwnerPropertyStatDTO']

interface UsePropertyStatsResult {
  stats: PropertyOwnerPropertyStatDTO | null
  loading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Hook to fetch property statistics for property owner
 */
export function usePropertyStats(): UsePropertyStatsResult {
  const [stats, setStats] = useState<PropertyOwnerPropertyStatDTO | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error: apiError } = await client.GET(
        '/api/v1/property-owner/property/stat'
      )

      if (apiError) {
        throw new Error(
          apiError.message || 'Failed to fetch property statistics'
        )
      }

      if (data?.data) {
        setStats(data.data)
      } else {
        setStats(null)
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to fetch property statistics'
      )
      setStats(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}
