import { useEffect, useState } from 'react'

import { client } from '@/services/api'

export interface USCityDTO {
  cityName?: string
  zipcodes?: string[]
  sortOrder?: number
}

export interface USStateCityDTO {
  stateCode?: string
  stateName?: string
  region?: string
  sortOrder?: number
  cities?: USCityDTO[]
}

export interface UseUSStatesCitiesReturn {
  data: USStateCityDTO[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useUSStatesCities(): UseUSStatesCitiesReturn {
  const [data, setData] = useState<USStateCityDTO[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data: responseData, error: apiError } = await client.GET(
        '/api/v1/dictionaries/us-states-cities'
      )

      if (apiError) {
        setError(
          apiError.message || 'Failed to fetch US states and cities data'
        )
        return
      }

      if (responseData?.data) {
        setData(responseData.data)
      } else {
        setError('No data received from API')
      }
    } catch (err) {
      console.error('Error fetching US states and cities:', err)
      setError('An unexpected error occurred while fetching data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    data,
    loading,
    error,
    refetch: fetchData
  }
}
