import { useEffect, useState } from 'react'

export interface DashboardStat {
  title: string
  value: string | number
  icon:
    | 'dollar'
    | 'credit-card'
    | 'building'
    | 'tools'
    | 'checkcircle'
    | 'custom'
  trendText: string
}

interface UseDashboardStatsResult {
  stats: DashboardStat[]
  loading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Hook to fetch dashboard statistics data
 * Returns raw data without trendType and trendColor (these will be computed)
 */
export function useDashboardStats(): UseDashboardStatsResult {
  const [stats, setStats] = useState<DashboardStat[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)

      // TODO: Replace with actual API call when backend is ready
      // const { data, error: apiError } = await client.GET('/api/v1/property-owner/dashboard/stats')

      // For now, using mock data
      const mockStats: DashboardStat[] = [
        {
          title: 'Monthly Income',
          value: '$42,500',
          icon: 'dollar',
          trendText: '4.5% from last month'
        },
        {
          title: 'Project Expense',
          value: '$15,320',
          icon: 'credit-card',
          trendText: '3.2% from last month'
        },
        {
          title: 'Properties',
          value: 24,
          icon: 'building',
          trendText: '2 new this month'
        },
        {
          title: 'Active Projects',
          value: 12,
          icon: 'tools',
          trendText: '8 completed'
        }
      ]

      setStats(mockStats)
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch dashboard stats'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}
