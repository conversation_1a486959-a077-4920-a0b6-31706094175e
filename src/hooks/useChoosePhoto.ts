import type { ImagePickerAsset } from 'expo-image-picker'
import { launchCameraAsync, launchImageLibraryAsync } from 'expo-image-picker'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

export type LocalPhoto = {
  id: string
  uri: string
  asset: ImagePickerAsset
  uploadProgress: number
  isUploading: boolean
  uploadedFileInfo?: components['schemas']['FileInfo']
}

export function useChoosePhoto({
  maxCount,
  onUpload,
  onUploaded,
  onLocalPhotosSelected,
  onUploadProgress
}: {
  maxCount?: () => number
  onUpload?: (fileKeys: ImagePickerAsset[]) => void
  onUploaded?: (
    fileKeys: components['schemas']['FileInfo'][],
    uploadedPhotoIds: string[]
  ) => void
  onLocalPhotosSelected?: (localPhotos: LocalPhoto[]) => void
  onUploadProgress?: (photoId: string, progress: number) => void
}) {
  const uploadAssets = async (assets: ImagePickerAsset[]) => {
    // return local file
    const localPhotos: LocalPhoto[] = assets.map((asset, index) => ({
      id: `local_${Date.now()}_${index}`,
      uri: asset.uri,
      asset,
      uploadProgress: 0,
      isUploading: true
    }))

    onLocalPhotosSelected?.(localPhotos)
    onUpload?.(assets)

    // upload and return progress
    try {
      const fileKeys = await uploadFilesWithProgress(
        assets,
        (assetIndex, progress) => {
          const photoId = localPhotos[assetIndex]?.id
          if (photoId) {
            onUploadProgress?.(photoId, progress)
          }
        }
      )

      const uploadedPhotoIds = localPhotos.map(photo => photo.id)
      onUploaded?.(
        fileKeys.filter(Boolean) as components['schemas']['FileInfo'][],
        uploadedPhotoIds
      )
    } catch (error) {
      console.error('Upload failed:', error)
    }
  }

  const uploadFilesWithProgress = async (
    files: ImagePickerAsset[],
    onProgress?: (index: number, progress: number) => void
  ) => {
    // Use original uploadFiles function with progress tracking
    // This is a simplified version, real progress tracking is complex in React Native
    const results = await Promise.all(
      files.map(async (file, index) => {
        onProgress?.(index, 0)

        try {
          // Simulate upload progress with more realistic progression
          let currentProgress = 0
          const progressInterval = setInterval(() => {
            currentProgress += Math.random() * 15 + 5 // Increment by 5-20%
            const progress = Math.min(90, currentProgress) // Cap at 90% until actual upload completes
            onProgress?.(index, Math.round(progress * 100) / 100) // Round to 2 decimal places
          }, 300)

          // Use original upload logic
          const { error, data } = await client.POST(
            '/api/v1/file/presigned-urls/upload',
            {
              body: {
                fileList: [
                  {
                    contentType: file.mimeType || 'application/octet-stream',
                    fileName: file.fileName || file.uri.split('/').pop() || '',
                    fileSize: file.fileSize || 0
                  }
                ]
              }
            }
          )

          if (!error && data?.data?.[0]) {
            const uploadUrl = data.data[0].url

            // Read file content
            const response = await fetch(file.uri)
            const blob = await response.blob()

            // Upload to presigned URL
            const uploadResponse = await fetch(uploadUrl, {
              method: 'PUT',
              body: blob,
              headers: {
                'Content-Type': file.mimeType || 'image/jpeg'
              }
            })

            clearInterval(progressInterval)

            if (uploadResponse.ok) {
              onProgress?.(index, 100)
              return data.data[0]
            }
            throw new Error(
              `Upload failed with status ${uploadResponse.status}`
            )
          }

          clearInterval(progressInterval)
          throw new Error('Failed to get upload URL')
        } catch (error) {
          console.error('Upload error:', error)
          onProgress?.(index, 0) // Reset progress to indicate failure
          return null
        }
      })
    )

    return results.filter(Boolean) as components['schemas']['FileInfo'][]
  }

  const downloadFileKeys = async (
    fileKeys: components['schemas']['FileInfo'][]
  ) => {
    const { data } = await client.POST('/api/v1/file/presigned-urls/download', {
      body: {
        fileKeys: fileKeys
          .map(i => i.fileKey)
          .filter((k): k is string => typeof k === 'string')
      }
    })
    return data?.data?.map(i => i.url) ?? []
  }

  const chooseFromCamera = async () => {
    const result = await launchCameraAsync({
      mediaTypes: 'images',
      allowsEditing: true,
      quality: 0.7
    })
    if (!result.canceled && result.assets && result.assets[0]?.uri) {
      uploadAssets(result.assets)
    }
  }

  const chooseFromLibrary = async () => {
    const result = await launchImageLibraryAsync({
      mediaTypes: 'images',
      allowsMultipleSelection: true,
      quality: 0.7,
      selectionLimit: maxCount?.()
    })
    if (!result.canceled && result.assets) {
      uploadAssets(result.assets)
    }
  }

  return {
    uploadAssets,
    downloadFileKeys,
    chooseFromCamera,
    chooseFromLibrary
  }
}
