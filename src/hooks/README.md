# Hooks

This directory contains custom React Hooks used in the project.

## useLanguage

The `useLanguage` hook is used to fetch language data from the API, specifically designed for select-type form components.

### Features

- Automatically fetches `LANGUAGE` type data from `/api/v1/dictionaries` endpoint
- Loading state management
- Error handling
- Data caching (via `useDict` store)
- Support for data refetching

### Usage

```tsx
import { useLanguage } from '@/hooks/useLanguage'

function MyComponent() {
  const { languages, loading, error, refetch } = useLanguage()

  if (loading) {
    return <Text>Loading languages...</Text>
  }

  if (error) {
    return <Text>Error: {error}</Text>
  }

  return (
    <View>
      {languages.map(language => (
        <Text key={language.code}>{language.label}</Text>
      ))}
    </View>
  )
}
```

### Return Value

```typescript
interface UseLanguageReturn {
  languages: DictItemType[]    // List of languages
  loading: boolean            // Loading state
  error: string | null        // Error message
  refetch: () => Promise<void> // Function to refetch data
}
```

### Data Structure

Each language item contains the following properties:

```typescript
interface DictItemType {
  type?: string      // Dictionary type (LANGUAGE)
  code?: string      // Language code (e.g., 'en', 'zh')
  label?: string     // Language name (e.g., 'English', '中文')
  sort?: number      // Sort order
  status?: string    // Status
  icon?: string      // Icon
  options?: DictionaryOption[] // Options list
}
```

### Usage in Forms

```tsx
import { LanguageSelect } from '@/components/common/LanguageSelect'

function UserProfileForm() {
  const [selectedLanguage, setSelectedLanguage] = useState('')

  return (
    <LanguageSelect
      value={selectedLanguage}
      onValueChange={setSelectedLanguage}
      label="Preferred Language"
      placeholder="Select your language"
      error={formErrors.language}
    />
  )
}
```

### Testing

Run tests:

```bash
npm test -- src/hooks/__tests__/useLanguage.test.ts
```

### Dependencies

- `@/store/dict` - Dictionary data management
- `@/services/api` - API client
- `@/types` - Type definitions

### Notes

1. The hook automatically fetches data when the component mounts
2. Data is cached in the `useDict` store to avoid duplicate requests
3. If the API request fails, an error message will be displayed
4. You can manually refetch data using the `refetch` function 