import { useEffect, useMemo, useState } from 'react'

import { client } from '@/services/api'
import type { Project } from '@/types/project'

interface UseProjectsOptions {
  pageNum?: number
  pageSize?: number
  ownerId?: number
  managerId?: number
  vendorId?: number
  tenantId?: number
  searchText?: string
  projectStatus?:
    | 'DRAFT'
    | 'SUBMITTED'
    | 'PENDING_QUOTES'
    | 'IN_PROGRESS'
    | 'COMPLETED'
    | 'CANCELLED'
  projectStatusList?: (
    | 'DRAFT'
    | 'SUBMITTED'
    | 'PENDING_QUOTES'
    | 'IN_PROGRESS'
    | 'COMPLETED'
    | 'CANCELLED'
  )[]
  projectTypeList?: string[]
  propertyId?: number
  dateRangeStart?: string
  dateRangeEnd?: string
  budgetRangeMin?: number
  budgetRangeMax?: number
}

interface UseProjectsResult {
  projects: Project[]
  total: number
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useProjects(
  options: UseProjectsOptions = {}
): UseProjectsResult {
  const [projects, setProjects] = useState<Project[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Memoize the options to prevent unnecessary re-renders
  const memoizedOptions = useMemo(
    () => options,
    [
      options.pageNum,
      options.pageSize,
      options.ownerId,
      options.managerId,
      options.vendorId,
      options.tenantId,
      options.searchText,
      options.projectStatus,
      JSON.stringify(options.projectStatusList),
      JSON.stringify(options.projectTypeList),
      options.propertyId,
      options.dateRangeStart,
      options.dateRangeEnd,
      options.budgetRangeMin,
      options.budgetRangeMax
    ]
  )

  const fetchProjects = async () => {
    setLoading(true)
    setError(null)

    try {
      const { data, error: apiError } = await client.POST(
        '/api/v1/property-owner/project/list',
        {
          body: {
            pageNum: memoizedOptions.pageNum || 1,
            pageSize: memoizedOptions.pageSize || 10,
            ownerId: memoizedOptions.ownerId,
            managerId: memoizedOptions.managerId,
            vendorId: memoizedOptions.vendorId,
            tenantId: memoizedOptions.tenantId,
            searchText: memoizedOptions.searchText,
            projectStatus: memoizedOptions.projectStatus,
            projectStatusList: memoizedOptions.projectStatusList,
            projectTypeList: memoizedOptions.projectTypeList,
            propertyId: memoizedOptions.propertyId,
            dateRangeStart: memoizedOptions.dateRangeStart,
            dateRangeEnd: memoizedOptions.dateRangeEnd,
            budgetRangeMin: memoizedOptions.budgetRangeMin,
            budgetRangeMax: memoizedOptions.budgetRangeMax
          }
        }
      )

      if (apiError) {
        throw new Error(apiError.message || 'Failed to fetch projects')
      }

      if (data?.data) {
        setProjects(data.data.list || [])
        setTotal(data.data.total || 0)
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching projects:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [memoizedOptions])

  return {
    projects,
    total,
    loading,
    error,
    refetch: fetchProjects
  }
}

// Hook for completed projects using GET /api/v1/property-owner/project/complete
export function useCompletedProjects(): UseProjectsResult {
  const [projects, setProjects] = useState<Project[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCompletedProjects = async () => {
    setLoading(true)
    setError(null)

    try {
      const { data, error: apiError } = await client.GET(
        '/api/v1/property-owner/project/complete'
      )

      if (apiError) {
        throw new Error(
          apiError.message || 'Failed to fetch completed projects'
        )
      }

      if (data?.data) {
        setProjects(data.data || [])
        setTotal(data.data.length || 0)
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching completed projects:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCompletedProjects()
  }, [])

  return {
    projects,
    total,
    loading,
    error,
    refetch: fetchCompletedProjects
  }
}

// Hook for active projects using GET /api/v1/property-owner/project/active
export function useActiveProjects(): UseProjectsResult {
  const [projects, setProjects] = useState<Project[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchActiveProjects = async () => {
    setLoading(true)
    setError(null)

    try {
      const { data, error: apiError } = await client.GET(
        '/api/v1/property-owner/project/active'
      )

      if (apiError) {
        throw new Error(apiError.message || 'Failed to fetch active projects')
      }

      if (data?.data) {
        setProjects(data.data || [])
        setTotal(data.data.length || 0)
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching active projects:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchActiveProjects()
  }, [])

  return {
    projects,
    total,
    loading,
    error,
    refetch: fetchActiveProjects
  }
}

// Hook for recent projects (first 3 items)
export function useRecentProjects(): UseProjectsResult {
  const options = useMemo(() => ({ pageNum: 1, pageSize: 3 }), [])
  return useProjects(options)
}

// Hook for all projects (large page size to get all)
export function useAllProjects(): UseProjectsResult {
  const options = useMemo(() => ({ pageNum: 1, pageSize: 9999 }), [])
  return useProjects(options)
}

// Hook for pending approvals (first 1 item)
export function usePendingApprovals(): UseProjectsResult {
  const options = useMemo(
    () => ({
      pageNum: 1,
      pageSize: 1,
      projectStatusList: ['SUBMITTED', 'PENDING_QUOTES'] as (
        | 'SUBMITTED'
        | 'PENDING_QUOTES'
      )[] // Only projects that need approval
    }),
    []
  )
  return useProjects(options)
}
