import { useCallback, useEffect, useState } from 'react'

export const usePhoneSend = (
  sendFn: () => Promise<boolean>,
  defaultInterval = 60
) => {
  const [sent, setSent] = useState(false)
  const [loading, setLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)

  useEffect(() => {
    if (countdown <= 0) return

    const timer = setInterval(() => {
      setCountdown(prev => prev - 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [countdown])

  const send = useCallback(async () => {
    setLoading(true)
    if (await sendFn()) {
      setCountdown(defaultInterval)
      if (!sent) {
        setSent(true)
      }
    }
    setLoading(false)
  }, [sendFn, defaultInterval])

  return {
    send,
    sent,
    loading,
    countdown
  }
}
