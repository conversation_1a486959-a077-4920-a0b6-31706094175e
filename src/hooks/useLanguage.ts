import { useEffect, useState } from 'react'

import { useDict } from '@/store/dict'
import type { DictItemType } from '@/types'

export interface UseLanguageReturn {
  languages: DictItemType[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Hook to fetch language data from the API
 * Fetches LANGUAGE dictionary items for use in select forms
 */
export function useLanguage(): UseLanguageReturn {
  const [languages, setLanguages] = useState<DictItemType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { getDictItems } = useDict()

  const fetchLanguages = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await getDictItems('LANGUAGE')
      setLanguages(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch languages')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLanguages()
  }, [])

  return {
    languages,
    loading,
    error,
    refetch: fetchLanguages
  }
}
