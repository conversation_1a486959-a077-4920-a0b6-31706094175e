import { useEffect, useState } from 'react'

import { client } from '@/services/api'
import type { Project } from '@/types/project'

interface UseProjectDetailResult {
  project: Project | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useProjectDetail(
  projectId: number | null
): UseProjectDetailResult {
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProjectDetail = async () => {
    if (!projectId) {
      setProject(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const { data, error: apiError } = await client.GET(
        '/api/v1/property-owner/project/{projectId}',
        {
          params: { path: { projectId } }
        }
      )

      if (apiError) {
        throw new Error(apiError.message || 'Failed to fetch project details')
      }

      if (data?.data) {
        setProject(data.data)
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching project details:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjectDetail()
  }, [projectId])

  return {
    project,
    loading,
    error,
    refetch: fetchProjectDetail
  }
}
