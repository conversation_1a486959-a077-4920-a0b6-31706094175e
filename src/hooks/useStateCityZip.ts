import { useEffect, useMemo, useState } from 'react'

import { useUSStatesCities } from './useUSStatesCities'

export function useStateCityZip(props?: {
  initialState?: string
  initialCity?: string
  initialZip?: string
  onZipChange?: (zip: string) => void
}) {
  const [selectedState, setSelectedState] = useState<string | undefined>(
    props?.initialState
  )
  const [selectedCity, setSelectedCity] = useState<string | undefined>(
    props?.initialCity
  )
  const [selectedZip, setSelectedZip] = useState<string | undefined>(
    props?.initialZip
  )

  const { data: states, loading } = useUSStatesCities()

  const stateOptions = useMemo(() => {
    if (states) {
      return states.map(s => ({
        label: s.stateName!,
        value: s.stateCode!
      }))
    }
    return []
  }, [states])

  const cityOptions = useMemo(() => {
    if (selectedState) {
      const state = states?.find(s => s.stateCode === selectedState)
      return (
        state?.cities?.map(c => ({
          label: c.cityName!,
          value: c.cityName!
        })) ?? []
      )
    }
    return []
  }, [selectedState, states])

  const zipOptions = useMemo(() => {
    if (selectedCity) {
      const state = states?.find(s => s.stateCode === selectedState)
      const city = state?.cities?.find(c => c.cityName === selectedCity)
      return (
        city?.zipcodes?.map(z => ({
          label: z,
          value: z
        })) ?? []
      )
    }
    return []
  }, [selectedCity, selectedState, states])

  useEffect(() => {
    if (zipOptions.length && !selectedZip) {
      setSelectedZip(zipOptions[0]!.value)
      props?.onZipChange?.(zipOptions[0]!.value)
    }
  }, [zipOptions, selectedZip])

  return {
    selectedState,
    setSelectedState,
    selectedCity,
    setSelectedCity,
    selectedZip,
    setSelectedZip,
    stateOptions,
    cityOptions,
    zipOptions,
    loading
  }
}
