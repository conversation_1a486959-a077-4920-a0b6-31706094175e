import { renderHook, waitFor } from '@testing-library/react-native'

import { client } from '@/services/api'

import { useActiveProjects, useCompletedProjects } from '../useProjects'

// Mock the API client
jest.mock('@/services/api', () => ({
  client: {
    GET: jest.fn(),
    POST: jest.fn()
  }
}))

const mockClient = client as jest.Mocked<typeof client>

describe('useCompletedProjects', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should fetch completed projects successfully', async () => {
    const mockProjects = [
      {
        projectId: 1,
        projectName: 'Test Project 1',
        status: 'completed',
        streetAddress: '123 Main St',
        city: 'Austin',
        state: 'TX',
        startDate: '2023-06-01T00:00:00Z',
        endDate: '2023-07-01T00:00:00Z',
        estimateBudget: 5000,
        budgetUsed: 4800,
        propertyManager: {
          name: '<PERSON>',
          companyName: 'Test Management'
        },
        timeline: [
          {
            createdTime: '2023-06-01T00:00:00Z',
            description: 'Project Started',
            activityType: 'started'
          }
        ]
      }
    ]

    mockClient.GET.mockResolvedValue({
      data: { data: mockProjects },
      error: null
    })

    const { result } = renderHook(() => useCompletedProjects())

    expect(result.current.loading).toBe(true)
    expect(result.current.projects).toEqual([])

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.projects).toEqual(mockProjects)
    expect(result.current.total).toBe(1)
    expect(result.current.error).toBeNull()
    expect(mockClient.GET).toHaveBeenCalledWith(
      '/api/v1/property-owner/project/complete'
    )
  })

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch completed projects'
    mockClient.GET.mockResolvedValue({
      data: null,
      error: { message: errorMessage }
    })

    const { result } = renderHook(() => useCompletedProjects())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe(errorMessage)
    expect(result.current.projects).toEqual([])
  })
})

describe('useActiveProjects', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should fetch active projects successfully', async () => {
    const mockProjects = [
      {
        projectId: 2,
        projectName: 'Active Project 1',
        status: 'in-progress',
        streetAddress: '456 Oak St',
        city: 'Dallas',
        state: 'TX',
        estimateStartDate: '2023-08-01T00:00:00Z',
        estimateCompleteDate: '2023-09-01T00:00:00Z',
        estimateBudget: 3000,
        budgetUsed: 1500,
        propertyManager: {
          name: 'Jane Smith',
          companyName: 'Active Management'
        }
      }
    ]

    mockClient.GET.mockResolvedValue({
      data: { data: mockProjects },
      error: null
    })

    const { result } = renderHook(() => useActiveProjects())

    expect(result.current.loading).toBe(true)
    expect(result.current.projects).toEqual([])

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.projects).toEqual(mockProjects)
    expect(result.current.total).toBe(1)
    expect(result.current.error).toBeNull()
    expect(mockClient.GET).toHaveBeenCalledWith(
      '/api/v1/property-owner/project/active'
    )
  })

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch active projects'
    mockClient.GET.mockResolvedValue({
      data: null,
      error: { message: errorMessage }
    })

    const { result } = renderHook(() => useActiveProjects())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe(errorMessage)
    expect(result.current.projects).toEqual([])
  })
})
