import { renderHook, waitFor } from '@testing-library/react-native'

import { client } from '@/services/api'

import { useUSStatesCities } from '../useUSStatesCities'

// Mock the API client
jest.mock('@/services/api', () => ({
  client: {
    GET: jest.fn()
  }
}))

const mockClient = client as jest.Mocked<typeof client>

describe('useUSStatesCities', () => {
  const mockStatesData = [
    {
      stateCode: 'NY',
      stateName: 'New York',
      region: 'Northeast',
      sortOrder: 1,
      cities: [
        {
          cityName: 'New York City',
          zipcodes: ['10001', '10002', '10003'],
          sortOrder: 1
        },
        {
          cityName: 'Buffalo',
          zipcodes: ['14201', '14202'],
          sortOrder: 2
        }
      ]
    },
    {
      stateCode: 'CA',
      stateName: 'California',
      region: 'West',
      sortOrder: 2,
      cities: [
        {
          cityName: 'Los Angeles',
          zipcodes: ['90001', '90002'],
          sortOrder: 1
        },
        {
          cityName: 'San Francisco',
          zipcodes: ['94101', '94102'],
          sortOrder: 2
        }
      ]
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should fetch US states and cities data successfully', async () => {
    mockClient.GET.mockResolvedValue({
      data: { code: 200, message: 'Success', data: mockStatesData },
      error: undefined,
      response: new Response()
    })

    const { result } = renderHook(() => useUSStatesCities())

    // Initial state should be loading
    expect(result.current.loading).toBe(true)
    expect(result.current.data).toEqual([])
    expect(result.current.error).toBe(null)

    // Wait for the data to be fetched
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual(mockStatesData)
    expect(result.current.error).toBe(null)
    expect(mockClient.GET).toHaveBeenCalledWith(
      '/api/v1/dictionaries/us-states-cities'
    )
  })

  it('should handle API error', async () => {
    const errorMessage = 'Failed to fetch data'
    mockClient.GET.mockResolvedValue({
      data: undefined,
      error: { message: errorMessage },
      response: new Response()
    })

    const { result } = renderHook(() => useUSStatesCities())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual([])
    expect(result.current.error).toBe(errorMessage)
  })

  it('should handle network error', async () => {
    mockClient.GET.mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() => useUSStatesCities())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual([])
    expect(result.current.error).toBe(
      'An unexpected error occurred while fetching data'
    )
  })

  it('should handle empty response data', async () => {
    mockClient.GET.mockResolvedValue({
      data: { code: 200, message: 'Success', data: undefined },
      error: undefined,
      response: new Response()
    })

    const { result } = renderHook(() => useUSStatesCities())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual([])
    expect(result.current.error).toBe('No data received from API')
  })

  it('should provide refetch function', async () => {
    mockClient.GET.mockResolvedValue({
      data: { code: 200, message: 'Success', data: mockStatesData },
      error: undefined,
      response: new Response()
    })

    const { result } = renderHook(() => useUSStatesCities())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(typeof result.current.refetch).toBe('function')

    // Test refetch
    mockClient.GET.mockResolvedValue({
      data: {
        code: 200,
        message: 'Success',
        data: [...mockStatesData, { stateCode: 'TX', stateName: 'Texas' }]
      },
      error: undefined,
      response: new Response()
    })

    await result.current.refetch()

    expect(mockClient.GET).toHaveBeenCalledTimes(2)
  })
})
