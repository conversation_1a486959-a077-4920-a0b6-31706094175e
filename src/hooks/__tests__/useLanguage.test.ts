import { act, renderHook, waitFor } from '@testing-library/react-native'

import { useDict } from '@/store/dict'

import { useLanguage } from '../useLanguage'

// Mock the dict store
jest.mock('@/store/dict', () => ({
  useDict: jest.fn()
}))

const mockUseDict = useDict as jest.MockedFunction<typeof useDict>

describe('useLanguage', () => {
  const mockGetDictItems = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseDict.mockReturnValue({
      getDictItems: mockGetDictItems,
      cachedTypes: new Map()
    })
  })

  it('should fetch languages on mount', async () => {
    const mockLanguages = [
      { id: 1, name: 'English', code: 'en' },
      { id: 2, name: '中文', code: 'zh' }
    ]

    mockGetDictItems.mockResolvedValue(mockLanguages)

    const { result } = renderHook(() => useLanguage())

    // Initially loading
    expect(result.current.loading).toBe(true)
    expect(result.current.languages).toEqual([])
    expect(result.current.error).toBeNull()

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(mockGetDictItems).toHaveBeenCalledWith('LANGUAGE')
    expect(result.current.languages).toEqual(mockLanguages)
    expect(result.current.error).toBeNull()
  })

  it('should handle errors when fetching fails', async () => {
    const errorMessage = 'Network error'
    mockGetDictItems.mockRejectedValue(new Error(errorMessage))

    const { result } = renderHook(() => useLanguage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe(errorMessage)
    expect(result.current.languages).toEqual([])
  })

  it('should allow refetching data', async () => {
    const mockLanguages = [{ id: 1, name: 'English', code: 'en' }]

    mockGetDictItems.mockResolvedValue(mockLanguages)

    const { result } = renderHook(() => useLanguage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Clear mock to verify refetch calls
    mockGetDictItems.mockClear()

    // Trigger refetch
    await act(async () => {
      await result.current.refetch()
    })

    expect(mockGetDictItems).toHaveBeenCalledWith('LANGUAGE')
  })

  it('should handle loading state during refetch', async () => {
    const mockLanguages = [{ id: 1, name: 'English', code: 'en' }]

    mockGetDictItems.mockResolvedValue(mockLanguages)

    const { result } = renderHook(() => useLanguage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Mock a slow refetch
    mockGetDictItems.mockImplementation(
      () =>
        new Promise(resolve => setTimeout(() => resolve(mockLanguages), 100))
    )

    // Start refetch
    let refetchPromise: Promise<void>
    await act(async () => {
      refetchPromise = result.current.refetch()
    })

    // Should be loading during refetch
    expect(result.current.loading).toBe(true)

    await act(async () => {
      await refetchPromise!
    })

    expect(result.current.loading).toBe(false)
  })
})
