import { renderHook, waitFor } from '@testing-library/react-native'

import { client } from '@/services/api'

import { useProperties } from '../useProperties'

// Mock the API client
jest.mock('@/services/api', () => ({
  client: {
    POST: jest.fn()
  }
}))

const mockClient = client as jest.Mocked<typeof client>

describe('useProperties', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should fetch properties successfully', async () => {
    const mockProperties = [
      {
        propertyId: 1,
        propertyName: 'Test Property 1',
        status: 'OCCUPIED',
        streetAddress: '123 Main St',
        city: 'Austin',
        state: 'TX',
        propertyType: 'SINGLE_FAMILY',
        bedroomCount: 3,
        bathroomCount: 2,
        monthlyRent: 2450,
        roi: 9.8,
        propertyValue: 320000,
        propertyManager: {
          name: '<PERSON>',
          jobTitle: 'Property Manager'
        }
      },
      {
        propertyId: 2,
        propertyName: 'Test Property 2',
        status: 'VACANT',
        streetAddress: '456 Oak Rd',
        city: 'Denver',
        state: 'CO',
        propertyType: 'MULTI_FAMILY',
        monthlyRent: 3200,
        roi: 8.5,
        propertyValue: 1200000,
        propertyManager: {
          name: 'Michael Lee',
          jobTitle: 'Property Manager'
        }
      }
    ]

    const mockResponse = {
      data: {
        data: {
          list: mockProperties,
          total: 2
        }
      },
      error: undefined,
      response: {} as any
    }

    mockClient.POST.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useProperties())

    expect(result.current.loading).toBe(true)
    expect(result.current.properties).toEqual([])

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.properties).toHaveLength(2)
    expect(result.current.total).toBe(2)
    expect(result.current.error).toBeNull()
    expect(mockClient.POST).toHaveBeenCalledWith(
      '/api/v1/property-owner/property/list',
      {
        body: {
          pageNum: 1,
          pageSize: 10,
          ownerId: undefined,
          managerId: undefined,
          searchText: undefined,
          propertyStatus: undefined,
          propertyStatusList: undefined,
          propertyType: undefined,
          propertyTypeList: undefined
        }
      }
    )
  })

  it('should handle API errors', async () => {
    const mockError = {
      data: undefined,
      error: {
        message: 'Failed to fetch properties'
      },
      response: {} as any
    }

    mockClient.POST.mockResolvedValue(mockError)

    const { result } = renderHook(() => useProperties())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe('Failed to fetch properties')
    expect(result.current.properties).toEqual([])
    expect(result.current.total).toBe(0)
  })

  it('should handle network errors', async () => {
    mockClient.POST.mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() => useProperties())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe('Network error')
    expect(result.current.properties).toEqual([])
    expect(result.current.total).toBe(0)
  })

  it('should pass options to API call', async () => {
    const mockResponse = {
      data: {
        data: {
          list: [],
          total: 0
        }
      },
      error: undefined,
      response: {} as any
    }

    mockClient.POST.mockResolvedValue(mockResponse)

    const options = {
      pageNum: 2,
      pageSize: 5,
      searchText: 'test',
      propertyStatus: 'OCCUPIED' as const
    }

    renderHook(() => useProperties(options))

    await waitFor(() => {
      expect(mockClient.POST).toHaveBeenCalledWith(
        '/api/v1/property-owner/property/list',
        {
          body: {
            pageNum: 2,
            pageSize: 5,
            ownerId: undefined,
            managerId: undefined,
            searchText: 'test',
            propertyStatus: 'OCCUPIED',
            propertyStatusList: undefined,
            propertyType: undefined,
            propertyTypeList: undefined
          }
        }
      )
    })
  })
})
