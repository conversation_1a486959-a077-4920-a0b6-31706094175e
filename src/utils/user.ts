export function getFullName(
  obj?: {
    firstName?: string | undefined | null
    lastName?: string | undefined | null
  },
  fallback?: string
): string
export function getFullName(
  firstName: string | undefined | null,
  lastName: string | undefined | null,
  fallback?: string
): string

export function getFullName(
  firstNameOrObj:
    | {
        firstName?: string | undefined | null
        lastName?: string | undefined | null
      }
    | string
    | undefined
    | null,
  lastNameParam?: string | undefined | null,
  fallback: string = 'Unknown'
) {
  if (typeof firstNameOrObj === 'object' && firstNameOrObj !== null) {
    const { firstName, lastName } = firstNameOrObj
    return firstName && lastName
      ? `${firstName} ${lastName}`
      : firstName || lastName || fallback
  }

  const firstName = firstNameOrObj
  const lastName = lastNameParam
  return firstName && lastName
    ? `${firstName} ${lastName}`
    : firstName || lastName || fallback
}
