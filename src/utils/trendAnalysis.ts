import type { StatCardProps } from '@/components/property-owner/home/<USER>'
import type { DashboardStat } from '@/hooks/useDashboardStats'

/**
 * Analyzes trendText to determine trendType and trendColor
 * @param trendText - The trend text to analyze
 * @returns Object with trendType and trendColor
 */
export function analyzeTrend(trendText: string): {
  trendType: 'up' | 'down' | 'dot'
  trendColor: 'success' | 'danger' | 'default'
} {
  const text = trendText.toLowerCase()

  // Check for percentage patterns
  const percentageMatch = text.match(/([+-]?\d+\.?\d*)%/)
  if (percentageMatch && percentageMatch[1]) {
    const percentage = parseFloat(percentageMatch[1])
    if (percentage > 0) {
      return { trendType: 'up', trendColor: 'success' }
    } else if (percentage < 0) {
      return { trendType: 'down', trendColor: 'danger' }
    }
  }

  // Check for positive indicators
  const positiveIndicators = [
    'increase',
    'up',
    'rise',
    'growth',
    'gain',
    'higher',
    'more',
    'new',
    'completed',
    'finished'
  ]
  if (positiveIndicators.some(indicator => text.includes(indicator))) {
    return { trendType: 'up', trendColor: 'success' }
  }

  // Check for negative indicators
  const negativeIndicators = [
    'decrease',
    'down',
    'fall',
    'drop',
    'lower',
    'less',
    'reduced',
    'decline'
  ]
  if (negativeIndicators.some(indicator => text.includes(indicator))) {
    return { trendType: 'down', trendColor: 'danger' }
  }

  // Check for neutral indicators (like "completed", "finished" without context)
  const neutralIndicators = ['completed', 'finished', 'done', 'total', 'count']
  if (neutralIndicators.some(indicator => text.includes(indicator))) {
    return { trendType: 'dot', trendColor: 'success' }
  }

  // Default to neutral
  return { trendType: 'dot', trendColor: 'default' }
}

/**
 * Transforms raw dashboard stats to StatCard props with computed trendType and trendColor
 * @param stats - Raw dashboard stats data
 * @returns StatCard props with computed trend information
 */
export function transformStatsToCardProps(
  stats: DashboardStat[]
): StatCardProps[] {
  return stats.map(stat => {
    const { trendType, trendColor } = analyzeTrend(stat.trendText)

    return {
      ...stat,
      trendType,
      trendColor
    }
  })
}
