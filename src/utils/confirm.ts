import { Alert, Platform } from 'react-native'

export function confirm(
  title: string,
  onConfirm: VoidFunction,
  onCancel?: VoidFunction
) {
  if (Platform.OS === 'web') {
    if (window.confirm(title)) {
      onConfirm()
    } else {
      onCancel?.()
    }
  } else {
    Alert.alert('Confirm', title, [
      { text: 'Cancel', style: 'cancel', onPress: onCancel },
      { text: 'Confirm', style: 'default', onPress: onConfirm }
    ])
  }
}
