// Note the syntax of these imports from the date-fns library.
// If you import with the syntax: import { format } from 'date-fns' the ENTIRE library
// will be included in your production bundle (even if you only use one function).
// This is because react-native does not support tree-shaking.
import { format } from 'date-fns/format'
import type { Locale } from 'date-fns/locale'
import { parseISO } from 'date-fns/parseISO'
import i18n from 'i18next'

type Options = Parameters<typeof format>[2]

let dateFnsLocale: Locale
export const loadDateFnsLocale = () => {
  const primaryTag = i18n.language.split('-')[0]
  switch (primaryTag) {
    case 'en':
      dateFnsLocale = require('date-fns/locale/en-US').default
      break
    case 'es':
      dateFnsLocale = require('date-fns/locale/es').default
      break
    default:
      dateFnsLocale = require('date-fns/locale/en-US').default
      break
  }
}

export const formatDate = (
  date: string | Date | undefined,
  dateFormat?: string,
  options?: Options
) => {
  if (!date) {
    return 'N/A'
  }
  const dateOptions = {
    ...options,
    locale: dateFnsLocale
  }
  return format(
    date instanceof Date ? date : parseISO(date),
    dateFormat ?? 'MMM dd, yyyy',
    dateOptions
  )
}

/**
 * Format date string to relative time display (e.g., "Today", "Tomorrow", "In 3 Days")
 * @param dateString - ISO date string or undefined
 * @returns Formatted relative date string
 */
export const formatRelativeDate = (dateString: string | undefined): string => {
  if (!dateString) return 'TBD'

  const date = new Date(dateString)
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Tomorrow'
  if (diffDays > 1) return `In ${diffDays} Days`
  if (diffDays === -1) return 'Yesterday'
  if (diffDays < -1) return `${Math.abs(diffDays)} Days Ago`

  return date.toLocaleDateString()
}
