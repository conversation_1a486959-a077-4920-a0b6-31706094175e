export function getFullAddr(
  addr?: {
    state?: string | null
    city?: string | null
    zipCode?: string | null
    address?: string | null
    streetAddress?: string | null
  } | null,
  options?: {
    showZip?: boolean
  }
) {
  if (!addr) return ''
  const sb: string[] = []
  if (addr.address) {
    sb.push(addr.address + ',')
  } else if (addr.streetAddress) {
    sb.push(addr.streetAddress + ',')
  }
  if (addr.city) sb.push(addr.city + ',')
  if (addr.state) sb.push(addr.state)
  if (options?.showZip !== false) {
    if (addr.zipCode) sb.push(addr.zipCode)
  }
  return sb.join(' ')
}
