/**
 * Validate US phone number
 * @param phoneNumber Phone number to validate
 * @returns true if valid US phone number
 */
export const validateUSPhoneNumber = (
  phoneNumber: string
): boolean | string => {
  if (!phoneNumber) {
    return 'Required'
  }
  if (!/^\(?\d{3}\)?\s?\d{3}-?\d{4}$/.test(phoneNumber.replace(/^\+1/, ''))) {
    return 'Invalid US phone number'
  }
  return true
}

export const validateUSPhoneNumberWithoutRequired = (
  phoneNumber: string
): boolean | string => {
  if (!phoneNumber) {
    return true
  }
  if (!/^\(?\d{3}\)?\s?\d{3}-?\d{4}$/.test(phoneNumber.replace(/^\+1/, ''))) {
    return 'Invalid US phone number'
  }
  return true
}
