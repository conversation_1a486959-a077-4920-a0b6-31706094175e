import React from 'react'
import { <PERSON>rollView, View } from 'react-native'

import { Head<PERSON> } from '@/components/property-owner/Header'
import { StatsGrid } from '@/components/property-owner/home/<USER>'
import { ActiveProjectCard } from '@/components/property-owner/projects/ActiveProjectCard'
import { RecentlyCompletedCard } from '@/components/property-owner/projects/RecentlyCompletedCard'
import { ViewAll } from '@/components/property-owner/ViewAll'

const stats = [
  {
    title: 'Active Projects',
    value: 7,
    icon: 'tools',
    trendText: '2 new this month',
    trendType: 'up' as const,
    trendColor: 'success' as const
  },
  {
    title: 'Completed',
    value: 24,
    icon: 'check',
    trendText: '3 last month',
    trendType: 'up' as const,
    trendColor: 'success' as const
  }
]

const activeProjects = [
  {
    title: 'Kitchen Renovation',
    address: '123 Main Street, Austin, TX',
    status: 'in-progress',
    startDate: 'Jul 10, 2023',
    endDate: 'Aug 28, 2023',
    budget: '$12,500',
    spent: '$8,250',
    manager: {
      initials: 'JD',
      name: '<PERSON>',
      company: 'Premier Property Management'
    },
    onDetails: () => {}
  }
]

const recentlyCompleted = [
  {
    id: 1,
    title: 'Exterior Painting',
    address: '123 Main Street, Austin, TX',
    status: 'completed',
    completedOn: 'Jul 05, 2023',
    duration: '28 days',
    budget: '$5,800',
    finalCost: '$5,650',
    manager: {
      initials: 'TD',
      name: 'Thomas Davis',
      company: 'Premier Property Management'
    },
    timeline: [
      {
        id: 1,
        date: 'Jun 08, 2023',
        label: 'Project Started',
        event: 'started'
      },
      {
        id: 2,
        date: 'Jun 15, 2023',
        label: 'Surface Preparation Completed',
        event: 'surface-prep'
      },
      {
        id: 3,
        date: 'Jun 28, 2023',
        label: 'Primary Painting Completed',
        event: 'painting'
      },
      {
        id: 4,
        date: 'Jul 05, 2023',
        label: 'Final Inspection Passed',
        event: 'inspection'
      }
    ],
    onReport: () => {},
    onDetails: () => {}
  }
]

export default function ApprovalHistoryScreen() {
  return (
    <ScrollView
      className="bg-gray-50"
      contentContainerStyle={{ paddingBottom: 24 }}
    >
      <Header
        title="Projects"
        actions={[
          { icon: 'view', onPress: () => {} },
          { icon: 'menu', onPress: () => {} }
        ]}
      />
      <View className="mt-2 px-3">
        <StatsGrid items={stats} />
        <View className="mb-2 mt-4 text-base font-bold">Active Projects</View>
        {activeProjects.map((item, idx) => (
          <ActiveProjectCard key={idx} {...item} />
        ))}
        <ViewAll
          title="Recently Completed"
          linkText="View All"
          onPress={() => {}}
        />
        {recentlyCompleted.map((item, idx) => (
          <RecentlyCompletedCard key={idx} {...item} />
        ))}
      </View>
    </ScrollView>
  )
}
