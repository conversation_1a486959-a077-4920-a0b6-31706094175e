import React, { useMemo } from 'react'
import { useState } from 'react'
import { ScrollView, View } from 'react-native'
import { useRequest } from 'ahooks'

import { AccountSettingsCard } from '@/components/property-owner/profile/AccountSettingsCard'
import { EditProfileModal } from '@/components/property-owner/profile/EditProfileModal'
import { Header } from '@/components/property-owner/profile/Header'
import { Personal } from '@/components/property-owner/profile/Personal'
import { PersonalInformation } from '@/components/property-owner/profile/PersonalInformationCard'
import { ActionSheetModal } from '@/components/property-owner/profile/PhotoActionSheetModal'
import { PortfolioStatsCard } from '@/components/property-owner/profile/PortfolioStatsCard'
import { SupportLegalCard } from '@/components/property-owner/profile/SupportLegalCard'
import { client } from '@/services/api'
import { useAuth } from '@/store'
import { Colors } from '@/theme/colors'
import { toTitleCase } from '@/utils/case'

export default function ProfileScreen() {
  const [editVisible, setEditVisible] = useState(false)
  const [photoSheetVisible, setPhotoSheetVisible] = useState(false)
  const { user } = useAuth()

  const { data: userData, refresh: refreshUserData } = useRequest(() => {
    return client.GET('/api/v1/property-owner/profile/{ownerId}', {
      params: {
        path: {
          ownerId: user?.userId ?? 0
        }
      }
    })
  })

  console.log(user, userData?.data?.data)

  // Memoize the profile data to prevent unnecessary re-renders
  const profileData = useMemo(
    () => ({
      name: user?.userName ?? '',
      phone: user?.phoneNumber ?? '',
      location: userData?.data?.data?.address ?? '',
      focus: userData?.data?.data?.investmentGoal ?? ''
    }),
    [
      user?.userName,
      user?.phoneNumber,
      userData?.data?.data?.address,
      userData?.data?.data?.investmentGoal
    ]
  )

  // eslint-disable-next-line unused-imports/no-unused-vars
  const handleChange = (field: string, value: string) => {
    // setProfile(prev => ({ ...prev, [field]: value }))
  }

  // eslint-disable-next-line unused-imports/no-unused-vars
  const handlePhotoAction = (action: 'choose' | 'take' | 'cancel') => {
    setPhotoSheetVisible(false)
    // TODO: camera and select photo
  }

  const handleRefresh = () => {
    // Refresh both user data and profile data
    refreshUserData()
  }

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <Header onEdit={() => setEditVisible(true)} />
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <Personal
          name={user?.userName ?? ''}
          email={user?.email ?? ''}
          role={toTitleCase(user?.role ?? '')}
          avatar={user?.avatar ?? ''}
          onChangePhoto={() => setPhotoSheetVisible(true)}
        />
        <PortfolioStatsCard />
        <PersonalInformation
          name={profileData.name}
          phone={profileData.phone}
          location={profileData.location}
          focus={profileData.focus}
        />
        <AccountSettingsCard />
        <SupportLegalCard />
      </ScrollView>
      <EditProfileModal
        visible={editVisible}
        name={profileData.name}
        phone={profileData.phone}
        location={profileData.location}
        focus={profileData.focus}
        onChange={handleChange}
        onClose={() => setEditVisible(false)}
        onCancel={() => setEditVisible(false)}
        onSave={() => setEditVisible(false)}
        onRefresh={handleRefresh}
      />
      <ActionSheetModal
        visible={photoSheetVisible}
        onClose={() => setPhotoSheetVisible(false)}
        onAction={handlePhotoAction}
      />
    </View>
  )
}
