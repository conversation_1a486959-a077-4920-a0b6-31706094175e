import React, { Fragment } from 'react'
import { ScrollView, Text, View } from 'react-native'
import { useRouter } from 'expo-router'

import { ActivityLogsCard } from '@/components/property-owner/home/<USER>'
import { DashboardHeader } from '@/components/property-owner/home/<USER>'
import { PendingApprovalCard } from '@/components/property-owner/home/<USER>'
import { RecentProjectCard } from '@/components/property-owner/home/<USER>'
import { VendorCostAnalyticsCard } from '@/components/property-owner/home/<USER>'
import { ViewAll } from '@/components/property-owner/ViewAll'
import { useDashboardStats } from '@/hooks/useDashboardStats'
import { usePendingApprovals, useRecentProjects } from '@/hooks/useProjects'
import { getFullAddr } from '@/utils/addr'
import { transformStatsToCardProps } from '@/utils/trendAnalysis'

const activityLogs = [
  {
    date: 'Today',
    iconType: 'message' as const,
    title: 'Property Manager Message',
    address: '123 Main Street, Austin, TX',
    content: '"Final inspection photos attached for your review"',
    contentIcon: 'mail' as const
  },
  {
    date: 'Yesterday',
    iconType: 'payment' as const,
    title: 'Payment Processed',
    address: '456 Oak Road, Denver, CO',
    content: '$2,450 to ABC Plumbing Services',
    contentIcon: 'check' as const
  },
  {
    date: 'Sep 20',
    iconType: 'photo' as const,
    title: 'New Photos Added',
    address: '789 Pine Avenue, Seattle, WA',
    content: '12 new renovation progress photos',
    contentIcon: 'photo' as const
  },
  {
    date: 'Sep 18',
    iconType: 'budget' as const,
    title: 'Budget Adjustment',
    address: '456 Oak Road, Denver, CO',
    content: 'Additional $350 approved for electrical work',
    contentIcon: 'exclamation' as const
  },
  {
    date: 'Sep 15',
    iconType: 'alert' as const,
    title: 'Inspection Alert',
    address: '650 Maple Drive, Portland, OR',
    content: 'City inspection scheduled for Sep 25',
    contentIcon: 'calendar' as const
  }
]

export default function OwnerHomeScreen() {
  const router = useRouter()

  // Fetch dashboard stats
  const {
    stats: rawStats,
    loading: statsLoading,
    error: statsError
  } = useDashboardStats()

  // Transform stats with computed trendType and trendColor
  const stats = transformStatsToCardProps(rawStats)

  const {
    projects: recentProjects,
    total: recentTotal,
    loading: recentLoading,
    error: recentError
  } = useRecentProjects()

  const {
    projects: pendingApprovals,
    total: pendingTotal,
    loading: pendingLoading,
    error: pendingError
  } = usePendingApprovals()
  // Transform API data to match RecentProjectCard props
  const transformedRecentProjects = recentProjects.map(project => {
    // Map API status to component status
    let mappedStatus: 'completed' | 'pending' | 'scheduled' = 'pending'

    // console.log('Original project status:', project.status)

    if (project.status === 'COMPLETED') {
      mappedStatus = 'completed'
    } else if (project.status === 'IN_PROGRESS') {
      mappedStatus = 'scheduled'
    } else if (
      project.status === 'SUBMITTED' ||
      project.status === 'PENDING_QUOTES'
    ) {
      mappedStatus = 'pending'
    } else {
      // For DRAFT, CANCELLED, undefined or any other status, default to pending
      mappedStatus = 'pending'
    }

    // console.log('Mapped status:', mappedStatus)

    return {
      type: project.projectType || 'Unknown',
      address: getFullAddr(project),
      date: project.createdTime
        ? new Date(project.createdTime).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        : 'Unknown',
      status: mappedStatus
    }
  })

  // Only show first 3 items for preview, rest will be shown in "View All"
  const previewRecentProjects = transformedRecentProjects.slice(0, 3)

  // Transform API data to match PendingApprovalCard props
  const transformedPendingApprovals = pendingApprovals.map(project => ({
    title: project.projectName || 'Unknown Project',
    address: getFullAddr(project),
    pmAmount: project.estimateBudget
      ? `$${project.estimateBudget.toLocaleString()}`
      : '$0',
    vendorAmount: project.budgetUsed
      ? `$${project.budgetUsed.toLocaleString()}`
      : '$0',
    requestedDate: project.createdTime
      ? new Date(project.createdTime).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      : 'Unknown',
    statusText: 'Needs Approval',
    projectId: project.projectId || undefined,
    onApprove: () => {
      console.log('Approval confirmed for', project.projectName)
    }
  }))

  // Only show first item for preview, rest will be shown in "View All"
  const previewPendingApprovals = transformedPendingApprovals.slice(0, 1)

  return (
    <Fragment>
      <ScrollView
        className="bg-gray-50"
        contentContainerStyle={{ paddingBottom: 80 }}
      >
        {statsLoading ? (
          // Show loading state for dashboard header
          <View className="h-48 animate-pulse rounded-lg bg-gray-200" />
        ) : statsError ? (
          // Show error state for dashboard header
          <View className="h-48 items-center justify-center rounded-lg border border-red-200 bg-red-50">
            <Text className="text-sm text-red-600">
              Failed to load dashboard stats
            </Text>
          </View>
        ) : (
          <DashboardHeader name="John" avatar="JD" stats={stats} />
        )}
        <View className="mt-2 px-3">
          <ViewAll
            title="Pending Approvals"
            linkText={pendingTotal > 1 ? 'View All' : undefined}
            onPress={() => router.push('/property-owner/approval-history')}
          />
          {pendingLoading ? (
            // Show loading state
            <View className="h-32 animate-pulse rounded-lg bg-gray-200" />
          ) : pendingError ? (
            // Show error state
            <View className="h-32 items-center justify-center rounded-lg border border-red-200 bg-red-50">
              <Text className="text-sm text-red-600">
                Failed to load pending approvals
              </Text>
            </View>
          ) : (
            previewPendingApprovals.map((item, idx) => (
              <PendingApprovalCard key={idx} {...item} />
            ))
          )}
        </View>
        <View className="mt-2 px-3">
          <ViewAll
            title="Recent Projects"
            linkText={recentTotal > 3 ? 'View All' : undefined}
            onPress={() => router.push('/property-owner/(tabs)/projects')}
          />
          <View className="gap-2">
            {recentLoading ? (
              // Show loading state or skeleton
              <View className="h-20 animate-pulse rounded-lg bg-gray-200" />
            ) : recentError ? (
              // Show error state
              <View className="h-20 items-center justify-center rounded-lg border border-red-200 bg-red-50">
                <Text className="text-sm text-red-600">
                  Failed to load projects
                </Text>
              </View>
            ) : (
              previewRecentProjects.map((item, idx) => (
                <RecentProjectCard key={idx} {...item} />
              ))
            )}
          </View>
        </View>
        <View className="mt-2 px-3">
          <ViewAll
            title="Vendor Cost Analytics"
            linkText="View Details"
            onPress={() => router.push('/property-owner/(tabs)/reports')}
          />
          <VendorCostAnalyticsCard />
        </View>
        <View className="mt-2 px-3">
          <ViewAll title="Activity Log" />
          <ActivityLogsCard items={activityLogs} />
        </View>
      </ScrollView>
    </Fragment>
  )
}
