import React, { useMemo, useState } from 'react'
import { ScrollView, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useRequest } from 'ahooks'

import { StatsGrid } from '@/components/property-owner/home/<USER>'
import { ActiveProjectCard } from '@/components/property-owner/projects/ActiveProjectCard'
import { FilterBar } from '@/components/property-owner/projects/FilterBar'
import { Header } from '@/components/property-owner/projects/Header'
import { RecentlyCompletedCard } from '@/components/property-owner/projects/RecentlyCompletedCard'
import { ViewAll } from '@/components/property-owner/ViewAll'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { getFullAddr } from '@/utils/addr'

export default function ProjectsScreen() {
  const [showFilter, setShowFilter] = useState(false)
  const [filters, setFilters] = useState({
    projectType: 'all',
    projectStatus: 'all',
    manager: 'all',
    budgetRange: [0, 30000] as [number, number],
    sortBy: 'date'
  })

  // 获取项目统计数据
  const statsRequest = useRequest(
    async () => {
      const { data } = await client.GET('/api/v1/property-owner/project/stat')
      return data?.data
    },
    { manual: false }
  )

  // 获取项目列表
  const request = useRequest(
    async (page, pageSize) => {
      const { projectType, projectStatus, budgetRange, sortBy } = filters
      const params: components['schemas']['ProjectInfoVO'] = {
        pageNum: page,
        pageSize,
        projectStatus:
          projectStatus !== 'all'
            ? (projectStatus as components['schemas']['ProjectInfoVO']['projectStatus'])
            : undefined,
        projectTypeList: projectType !== 'all' ? [projectType] : undefined,
        budgetRangeMin: budgetRange[0],
        budgetRangeMax: budgetRange[1],
        dateRangeStart: undefined,
        dateRangeEnd: undefined,
        sortBy
      }
      const { data } = await client.POST(
        '/api/v1/property-owner/project/list',
        {
          body: params
        }
      )
      return {
        data: data?.data?.list ?? [],
        total: data?.data?.total || 0,
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { refreshDeps: [filters] }
  )

  const {
    data: projectsData,
    loading: projectsLoading,
    error: projectsError
  } = request
  const activeProjects =
    projectsData?.data?.filter(
      (p: components['schemas']['ProjectInfoDTO']) => p.status !== 'COMPLETED'
    ) || []
  const completedProjects =
    projectsData?.data?.filter(
      (p: components['schemas']['ProjectInfoDTO']) => p.status === 'COMPLETED'
    ) || []

  // 转换统计数据为组件需要的格式
  const stats = useMemo(() => {
    if (!statsRequest.data) return []

    return [
      {
        title: 'Active Projects',
        value: Number(statsRequest.data.activeProjects) || 0,
        icon: 'tools',
        trendText: `${Number(statsRequest.data.newThisMonth) || 0} new this month`,
        trendType: 'up' as const,
        trendColor: 'success' as const
      },
      {
        title: 'Completed',
        value: Number(statsRequest.data.completedProjects) || 0,
        icon: 'checkcircle',
        trendText: `${Number(statsRequest.data.completedLastMonth) || 0} last month`,
        trendType: 'up' as const,
        trendColor: 'success' as const
      }
    ]
  }, [statsRequest.data])

  // Transform API data to match ActiveProjectCard props
  const transformedActiveProjects = activeProjects.map(
    (project: components['schemas']['ProjectInfoDTO']) => ({
      id: project.projectId || 0,
      title: project.projectName || 'Unknown Project',
      address: getFullAddr(project),
      status: project.status,
      startDate: project.estimateStartDate
        ? new Date(project.estimateStartDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : 'TBD',
      endDate: project.estimateCompleteDate
        ? new Date(project.estimateCompleteDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : 'TBD',
      budget: project.estimateBudget
        ? `$${project.estimateBudget.toLocaleString()}`
        : 'TBD',
      spent: project.budgetUsed
        ? `$${project.budgetUsed.toLocaleString()}`
        : '$0',
      manager: {
        initials: project.propertyManager?.name
          ? project.propertyManager.name
              .split(' ')
              .map((n: string) => n[0])
              .join('')
              .toUpperCase()
          : 'PM',
        name: project.propertyManager?.name || 'Property Manager',
        company: project.propertyManager?.companyName || 'Property Management',
        bgColor: 'bg-indigo-100',
        color: 'text-indigo-600'
      },
      statusText: project.status
    })
  )

  // Transform API data to match RecentlyCompletedCard props
  const transformedCompletedProjects = completedProjects.map(
    (project: components['schemas']['ProjectInfoDTO']) => {
      // Calculate duration based on start and end dates
      let duration = 'Unknown'
      if (project.startDate && project.endDate) {
        const start = new Date(project.startDate)
        const end = new Date(project.endDate)
        const diffTime = Math.abs(end.getTime() - start.getTime())
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        duration = `${diffDays} days`
      }

      // Create timeline from project timeline data
      const timeline =
        project.timeline?.map((item: any, index: number) => ({
          id: index + 1,
          date: item.createdTime
            ? new Date(item.createdTime).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })
            : 'Unknown',
          label: item.description || 'Project Event',
          event: item.activityType || 'event'
        })) || []

      return {
        id: project.projectId || 0,
        title: project.projectName || 'Unknown Project',
        address: getFullAddr(project),
        completedOn: project.endDate
          ? new Date(project.endDate).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })
          : 'Unknown',
        duration,
        budget: project.estimateBudget
          ? `$${project.estimateBudget.toLocaleString()}`
          : 'TBD',
        finalCost: project.budgetUsed
          ? `$${project.budgetUsed.toLocaleString()}`
          : 'TBD',
        manager: {
          initials: project.propertyManager?.name
            ? project.propertyManager.name
                .split(' ')
                .map((n: string) => n[0])
                .join('')
                .toUpperCase()
            : 'PM',
          name: project.propertyManager?.name || 'Property Manager',
          company:
            project.propertyManager?.companyName || 'Property Management',
          bgColor: 'bg-sky-100',
          color: 'text-sky-600'
        },
        timeline,
        onReport: () => {
          // TODO: Implement report functionality
        },
        onDetails: () => {
          // TODO: Implement details functionality
        }
      }
    }
  )

  return (
    <View style={{ flex: 1 }}>
      {/* 顶部Header和Filter按钮 */}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 16,
          paddingTop: 12,
          paddingBottom: 8
        }}
      >
        <Text style={{ fontSize: 22, fontWeight: 'bold' }}>My Projects</Text>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#F3F4F6',
            borderRadius: 12,
            paddingHorizontal: 14,
            height: 36
          }}
          onPress={() => setShowFilter(v => !v)}
          activeOpacity={0.7}
        >
          <Ionicons name="filter" size={18} color="#222" />
          <Text
            style={{
              marginLeft: 6,
              marginRight: 6,
              fontWeight: '600',
              color: '#222'
            }}
          >
            Filter
          </Text>
          <Ionicons
            name={showFilter ? 'chevron-up' : 'chevron-down'}
            size={16}
            color="#222"
          />
        </TouchableOpacity>
      </View>
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingBottom: 24 }}
      >
        <Header />
        <View className="mt-2 px-3">
          <StatsGrid items={stats} />
          <Text className="mb-2 mt-4 text-base font-bold">Active Projects</Text>
          {projectsLoading ? (
            // Show loading state
            <View className="space-y-2">
              {[1, 2, 3].map(i => (
                <View
                  key={i}
                  className="h-32 animate-pulse rounded-lg bg-gray-200"
                />
              ))}
            </View>
          ) : projectsError ? (
            // Show error state
            <View className="h-32 items-center justify-center rounded-lg border border-red-200 bg-red-50">
              <Text className="text-sm text-red-600">
                Failed to load projects
              </Text>
            </View>
          ) : (
            transformedActiveProjects.map((item, idx) => (
              <ActiveProjectCard key={idx} {...item} />
            ))
          )}
          <ViewAll title="Recently Completed" />
          {projectsLoading ? (
            // Show loading state
            <View className="space-y-2">
              {[1, 2].map(i => (
                <View
                  key={i}
                  className="h-48 animate-pulse rounded-lg bg-gray-200"
                />
              ))}
            </View>
          ) : projectsError ? (
            // Show error state
            <View className="h-48 items-center justify-center rounded-lg border border-red-200 bg-red-50">
              <Text className="text-sm text-red-600">
                Failed to load projects
              </Text>
            </View>
          ) : (
            transformedCompletedProjects.map((item, idx) => (
              <RecentlyCompletedCard key={idx} {...item} />
            ))
          )}
        </View>
      </ScrollView>
      {/* FilterBar浮层，始终在页面最顶层 */}
      {showFilter && (
        <FilterBar onClose={() => setShowFilter(false)} onApply={setFilters} />
      )}
    </View>
  )
}
