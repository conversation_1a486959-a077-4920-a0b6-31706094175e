import { Platform, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { Tabs } from 'expo-router'

import { Colors } from '@/theme/colors'

import '../../../global.css'

// NOTE: https://icons.expo.fyi/Index
const tabBarIcon =
  (name: string) =>
  // eslint-disable-next-line unused-imports/no-unused-vars
  ({ color, focused }: { color: string; focused: boolean }) => (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <FontAwesome6 name={name} size={22} color={color} />
    </View>
  )

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: '#6b7280',
        headerShown: false,
        tabBarShowLabel: true,
        tabBarLabelStyle: {
          fontSize: 14,
          marginTop: 2,
          marginBottom: 2,
          lineHeight: 16,
          textAlign: 'center'
        },
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
            height: 68,
            // paddingBottom: 8,
            paddingTop: 4
          },
          default: {
            height: 68,
            // paddingBottom: 8,
            paddingTop: 4
          }
        }),
        tabBarIconStyle: {
          marginTop: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: tabBarIcon('house')
        }}
      />
      <Tabs.Screen
        name="properties"
        options={{
          title: 'Properties',
          tabBarIcon: tabBarIcon('building')
        }}
      />
      {/* <Tabs.Screen
        name="property-details"
        options={{
          title: 'Property Details',
          tabBarIcon: tabBarIcon('building')
        }}
      /> */}
      <Tabs.Screen
        name="reports"
        options={{
          title: 'Reports',
          tabBarIcon: tabBarIcon('chart-line')
        }}
      />
      <Tabs.Screen
        name="projects"
        options={{
          title: 'Projects',
          tabBarIcon: tabBarIcon('screwdriver-wrench')
        }}
      />
      {/* <Tabs.Screen
        name="approvals"
        options={{
          title: 'Approvals',
          tabBarIcon: tabBarIcon('ShieldCheckIcon')
        }}
      /> */}
      {/* <Tabs.Screen
        name="messages"
        options={{
          title: 'Messages',
          tabBarIcon: tabBarIcon('chatbubble-sharp')
        }}
      /> */}
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: tabBarIcon('user')
        }}
      />
    </Tabs>
  )
}
