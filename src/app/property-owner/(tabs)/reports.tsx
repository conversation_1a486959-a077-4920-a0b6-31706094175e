import React from 'react'
import { ScrollView, Text, View } from 'react-native'

import { BudgetProgressCard } from '@/components/property-owner/reports/BudgetProgress'
import { CostSummaryCard } from '@/components/property-owner/reports/CostSummaryCard'
import { Header } from '@/components/property-owner/reports/Header'
import { ViewTabs } from '@/components/property-owner/ViewTabs'

const tabs = [{ label: 'Overview', value: 'overview' }]

const budgetProgress = [
  {
    title: 'Renovation Budget',
    actual: 37500,
    total: 50000,
    color: '#f59e0b'
  },
  {
    title: 'Maintenance Budget',
    actual: 27600,
    total: 30000,
    color: '#ef4444'
  },
  {
    title: 'Landscaping Budget',
    actual: 8400,
    total: 20000,
    color: '#22c55e'
  }
]

export default function ReportsScreen() {
  return (
    <ScrollView
      className="bg-gray-50"
      contentContainerStyle={{ paddingBottom: 24 }}
    >
      <Header
        title="Report Analytics"
        actions={[
          { icon: 'community', onPress: () => {} },
          { icon: 'menu', onPress: () => {} }
        ]}
      />
      <View>
        <ViewTabs options={tabs} value="overview" onChange={() => {}} />
        <View className="mx-2 mb-2 mt-[-2px] h-px bg-gray-200" />
      </View>
      <View className="px-3">
        <CostSummaryCard />
        <Text className="mb-2 mt-2 text-base font-bold">Budget Progress</Text>
        {budgetProgress.map(i => (
          <BudgetProgressCard key={i.title} {...i} />
        ))}
      </View>
    </ScrollView>
  )
}
