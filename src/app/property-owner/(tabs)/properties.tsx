import React, { useState } from 'react'
import { ScrollView, Text, TouchableOpacity, View } from 'react-native'

// import { XMarkIcon } from 'react-native-heroicons/outline'
import { Header } from '@/components/property-owner/properties/Header'
import { PropertyCard } from '@/components/property-owner/properties/PropertyCard'
import { ViewTabs } from '@/components/property-owner/ViewTabs'
import { useProperties } from '@/hooks/useProperties'
import { usePropertyStats } from '@/hooks/usePropertyStats'

export default function PropertiesScreen() {
  // 单选过滤器
  const [filter, setFilter] = useState<
    'all' | 'SINGLE_FAMILY' | 'MULTI_FAMILY'
  >('all')

  // Fetch property statistics for filter options
  const { stats, loading: statsLoading, error: statsError } = usePropertyStats()

  const filterOptions = [
    { label: `All (${stats?.propertyCount || 0})`, value: 'all' },
    {
      label: `Single Family (${stats?.singleFamilyCount || 0})`,
      value: 'SINGLE_FAMILY'
    },
    {
      label: `Multi-Family (${stats?.multiFamilyCount || 0})`,
      value: 'MULTI_FAMILY'
    }
  ]

  const tabs = [{ label: 'Properties', value: 'properties' }]

  // Fetch properties data，propertyType 由 filter 控制
  const { properties, loading, error } = useProperties({
    pageSize: 20,
    propertyType: filter === 'all' ? undefined : filter
  })

  // Transform API data to match PropertyCard props
  const transformedProperties = properties.map(property => {
    let mappedStatus: 'occupied' | 'vacant' | 'renovation' = 'vacant'
    if (property.status === 'OCCUPIED') mappedStatus = 'occupied'
    else if (property.status === 'VACANT') mappedStatus = 'vacant'
    else if (property.status === 'RENOVATION') mappedStatus = 'renovation'
    const typeParts = []
    if (property.propertyType) typeParts.push(property.propertyType)
    if (property.bedroomCount && property.bathroomCount)
      typeParts.push(
        `${property.bedroomCount} bed, ${property.bathroomCount} bath`
      )
    const metrics = []
    if (property.monthlyRent)
      metrics.push({
        value: `$${property.monthlyRent.toLocaleString()}`,
        label: 'Monthly Rent'
      })
    if (property.roi) metrics.push({ value: `${property.roi}%`, label: 'ROI' })
    if (property.propertyValue)
      metrics.push({
        value: `$${(property.propertyValue / 1000).toFixed(0)}K`,
        label: 'Value'
      })
    const addressParts = []
    if (property.streetAddress) addressParts.push(property.streetAddress)
    if (property.city) addressParts.push(property.city)
    if (property.state) addressParts.push(property.state)
    const address = addressParts.join(', ')
    return {
      id: property.propertyId || 0,
      image:
        property.mainPhoto ||
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
      address,
      type: typeParts.join(' · '),
      status: mappedStatus,
      metrics,
      manager: {
        name: property.propertyManager?.name || 'Unknown Manager',
        title: property.propertyManager?.jobTitle || 'Property Manager',
        avatar: property.propertyManager?.name?.charAt(0) || 'U'
      }
    }
  })

  const handleFilterChange = (val: string) => {
    setFilter(val as 'all' | 'SINGLE_FAMILY' | 'MULTI_FAMILY')
  }

  // const handleClearAll = () => setFilter('all')

  return (
    <ScrollView
      className="bg-gray-50"
      contentContainerStyle={{ paddingBottom: 24 }}
    >
      <Header
        title="My Properties"
        actions={[
          { icon: 'view', onPress: () => {} },
          { icon: 'settings', onPress: () => {} }
        ]}
      />
      <View>
        <ViewTabs options={tabs} value="properties" onChange={() => {}} />
        <View className="mx-2 mt-[-2px] h-px bg-gray-200" />
      </View>
      {statsLoading ? (
        <View className="mx-4 my-2 h-12 animate-pulse rounded-lg bg-gray-200" />
      ) : statsError ? (
        <View className="mx-4 my-2 h-12 items-center justify-center rounded-lg border border-red-200 bg-red-50">
          <Text className="text-sm text-red-600">
            Failed to load filter options
          </Text>
        </View>
      ) : (
        <View className="flex-row flex-wrap gap-2 px-4 py-2">
          {filterOptions.map(opt => {
            const isActive = filter === opt.value
            return (
              <TouchableOpacity
                key={opt.value}
                onPress={() => handleFilterChange(opt.value)}
                activeOpacity={0.85}
                className={`rounded-full border px-4 py-2 ${isActive ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 bg-white'} ${isActive ? '' : 'text-gray-700'}`}
                style={{ marginBottom: 4 }}
              >
                <Text
                  className={`text-sm font-medium ${isActive ? 'text-indigo-600' : 'text-gray-700'}`}
                >
                  {opt.label}
                </Text>
              </TouchableOpacity>
            )
          })}
        </View>
      )}
      {/* Active Filters */}
      {/* <View className="flex-row items-center justify-between bg-white px-4 py-2">
        <Text className="text-base font-bold">Active Filters:</Text>
        {filter !== 'all' && (
          <TouchableOpacity
            onPress={handleClearAll}
            className="flex-row items-center gap-1"
          >
            <XMarkIcon size={18} color="#6366F1" />
            <Text className="ml-1 font-semibold text-indigo-600">
              Clear All
            </Text>
          </TouchableOpacity>
        )}
      </View> */}
      {/* <View className="flex-row flex-wrap bg-white px-4 pb-2">
        {filter === 'all' ? (
          <View className="mb-2 mr-2 rounded-full bg-gray-100 px-3 py-1">
            <Text style={{ color: '#6B7280', fontSize: 13 }} className="py-1">
              All Properties
            </Text>
          </View>
        ) : (
          <View className="mb-2 mr-2 flex-row items-center rounded-full bg-indigo-50 px-3 py-1">
            <Text
              style={{ color: '#6366F1', fontSize: 13 }}
              className="mr-1 py-1 font-medium"
            >
              {filterOptions
                .find(opt => opt.value === filter)
                ?.label?.replace(/\s*\(.*\)/, '')}
            </Text>
            <TouchableOpacity
              onPress={handleClearAll}
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            >
              <XMarkIcon size={15} color="#6366F1" />
            </TouchableOpacity>
          </View>
        )}
      </View> */}
      <View className="mt-2 px-3">
        {loading ? (
          <View className="h-64 animate-pulse rounded-lg bg-gray-200" />
        ) : error ? (
          <View className="h-64 items-center justify-center rounded-lg border border-red-200 bg-red-50">
            <Text className="text-sm text-red-600">
              Failed to load properties
            </Text>
          </View>
        ) : (
          transformedProperties.map((item, idx) => (
            <PropertyCard key={idx} {...item} status={item.status} />
          ))
        )}
      </View>
    </ScrollView>
  )
}
