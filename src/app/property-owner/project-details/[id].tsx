import React from 'react'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import { Colors } from '@/theme/colors'
export default function ProjectDetailsScreen() {
  // eslint-disable-next-line unused-imports/no-unused-vars
  const { id } = useLocalSearchParams()

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={[]}
      backgroundColor={Colors.light}
      contentContainerClass="p-4 bg-white"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Project Details'
        }}
      />
      {/* TODO: */}
    </Screen>
  )
}
