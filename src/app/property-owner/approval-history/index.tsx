import React, { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'

import { ApprovalHistoryCard } from '@/components/property-owner/approvals/ApprovalHistoryCard'
import { ApprovalRequestCard } from '@/components/property-owner/approvals/ApprovalRequestCard'
import { Header } from '@/components/property-owner/approvals/Header'
import { Tabs } from '@/components/property-owner/approvals/Tabs'

// Mock data
const approvalRequests = [
  {
    title: 'HVAC Repair Quote',
    address: '789 Pine Avenue, Seattle, WA',
    pmAmount: '$680.00',
    vendorAmount: '$750.00',
    requestedDate: 'Jul 19, 2023',
    statusText: 'Needs Approval'
  },
  {
    title: 'Plumbing Fixture Replacement',
    address: '123 Main Street, Austin, TX',
    pmAmount: '$850.00',
    vendorAmount: '$920.00',
    requestedDate: 'Jul 20, 2023',
    statusText: 'Needs Approval'
  }
]

const approvalHistory: Array<{
  title: string
  address: string
  pmAmount: string
  vendorAmount: string
  approvedAmount: string
  date: string
  status: 'approved' | 'rejected'
}> = [
  {
    title: 'Roof Repair',
    address: '456 Oak Road, Denver, CO',
    pmAmount: '$1,250.00',
    vendorAmount: '$1,280.00',
    approvedAmount: '$1,280.00',
    date: 'Jul 15, 2023',
    status: 'approved'
  },
  {
    title: 'Kitchen Appliance Replacement',
    address: '789 Pine Avenue, Seattle, WA',
    pmAmount: '$2,400.00',
    vendorAmount: '$2,400.00',
    approvedAmount: '$2,400.00',
    date: 'Jul 10, 2023',
    status: 'approved'
  },
  {
    title: 'Hardwood Floor Refinishing',
    address: '123 Main Street, Austin, TX',
    pmAmount: '$3,500.00',
    vendorAmount: '$4,800.00',
    approvedAmount: '$0.00',
    date: 'Jul 5, 2023',
    status: 'rejected'
  }
]

export default function ApprovalHistoryScreen() {
  const [tab, setTab] = useState('all')

  // Filter data based on tab
  let requestList: typeof approvalRequests = []
  let historyList: typeof approvalHistory = []
  if (tab === 'all') {
    requestList = approvalRequests
    historyList = approvalHistory
  } else if (tab === 'pending') {
    requestList = approvalRequests
    historyList = []
  } else if (tab === 'approved') {
    requestList = []
    historyList = approvalHistory.filter(item => item.status === 'approved')
  } else if (tab === 'rejected') {
    requestList = []
    historyList = approvalHistory.filter(item => item.status === 'rejected')
  }

  return (
    <ScrollView
      className="bg-gray-50"
      contentContainerStyle={{ paddingBottom: 24 }}
    >
      <Header />
      <Tabs value={tab} onChange={setTab} />
      <View className="mt-2 px-3">
        {requestList.length > 0 && (
          <>
            <View className="mb-2 mt-2">
              <Text className="text-base font-bold text-gray-900">
                Approval Requests
              </Text>
            </View>
            {requestList.map((item, idx) => (
              <ApprovalRequestCard key={idx} {...item} />
            ))}
          </>
        )}
        {historyList.length > 0 && (
          <>
            <View className="mb-2 mt-4">
              <Text className="text-base font-bold text-gray-900">
                Approval History
              </Text>
            </View>
            {historyList.map((item, idx) => (
              <ApprovalHistoryCard key={idx} {...item} />
            ))}
          </>
        )}
      </View>
    </ScrollView>
  )
}
