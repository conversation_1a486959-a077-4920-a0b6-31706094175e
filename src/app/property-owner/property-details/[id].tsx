import React, { useState } from 'react'
import { View } from 'react-native'
import Animatable from 'react-native-animatable'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import { FinancialDetailsCard } from '@/components/property-owner/property-details/FinancialDetailsCard'
import { MaintenanceHistory } from '@/components/property-owner/property-details/MaintenanceHistory'
import { Manitenance } from '@/components/property-owner/property-details/Manitenance'
import { PropertyDetailsCard } from '@/components/property-owner/property-details/PropertyDetailsCard'
import { PropertyManagerCard } from '@/components/property-owner/property-details/PropertyManagerCard'
import { PropertyOverviewCard } from '@/components/property-owner/property-details/PropertyOverviewCard'
import { ScheduledMaintenance } from '@/components/property-owner/property-details/ScheduledMaintenanceCard'
import { Tabs } from '@/components/property-owner/property-details/Tabs'
import { Colors } from '@/theme/colors'

export default function PropertyDetailsScreen() {
  // eslint-disable-next-line unused-imports/no-unused-vars
  const { id } = useLocalSearchParams()
  const [tab, setTab] = useState('details')

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={[]}
      backgroundColor={Colors.light}
      contentContainerClass="p-4"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Project Details'
        }}
      />
      <PropertyOverviewCard />
      <Manitenance />
      <View>
        <Tabs value={tab} onChange={setTab} />
        <View className="mt-3" />
        <Animatable.View
          key={tab}
          animation="fadeIn"
          duration={300}
          useNativeDriver
        >
          {tab === 'details' ? (
            <>
              <PropertyDetailsCard />
              {/* <FinancialPerformance /> */}
              <FinancialDetailsCard />
              <PropertyManagerCard />
            </>
          ) : (
            <>
              <MaintenanceHistory
                records={[
                  {
                    title: 'HVAC Service',
                    date: 'Jun 12, 2023',
                    description:
                      'Annual maintenance service for HVAC system. Replaced air filter and cleaned condenser unit.',
                    cost: '$175.00'
                  },
                  {
                    title: 'Dishwasher Repair',
                    date: 'May 8, 2023',
                    description:
                      'Dishwasher not draining properly. Unclogged drain line and replaced pump assembly.',
                    cost: '$285.00'
                  },
                  {
                    title: 'Garage Door Repair',
                    date: 'Mar 22, 2023',
                    description:
                      'Garage door opener malfunctioning. Replaced motor and adjusted sensors.',
                    cost: '$210.00'
                  },
                  {
                    title: 'Plumbing Leak Repair',
                    date: 'Feb 15, 2023',
                    description:
                      'Water leak under kitchen sink. Replaced damaged pipes and water supply lines.',
                    cost: '$195.00'
                  }
                ]}
              />
              <View className="h-4" />
              <ScheduledMaintenance
                records={[
                  {
                    title: 'Lawn Care Service',
                    date: 'Jul 28, 2023',
                    description:
                      'Monthly landscaping maintenance including lawn mowing, trimming, and general cleanup.',
                    cost: '$120.00'
                  },
                  {
                    title: 'Exterior Paint Touch-up',
                    date: 'Aug 15, 2023',
                    description:
                      'Touch-up painting on exterior trim and front door. Weather sealing of exterior surfaces.',
                    cost: '$350.00'
                  }
                ]}
              />
            </>
          )}
        </Animatable.View>
      </View>
    </Screen>
  )
}
