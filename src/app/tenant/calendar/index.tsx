import { useState } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { Stack } from 'expo-router'

import { Screen } from '@/components'
import SectionTitle from '@/components/SectionTitle'
import CalendarView from '@/components/tenant/CalendarView'
import { Colors, ShadowStyles } from '@/theme/colors'
import classNames from '@/utils/classname'

const events = [
  {
    id: 'rent-payment',
    type: 'payment',
    month: 'Jun',
    day: 1,
    time: 'Auto',
    title: 'Monthly Rent Payment',
    details: '$1,450.00 via Auto-Pay',
    icon: 'dollar-sign',
    color: 'success'
  },
  {
    id: 'maintenance-visit',
    type: 'maintenance',
    month: 'May',
    day: 18,
    time: '9:00 AM',
    title: 'Plumbing Service Visit',
    details: 'Kitchen Faucet Repair',
    icon: 'wrench',
    color: 'warning'
  },
  {
    id: 'inspection',
    type: 'notice',
    month: 'May',
    day: 25,
    time: '2:00 PM',
    title: 'Property Inspection',
    details: '2:00 PM - 4:00 PM',
    icon: 'clock',
    color: 'info'
  },
  {
    id: 'pool-maintenance',
    type: 'notice',
    month: 'May',
    day: 22,
    time: 'All Day',
    title: 'Pool Maintenance',
    details: 'Pool closed for routine maintenance',
    icon: 'circle-info',
    color: 'info'
  }
]

export default function CalendarScreen() {
  const [view, setView] = useState<'month' | 'list'>('month')
  const isEmpty = false

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={['top']}
      contentContainerClass="p-4"
      backgroundColor={Colors.white}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title: 'Calendar'
        }}
      />
      <CalendarView view={view} onViewChange={setView} />
      {/* Upcoming Events Section Title & Add Button */}
      <SectionTitle title="Upcoming Events" className="mb-2" />
      {/* Event list or empty state */}
      {isEmpty ? (
        <View className="items-center py-12">
          <View className="mb-3 rounded-full bg-tenant-light p-4">
            <FontAwesome6 name="calendar" size={32} color={Colors['tenant']} />
          </View>
          <Text className="mb-1 text-lg font-semibold text-dark">
            No Upcoming Events
          </Text>
          <Text className="text-sm text-gray">
            Your calendar is clear for the selected period.
          </Text>
        </View>
      ) : (
        <>
          {events.map(event => {
            const textClass =
              event.type === 'payment'
                ? 'text-success'
                : event.type === 'maintenance'
                  ? 'text-warning'
                  : event.type === 'notice'
                    ? 'text-info'
                    : ''
            const bgClass =
              event.type === 'payment'
                ? 'bg-success-light'
                : event.type === 'maintenance'
                  ? 'bg-warning-light'
                  : event.type === 'notice'
                    ? 'bg-info-light'
                    : ''
            return (
              <TouchableOpacity
                key={event.id}
                className="mb-4 flex-row overflow-hidden rounded-xl bg-white"
                style={ShadowStyles.default}
                activeOpacity={0.8}
                onPress={() => {}}
              >
                <View
                  className={classNames(
                    'w-[70px] items-center justify-center py-3',
                    bgClass
                  )}
                >
                  <Text
                    className={classNames(
                      'mb-1 text-xs font-medium uppercase',
                      textClass
                    )}
                  >
                    {event.month}
                  </Text>
                  <Text
                    className={classNames(
                      'mb-1 text-xl font-bold leading-none',
                      textClass
                    )}
                  >
                    {event.day}
                  </Text>
                  <Text className={classNames('text-xs', textClass)}>
                    {event.time}
                  </Text>
                </View>
                <View className="flex-1 justify-center px-4 py-3">
                  <Text
                    className={
                      'mb-1 self-start rounded-full px-2 py-1 text-xs ' +
                      (event.type === 'payment'
                        ? 'bg-success-light text-success'
                        : event.type === 'maintenance'
                          ? 'bg-warning-light text-warning'
                          : event.type === 'notice'
                            ? 'bg-info-light text-info'
                            : '')
                    }
                  >
                    {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                  </Text>
                  <Text className="mb-1 text-base font-semibold">
                    {event.title}
                  </Text>
                  <View className="flex-row items-center">
                    <FontAwesome6
                      name={event.icon as string}
                      size={14}
                      color={Colors.gray}
                      solid
                      style={{ marginRight: 6 }}
                    />
                    <Text className="text-sm text-gray">{event.details}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            )
          })}
        </>
      )}
    </Screen>
  )
}
