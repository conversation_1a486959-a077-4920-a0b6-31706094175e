import { useEffect } from 'react'
import { Platform, Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { Tabs } from 'expo-router'

import { Button } from '@/components'
import LoadingScreen from '@/components/common/LoadingScreen'
import { HapticTab } from '@/components/HapticTab'
import TabBarBackground from '@/components/ui/TabBarBackground'
import { useAuth } from '@/store'
import { useMyProperty } from '@/store/myProperty'
import { Colors } from '@/theme/colors'

export default function TabLayout() {
  const { logout } = useAuth()
  const { ready, myProperty, getMyProperty } = useMyProperty()

  useEffect(() => {
    getMyProperty()
  }, [])

  if (!ready) {
    return <LoadingScreen />
  }
  if (!myProperty) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-6">
        <Text className="mb-4 text-center text-lg text-gray-700">
          Sorry, you currently have no property information.
        </Text>
        <Button
          className="mt-10"
          variant="warning"
          onPress={() => {
            logout()
          }}
        >
          Log out
        </Button>
      </View>
    )
  }
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute'
          },
          default: {}
        })
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          // @ts-expect-error
          tabBarIcon: ({ color }) => <FA name="house" size={20} color={color} />
        }}
      />
      <Tabs.Screen
        name="maintenance"
        options={{
          title: 'Maintenance',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="wrench" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="messages"
        options={{
          title: 'Messages',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="comments" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          // @ts-expect-error
          tabBarIcon: ({ color }) => <FA size={20} name="user" color={color} />
        }}
      />
    </Tabs>
  )
}
