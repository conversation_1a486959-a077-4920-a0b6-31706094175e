import { useMemo, useRef, useState } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack } from 'expo-router'

import type { InfiniteScrollRef } from '@/components'
import { Button, InfiniteScroll, RadioButtonGroup, Screen } from '@/components'
import RequestCard from '@/components/tenant/RequestCard'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'

type SearchParams = {
  projectStatus: string
}

type MaintenanceRequest = components['schemas']['TenantProjectSubmitDTO']

export default function MaintenanceScreen() {
  const { getDictItems } = useDict()
  const [selectedFilter, setSelectedFilter] = useState('all')
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)
  const projectStatusRequest = useRequest(() => getDictItems('PROJECT_STATUS'))
  const request = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/tenant/maintenance/list', {
        body: {
          pageNum: page,
          pageSize,
          ...(args?.projectStatus
            ? {
                projectStatus: args.projectStatus as
                  | 'DRAFT'
                  | 'SUBMITTED'
                  | 'PENDING_QUOTES'
                  | 'IN_PROGRESS'
                  | 'COMPLETED'
                  | 'CANCELLED'
              }
            : {})
        }
      })
      return {
        // @ts-ignore
        data: data?.data?.list ?? [],
        // @ts-ignore
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { manual: true, refreshDeps: [selectedFilter] }
  )

  const searchParams = useMemo<SearchParams>(() => {
    return {
      projectStatus: selectedFilter === 'all' ? '' : selectedFilter
    }
  }, [selectedFilter])

  // eslint-disable-next-line unused-imports/no-unused-vars
  const handleCancel = (req: MaintenanceRequest) => {
    // TODO: update logic
    // refresh list or mute status
  }
  const handleNewRequest = () => {
    router.push({ pathname: '/tenant/maintenance/create' })
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="flex-1"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Maintenance Requests'
        }}
      />
      <View className="flex-1 p-4">
        {/* Submit New Request Button */}
        <Button
          variant="tenant"
          className="mb-6"
          onPress={handleNewRequest}
          accessibilityLabel="Submit New Request"
          leftIcon="plus"
        >
          Submit New Request
        </Button>
        {/* Filters */}
        <RadioButtonGroup
          value={selectedFilter}
          className="mb-4 flex-grow-0"
          onChange={setSelectedFilter}
          items={[
            {
              label: 'All (12)',
              value: 'all',
              activeColor: Colors.white,
              activeBgColor: Colors.tenant
            },
            ...(projectStatusRequest.data?.map(item => ({
              label: item.label!,
              value: item.code!,
              activeColor: Colors.white,
              activeBgColor: Colors.tenant
            })) ?? [])
          ]}
        />
        {/* Request List or Empty State */}
        <InfiniteScroll<MaintenanceRequest, SearchParams>
          ref={infiniteScrollRef}
          // initialLoad={false}
          renderItem={req => (
            <RequestCard
              key={req.projectId}
              // @ts-expect-error
              request={req}
              onCancelRequest={() => handleCancel(req)}
            />
          )}
          requestArgs={searchParams}
          onRequest={request.runAsync}
          emptyText="No request found."
          className="flex-1"
          itemClassName="w-full"
          numColumns={1}
          pageSize={10}
        />
      </View>
    </Screen>
  )
}
