import { ScrollView, View } from 'react-native'

import SectionTitle from '@/components/SectionTitle'
import TenantDashboardHeader from '@/components/tenant/TenantDashboardHeader'
import TenantNotificationCard from '@/components/tenant/TenantNotificationCard'
import TenantPropertyCard from '@/components/tenant/TenantPropertyCard'
import TenantQuickActions from '@/components/tenant/TenantQuickActions'
import TenantUpcomingCard from '@/components/tenant/TenantUpcomingCard'

// Mock notifications data
const notifications = [
  {
    icon: 'wrench',
    type: 'maintenance' as const,
    title: 'Maintenance Update',
    text: 'Your request for kitchen faucet repair has been scheduled.',
    time: '2h ago'
  },
  {
    icon: 'bullhorn',
    type: 'announcement' as const,
    title: 'Building Announcement',
    text: 'Pool maintenance scheduled for next Monday.',
    time: '2d ago'
  }
]

export default function HomeScreen() {
  return (
    <ScrollView className="flex-1 bg-white" contentContainerClassName="pb-8">
      {/* Dashboard header with welcome and avatar */}
      <TenantDashboardHeader />
      <View className="px-5">
        {/* Quick action buttons */}
        <TenantQuickActions />
        {/* My Property section */}
        <SectionTitle title="My Property" />
        <TenantPropertyCard />
        {/* Upcoming section */}
        <SectionTitle
          title="Upcoming"
          linkText="See All"
          href="/+not-found"
          variant="tenant"
        />
        <TenantUpcomingCard />
        {/* Notifications section */}
        <SectionTitle
          title="Notifications"
          linkText="See All"
          href="/+not-found"
          variant="tenant"
        />
        {notifications.map((n, i) => (
          <TenantNotificationCard key={i} {...n} />
        ))}
      </View>
    </ScrollView>
  )
}
