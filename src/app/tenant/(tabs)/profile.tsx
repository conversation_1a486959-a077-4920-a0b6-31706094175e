import React, { useState } from 'react'
import { ScrollView, View } from 'react-native'
import { useRequest } from 'ahooks'
import { Toast } from 'toastify-react-native'

import { EditProfileModal } from '@/components/common/profile/EditProfileModal'
import { Version } from '@/components/profile/Version'
import { AccountSettingsCard } from '@/components/property-owner/profile/AccountSettingsCard'
import { Header } from '@/components/property-owner/profile/Header'
import { SupportLegalCard } from '@/components/property-owner/profile/SupportLegalCard'
import SectionTitle from '@/components/SectionTitle'
import { ProfileCard } from '@/components/tenant/ProfileCard'
import { client } from '@/services/api'
import { useAuth } from '@/store'
import { validateUSPhoneNumber } from '@/utils/validators'

type TenantProfile = {
  firstName?: string
  lastName?: string
  phoneNumber?: string
  birthday?: Date
}

export default function ProfileScreen() {
  const { refreshMe } = useAuth()
  const [editVisible, setEditVisible] = useState(false)
  const profileRequest = useRequest(async () => {
    const { data } = await client.GET('/api/v1/tenant/profile/my')
    return data?.data
  })
  const onUpdateProfile = async (values: TenantProfile) => {
    const birthdayStr = values.birthday
      ? values.birthday.toISOString()
      : undefined
    const { error } = await client.PUT('/api/v1/tenant/profile/personalInfo', {
      body: {
        ...values,
        birthday: birthdayStr
      }
    })
    if (!error) {
      Toast.success('Profile updated')
      profileRequest.mutate({
        personalInfo: {
          ...values,
          birthday: birthdayStr
        }
      })
      refreshMe()
    }
    return !error
  }

  const profile = profileRequest.data
  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <Header onEdit={() => setEditVisible(true)} />
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        {/* Tenant Profile Card */}
        {profileRequest.data && <ProfileCard profile={profileRequest.data} />}
        {/* Account Settings Section */}
        <SectionTitle title="Account Settings" />
        <AccountSettingsCard />
        {/* Support & Legal Section */}
        <SectionTitle title="Support & Legal" />
        <SupportLegalCard />
        <Version />
      </ScrollView>
      {/* Edit Profile Modal */}
      <EditProfileModal<TenantProfile>
        visible={editVisible}
        initialValues={{
          firstName: profile?.personalInfo?.firstName || '',
          lastName: profile?.personalInfo?.lastName || '',
          phoneNumber: profile?.personalInfo?.phoneNumber || '',
          birthday: profile?.personalInfo?.birthday
            ? new Date(profile.personalInfo.birthday)
            : undefined
        }}
        columns={[
          {
            label: 'First Name',
            name: 'firstName'
          },
          {
            label: 'Last Name',
            name: 'lastName'
          },
          {
            label: 'Phone Number',
            name: 'phoneNumber',
            type: 'phone',
            rules: { validate: v => validateUSPhoneNumber(v as string) }
          },
          {
            label: 'Date of Birth',
            name: 'birthday',
            type: 'date'
          }
        ]}
        onClose={() => setEditVisible(false)}
        onSave={onUpdateProfile}
      />
    </View>
  )
}
