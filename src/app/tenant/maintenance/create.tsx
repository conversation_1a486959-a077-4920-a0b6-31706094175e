import { useState } from 'react'
import { Text, View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { Button, Form, FormItem, Input, Screen, Select } from '@/components'
import { AddedItems } from '@/components/property-manager/projects/create/area/AddedItems'
import { AddEditItemModal } from '@/components/property-manager/projects/create/area/AddEditItemModal'
import { CommonItems } from '@/components/property-manager/projects/create/area/CommonItems'
import { PropertyAreas } from '@/components/property-manager/projects/create/area/PropertyAreas'
import type {
  AddedItem,
  SelectedArea
} from '@/components/property-manager/projects/create/types'
import SectionTitle from '@/components/SectionTitle'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useMyProperty } from '@/store/myProperty'
import { ShadowStyles } from '@/theme/colors'
import type { DictItemType } from '@/types'
import { getFullAddr } from '@/utils/addr'

type CreateForm = components['schemas']['TenantProjectSubmitDTO']

export default function MaintenanceCreateScreen() {
  const { myProperty } = useMyProperty()
  const form = Form.useForm<CreateForm>()
  const [selectedArea, setSelectedArea] = useState<SelectedArea | null>(null)
  const [addedItems, setAddedItems] = useState<AddedItem[]>([])

  const [isModalVisible, setModalVisible] = useState(false)
  const [editingItem, setEditingItem] = useState<Partial<AddedItem> | null>(
    null
  )

  const handleCommonItemAdd = (item: DictItemType) => {
    setEditingItem({ itemName: item.label })
    setModalVisible(true)
  }

  const handleCreateNewItem = () => {
    setEditingItem(null) // This will open a blank form
    setModalVisible(true)
  }

  const handleAddedItemEdit = (item: AddedItem) => {
    setEditingItem(item)
    setModalVisible(true)
  }

  const handleItemRemove = (item: AddedItem) => {
    setAddedItems(prev => prev.filter(i => item !== i))
  }

  const handleModalSubmit = (values: AddedItem) => {
    if (editingItem) {
      const index = addedItems.indexOf(editingItem as AddedItem)
      // Edit mode
      setAddedItems(prev =>
        prev.map((i, idx) => (index === idx ? { ...i, ...values } : i))
      )
    } else {
      // Add mode
      setAddedItems(prev => [
        ...prev,
        {
          ...values,
          ...selectedArea
        } as AddedItem
      ])
    }
    setModalVisible(false)
    setEditingItem(null)
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingItem(null)
  }

  const createRequest = useRequest(
    async (values: CreateForm) => {
      if (!addedItems.length) {
        Toast.error('No item added!')
        return
      }
      const { error } = await client.POST('/api/v1/tenant/maintenance/submit', {
        body: {
          ...values,
          ...addedItems[0]
        }
      })
      return !error
    },
    {
      manual: true,
      onSuccess(v) {
        if (v) {
          Toast.success('Maintenance request submitted!')
          router.back()
        }
      }
    }
  )

  return (
    <Screen preset="scroll" safeAreaEdges={['top']}>
      <Stack.Screen
        options={{
          headerShown: true,
          title: 'Submit New Request'
        }}
      />
      <Form<CreateForm>
        className="flex-1 rounded-default border border-border bg-white p-5"
        form={form}
        style={ShadowStyles.sm}
        onFinish={v => createRequest.runAsync(v)}
        initialValues={{ preferredContactMethod: 'app' }}
      >
        <SectionTitle title="Property Address" className="mb-2" />
        <View className="mb-5 rounded-default border border-tenant bg-tenant-light p-4">
          <Text className="mb-1 text-base font-semibold text-dark">
            {getFullAddr({
              address: myProperty!.streetAddress,
              city: myProperty!.city
            })}
          </Text>
          <Text className="text-sm text-gray">
            {getFullAddr({
              city: myProperty!.city,
              state: myProperty!.state,
              zipCode: myProperty!.zipCode
            })}
          </Text>
        </View>
        <FormItem<CreateForm>
          label="Request Title*"
          name="requestTitle"
          rules={{ required: { value: true, message: 'Title is required' } }}
        >
          <Input placeholder="Enter a brief title for your request" />
        </FormItem>

        {/* Property Areas */}
        <PropertyAreas
          selectedArea={selectedArea}
          setSelectedArea={setSelectedArea}
          onAreasFetched={v => {
            if (!selectedArea && v.length) {
              setSelectedArea({
                areaType: v[0]!.code!,
                areaName: v[0]!.label!
              })
            }
          }}
        />

        {/* Common Items */}
        {selectedArea && (
          <CommonItems
            showCreate={addedItems.length < 1}
            selectedArea={selectedArea}
            onItemAdd={handleCommonItemAdd}
            onNewItem={handleCreateNewItem}
          />
        )}

        {/* Added Items */}
        <AddedItems
          items={addedItems}
          onItemRemove={handleItemRemove}
          onItemEdit={handleAddedItemEdit}
        />

        {/* Add/Edit Modal */}
        <AddEditItemModal
          visible={isModalVisible}
          showBudget={false}
          initialValues={editingItem || undefined}
          onSubmit={handleModalSubmit}
          onCancel={handleModalCancel}
          // TODO: fix this
          isEdit={!!(editingItem && 'id' in editingItem)}
        />
        <FormItem<CreateForm>
          label="Access Instructions (Optional)"
          name="specialInstruction"
        >
          <Input
            multiline
            numberOfLines={5}
            placeholder="Any special instructions for accessing your unit"
            style={{ textAlignVertical: 'top' }}
          />
        </FormItem>
        <FormItem<CreateForm>
          label="Preferred Contact Method"
          name="preferredContactMethod"
          rules={{ required: { value: true, message: 'This is required' } }}
        >
          <Select dictType="CONTACT_METHOD" />
        </FormItem>
        <View className="mt-10 flex flex-row items-center gap-x-[10px]">
          <Button
            variant="cancel"
            leftIcon="xmark"
            className="flex-1"
            disabled={createRequest.loading}
            onPress={() => {
              router.back()
            }}
          >
            Cancel
          </Button>
          <Button
            variant="tenant"
            leftIcon="paper-plane"
            className="flex-1"
            loading={createRequest.loading}
            onPress={form.submit}
          >
            Submit Request
          </Button>
        </View>
      </Form>
    </Screen>
  )
}
