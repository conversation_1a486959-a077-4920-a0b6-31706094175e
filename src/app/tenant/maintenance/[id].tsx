import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { Stack } from 'expo-router'

import { Button, Screen } from '@/components'
import Alert from '@/components/Alert'
import SectionTitle from '@/components/SectionTitle'
import PhotoGallery from '@/components/tenant/PhotoGallery'
import Timeline from '@/components/tenant/Timeline'
import { Colors, ShadowStyles } from '@/theme/colors'

// Example data (replace with real data or props)
const request = {
  id: 'MR-2023-042',
  title: 'Leaking Kitchen Faucet',
  property: '123 Main Street, Unit 4B, Austin, TX 78701',
  submitted: 'April 15, 2023',
  priority: 'Medium',
  expectedVisit: 'April 18, 2023, 9:00 AM - 12:00 PM',
  assignedTo: '<PERSON> (Plumbing Services LLC)',
  area: {
    name: 'Kitchen',
    icon: 'utensils',
    description:
      'The kitchen faucet is leaking water at the base and causing water damage to the cabinet underneath. Water is dripping constantly, even when the faucet is turned off completely.',
    attributes: [
      { icon: 'triangle-exclamation', label: 'Water Damage' },
      { icon: 'droplet', label: 'Plumbing Issue' },
      { icon: 'clock', label: 'Continuous Leak' }
    ],
    photos: [
      'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      'https://images.unsplash.com/photo-1585909695284-32d2985ac9c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      'https://images.unsplash.com/photo-1603380680624-ca765a326dbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60'
    ]
  },
  timeline: [
    {
      icon: 'check',
      title: 'Technician Assigned',
      date: 'April 16, 2023 - 10:30 AM',
      description:
        'John Smith from Plumbing Services LLC has been assigned to your request. Expected visit on April 18, 2023.'
    },
    {
      icon: 'comment',
      title: 'Message from Property Manager',
      date: 'April 16, 2023 - 9:15 AM',
      description:
        '"We\'ve reviewed your request and will be sending a plumber. Please ensure access to your unit on April 18 between 9 AM and 12 PM."'
    },
    {
      icon: 'file-alt',
      title: 'Request Submitted',
      date: 'April 15, 2023 - 3:45 PM',
      description:
        'Maintenance request for leaking kitchen faucet submitted successfully.'
    }
  ]
}

export default function MaintenanceDetailScreen() {
  return (
    <Screen contentContainerClass="bg-white p-4" preset="scroll">
      <Stack.Screen
        options={{
          headerShown: true,
          title: 'Request Detail'
        }}
      />
      {/* Status Banner */}
      <View className="mb-5">
        <Alert
          type="info"
          leftIcon={
            <View className="flex h-10 w-10 flex-row items-center justify-center rounded-full bg-info">
              <FontAwesome6
                name="screwdriver-wrench"
                size={20}
                color={Colors.white}
              />
            </View>
          }
          title="In Progress"
          message="Your request is being worked on. A technician will visit on April 18, 2023."
        />
      </View>

      {/* Request Info Card */}
      <View
        className="mb-5 rounded-default bg-white p-5"
        style={ShadowStyles.default}
      >
        <Text className="mb-2 text-xs text-gray">Ticket #{request.id}</Text>
        <Text className="mb-3 text-lg font-semibold">{request.title}</Text>
        <View className="gap-2">
          <View className="flex-row">
            <Text className="w-32 text-gray">Property:</Text>
            <Text className="flex-1">{request.property}</Text>
          </View>
          <View className="flex-row">
            <Text className="w-32 text-gray">Submitted:</Text>
            <Text className="flex-1">{request.submitted}</Text>
          </View>
          <View className="flex-row">
            <Text className="w-32 text-gray">Priority:</Text>
            <Text className="flex-1">{request.priority}</Text>
          </View>
          <View className="flex-row">
            <Text className="w-32 text-gray">Expected Visit:</Text>
            <Text className="flex-1">{request.expectedVisit}</Text>
          </View>
          <View className="flex-row">
            <Text className="w-32 text-gray">Assigned To:</Text>
            <Text className="flex-1">{request.assignedTo}</Text>
          </View>
        </View>
      </View>

      {/* Property Area */}
      <SectionTitle title="Property Area" className="mb-2" />
      <View
        className="mb-5 overflow-hidden rounded-xl bg-white"
        style={ShadowStyles.default}
      >
        <View className="flex-row items-center bg-tenant-light px-4 py-3">
          <View className="mr-4 h-10 w-10 items-center justify-center rounded-lg bg-tenant">
            <FontAwesome6 name={request.area.icon} size={20} color="#fff" />
          </View>
          <Text className="text-base font-semibold text-tenant">
            {request.area.name}
          </Text>
        </View>
        <View className="px-4 py-3">
          <Text className="mb-3 text-sm leading-6">
            {request.area.description}
          </Text>
          <View className="mb-3 flex-row flex-wrap gap-[10px]">
            {request.area.attributes.map((attr, idx) => (
              <View
                key={idx}
                className="mb-2 mr-2 flex-row items-center rounded-full bg-light-gray px-3 py-1"
              >
                <FontAwesome6
                  name={attr.icon}
                  size={12}
                  color={Colors.gray}
                  solid
                  style={{ marginRight: 6 }}
                />
                <Text className="text-gray-500 text-xs">{attr.label}</Text>
              </View>
            ))}
          </View>
          <SectionTitle title="Photos" className="mb-2" />
          <PhotoGallery photos={request.area.photos} />
        </View>
      </View>

      {/* Activity Timeline */}
      <SectionTitle title="Activity Timeline" className="mb-2" />
      <Timeline items={request.timeline} />

      {/* Action Buttons */}
      <View className="mt-6 flex-row justify-center gap-4">
        <Button
          variant="tenant"
          className="flex-1"
          leftIcon="comment"
          leftIconProps={{ solid: true }}
        >
          Send Message
        </Button>
        <Button variant="cancel" className="flex-1" leftIcon="xmark">
          Cancel Request
        </Button>
      </View>
      {/* Bottom padding for navigation */}
      <View className="h-8" />
    </Screen>
  )
}
