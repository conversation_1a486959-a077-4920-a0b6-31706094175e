import { useEffect, useRef, useState } from 'react'
import { KeyboardProvider } from 'react-native-keyboard-controller'
import { ActionSheetProvider } from '@expo/react-native-action-sheet'
// FIXME: The current file is a CommonJS module whose imports will produce 'require' calls; however, the referenced file is an ECMAScript module and cannot be imported with 'require'. Consider writing a dynamic 'import("@react-navigation/native")' call instead.
// import { DarkTheme, DefaultTheme } from '@react-navigation/native'
import { useFonts } from 'expo-font'
import type { Href } from 'expo-router'
import { router, SplashScreen, Stack } from 'expo-router'
import { StatusBar } from 'expo-status-bar'
import ToastManager from 'toastify-react-native'

import { useColorScheme } from '@/hooks/useColorScheme'
import { initI18n } from '@/i18n'
import { useAuth } from '@/store'
import { customFontsToLoad } from '@/theme'
import { loadDateFnsLocale } from '@/utils/formatDate'
import { useThemeProvider } from '@/utils/useAppTheme'

import '../global.css'

import 'react-native-reanimated'
import '@/services/api'

const { DarkTheme, DefaultTheme } = require('@react-navigation/native')

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts(customFontsToLoad)
  const [isI18nInitialized, setIsI18nInitialized] = useState(false)
  const authReady = useAuth(state => state.ready)
  const userRole = useAuth(state => state.userRole)
  const user = useAuth(state => state.user)
  const initAuth = useAuth(state => state.init)
  const inited = useRef(false)
  const { ThemeProvider } = useThemeProvider()

  useEffect(() => {
    initAuth()
    initI18n()
      .then(() => setIsI18nInitialized(true))
      .then(() => loadDateFnsLocale())
  }, [initAuth])

  const loaded = fontsLoaded && isI18nInitialized && authReady
  const colorScheme = useColorScheme()

  useEffect(() => {
    if (fontError) throw fontError
  }, [fontError])

  useEffect(() => {
    if (loaded && !inited.current) {
      SplashScreen.hideAsync()
      inited.current = true

      if (!userRole || !user) {
        router.replace('/role-selection')
      } else {
        router.replace(`/${userRole}/(tabs)` as Href)
      }
    }
  }, [loaded, userRole, user])

  if (!loaded) {
    return null
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <KeyboardProvider>
        <ActionSheetProvider>
          <Stack>
            <Stack.Screen
              name="role-selection"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="auth" options={{ headerShown: false }} />
            <Stack.Screen
              name="vendor/(tabs)"
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="property-manager/(tabs)"
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="property-owner/(tabs)"
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="tenant/(tabs)"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="+not-found" />
          </Stack>
        </ActionSheetProvider>
      </KeyboardProvider>
      <StatusBar style="auto" />
      <ToastManager useModal={false} />
    </ThemeProvider>
  )
}
