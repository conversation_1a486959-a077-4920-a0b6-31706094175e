import React, { useState } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import { client } from '@/services/api'
import { getFullAddr } from '@/utils/addr'

import ProjectTab from './ProjectTab'
import PropertyDetailsTab from './PropertyDetailsTab'
import PropertyHero from './PropertyHero'
import QuickActions from './QuickActions'
import TabNavigation from './TabNavigation'

export default function PropertyDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>()
  const [activeTab, setActiveTab] = useState<'overview' | 'projects'>(
    'overview'
  )

  const { data: propertyDetail, run: refetchProperty } = useRequest(
    async () => {
      const res = await client.GET(`/api/v1/pm/property/{propertyId}`, {
        params: {
          path: {
            propertyId: Number(id)
          }
        }
      })
      if (res?.data?.code === 200) {
        return res.data.data
      }
      return null
    }
  )

  const property = {
    name: propertyDetail?.propertyName ?? '',
    photos: propertyDetail?.photos?.map(p => p.fileUrl!) || [],
    status: propertyDetail?.status ?? '',
    address: getFullAddr(propertyDetail)
  }

  const quickActions = [
    {
      // @ts-ignore
      count: propertyDetail?.projectList?.length ?? '-',
      unit: 'Projects'
    },
    // {
    //   count: '-',
    //   unit: 'Units'
    // },
    {
      count: propertyDetail?.sizeSqFt ?? '-',
      unit: 'Sq.Ft.'
    },
    {
      count: propertyDetail?.yearBuilt ?? '-',
      unit: 'Built'
    }
  ]

  return (
    <Screen preset="scroll" safeAreaEdges={[]} contentContainerClass="p-4">
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Property Details'
        }}
      />
      <PropertyHero {...property} />
      <View className="flex flex-row gap-2">
        {quickActions.map((item, index) => (
          <QuickActions key={index} {...item} />
        ))}
      </View>
      {/* <FinancialOverview /> */}
      <TabNavigation
        active={activeTab}
        onTab={tab => setActiveTab(tab as 'overview' | 'projects')}
      />
      {activeTab === 'overview' && (
        <PropertyDetailsTab
          data={propertyDetail!}
          onDataUpdate={() => refetchProperty()}
        />
      )}
      {activeTab === 'projects' && (
        // @ts-ignore
        <ProjectTab list={propertyDetail?.projectList || []} />
      )}
    </Screen>
  )
}
