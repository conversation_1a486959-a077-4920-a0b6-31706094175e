import React from 'react'
import { Dimensions, View } from 'react-native'
import { LineChart as ChartKitLineChart } from 'react-native-chart-kit'
import type { FC } from 'react'

import { Colors } from '@/theme/colors'

interface FinancialLineChartProps {
  incomeData: number[]
  expensesData: number[]
  months: string[]
  width?: number
  height?: number
}

const FinancialLineChart: FC<FinancialLineChartProps> = ({
  incomeData,
  expensesData,
  months,
  width,
  height = 200
}) => {
  const chartData = {
    labels: months,
    datasets: [
      {
        data: incomeData,
        // eslint-disable-next-line unused-imports/no-unused-vars
        color: (opacity = 1) => Colors.success,
        strokeWidth: 2,
        withDots: true,
        withShadow: true,
        fillShadowGradient: 'rgb(211,241,216)',
        fillShadowGradientOpacity: 0.7
      },
      {
        data: expensesData,
        // eslint-disable-next-line unused-imports/no-unused-vars
        color: (opacity = 1) => Colors.danger,
        strokeWidth: 2,
        withDots: true,
        withShadow: true,
        fillShadowGradient: 'rgb(197,206,181)',
        fillShadowGradientOpacity: 0.7
      }
    ],
    legend: ['Income', 'Expenses']
  }

  const chartConfig = {
    backgroundGradientFrom: Colors.light,
    backgroundGradientTo: Colors.light,
    decimalPlaces: 0,
    // eslint-disable-next-line unused-imports/no-unused-vars
    color: (opacity = 1) => `#64748b`,
    // eslint-disable-next-line unused-imports/no-unused-vars
    labelColor: (opacity = 1) => `#64748b`,
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#fff'
    },
    propsForBackgroundLines: {
      stroke: '#e5e7eb'
    },
    fillShadowGradient: Colors.success,
    fillShadowGradientOpacity: 0.15
  }

  const chartWidth = width || Dimensions.get('window').width - 32

  return (
    <View className="rounded-default bg-light" style={{ padding: 4 }}>
      <ChartKitLineChart
        data={chartData}
        width={chartWidth}
        height={height}
        chartConfig={chartConfig}
        bezier={false}
        withShadow={true}
        withInnerLines={true}
        withOuterLines={true}
        withDots={true}
        withVerticalLabels={true}
        withHorizontalLabels={true}
        fromZero
        style={{ borderRadius: 12, alignSelf: 'center' }}
        yAxisSuffix=""
        yAxisInterval={1}
        segments={5}
        yLabelsOffset={8}
        formatYLabel={v => `$${v}`}
      />
    </View>
  )
}

export default FinancialLineChart
