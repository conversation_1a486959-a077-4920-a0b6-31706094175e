import React from 'react'
import { Pressable, Text, View } from 'react-native'
import { router } from 'expo-router'

import { Button } from '@/components'
import type { components } from '@/services/api/schema'

const ProjectTab = ({
  list
}: {
  // @ts-ignore
  list: components['schemas']['PropertyInfo']['projectList']
}) => {
  console.log('list-----', list)
  return (
    <>
      <View>
        {list.length === 0 ? (
          <Text className="mb-5 flex justify-center">Empty Project</Text>
        ) : (
          // @ts-ignore
          list.map((item, idx) => (
            <Pressable
              key={idx}
              className="mb-5 rounded-default bg-white p-4 shadow-sm"
              onPress={() => {
                router.push(`/property-manager/project/${item.projectId}`)
              }}
            >
              <Text className="mb-3 text-base font-semibold">
                {item.projectName}
              </Text>
              <Text className="w-auto max-w-min rounded-full bg-success-light px-2 py-1 text-[10px] font-medium text-success">
                {item.status}
              </Text>
              <View className="flex-row gap-2">
                <Text>Due: {item.endDate}</Text>
                <Text>$</Text>
              </View>
            </Pressable>
          ))
        )}
        <View className="flex-row gap-2">
          <Button
            className="flex-1"
            onPress={() => {
              router.push('/property-manager/project/create')
            }}
          >
            Add Project
          </Button>
          <Button
            className="flex-1"
            onPress={() => {
              router.push('/property-manager/projects')
            }}
            variant="primary"
          >
            View All
          </Button>
        </View>
      </View>
    </>
  )
}

export default ProjectTab
