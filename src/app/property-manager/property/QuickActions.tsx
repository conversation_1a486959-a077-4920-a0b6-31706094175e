import React from 'react'
import { Pressable, Text, View } from 'react-native'

const QuickActions = ({
  count,
  unit
}: {
  count: number | string
  unit: string
}) => {
  return (
    <View className="mb-5 flex-1">
      <Pressable className="flex flex-col items-center gap-1 rounded-default bg-white p-4 shadow-sm">
        <Text className="mb-1 flex h-9 w-9 items-center justify-center rounded-default text-xl font-bold text-primary">
          {count}
        </Text>
        <Text className="text-center text-[10px] font-medium text-dark">
          {unit}
        </Text>
      </Pressable>
    </View>
  )
}

export default QuickActions
