import React from 'react'
import { Pressable, Text, View } from 'react-native'

import classNames from '@/utils/classname'

interface TabNavigationProps {
  active: string
  onTab: (tab: string) => void
}

const tabs = [
  { key: 'overview', label: 'Overview' },
  { key: 'projects', label: 'Projects' }
]

const TabNavigation = ({ active, onTab }: TabNavigationProps) => {
  return (
    <View className="mb-4 flex flex-row overflow-hidden rounded-default bg-white p-1 shadow-sm">
      {tabs.map(tab => (
        <Pressable
          key={tab.key}
          className={classNames(
            'flex-1 items-center py-2',
            active === tab.key && 'rounded-default bg-primary',
            active === tab.key ? 'text-white' : 'text-gray-500'
          )}
          onPress={() => onTab(tab.key)}
        >
          <Text
            className={classNames(
              'text-base font-medium',
              active === tab.key ? 'text-white' : 'text-gray-500'
            )}
          >
            {tab.label}
          </Text>
        </Pressable>
      ))}
    </View>
  )
}

export default TabNavigation
