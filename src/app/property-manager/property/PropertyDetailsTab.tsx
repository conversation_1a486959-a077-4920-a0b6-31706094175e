import React, { useState } from 'react'
import { Modal, Pressable, ScrollView, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Toast } from 'toastify-react-native'

import {
  AddPhotos,
  Button,
  DatePicker,
  Form,
  FormItem,
  Input,
  Select
} from '@/components'
import { PhoneNumberInput } from '@/components/common/PhoneNumberInput'
import { NumberInput } from '@/components/NumberInput'
import EditLocationModal from '@/components/property-manager/properties/EditLocationModal'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors, ShadowStyles } from '@/theme/colors'
import { formatDate } from '@/utils/formatDate'
import { getFullName } from '@/utils/user'
import { validateUSPhoneNumber } from '@/utils/validators'

const PropertyDetailsTab = ({
  data,
  onDataUpdate
}: {
  data: components['schemas']['PropertyInfoDTO']
  onDataUpdate?: () => void
}) => {
  console.log('detail tab', data)

  // Modal states
  const [showBasicInfoModal, setShowBasicInfoModal] = useState(false)
  const [showLocationModal, setShowLocationModal] = useState(false)
  const [showFeaturesModal, setShowFeaturesModal] = useState(false)
  const [showOwnerModal, setShowOwnerModal] = useState(false)
  const [showLeaseModal, setShowLeaseModal] = useState(false)
  const [showTenantFormModal, setShowTenantFormModal] = useState(false)
  const [editingTenant, setEditingTenant] = useState<any>(null)
  const [showTenantModal, setShowTenantModal] = useState(false)

  // Forms
  const basicInfoForm = Form.useForm()
  const locationForm = Form.useForm()
  const featuresForm = Form.useForm()
  const ownerForm = Form.useForm()
  const leaseForm = Form.useForm()
  const tenantForm = Form.useForm()

  // API requests
  const updateBasicInfoRequest = useRequest(
    async (values: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/updateBasicInfo',
        {
          body: {
            propertyId: data.propertyId,
            ...values
          }
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Basic information updated successfully!')
          setShowBasicInfoModal(false)
          onDataUpdate?.()
        }
      }
    }
  )

  const updateFeaturesRequest = useRequest(
    async (values: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/updateFeatures',
        {
          body: {
            propertyId: data.propertyId,
            ...values
          }
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Features updated successfully!')
          setShowFeaturesModal(false)
          onDataUpdate?.()
        }
      }
    }
  )

  const updateOwnerRequest = useRequest(
    async (values: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/{propertyId}/updateOwner',
        {
          params: { path: { propertyId: data.propertyId! } },
          body: values
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Owner information updated successfully!')
          setShowOwnerModal(false)
          onDataUpdate?.()
        }
      }
    }
  )

  const updateLeaseRequest = useRequest(
    async ({ leaseBeginDate, leaseEndDate, ...values }: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/updateLeaseInformation',
        {
          body: {
            propertyId: data.propertyId,
            beginDate: leaseBeginDate?.toISOString(),
            endDate: leaseEndDate?.toISOString(),
            ...values
          }
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Lease information updated successfully!')
          setShowLeaseModal(false)
          onDataUpdate?.()
        }
      }
    }
  )

  // Tenant management API requests
  const addTenantRequest = useRequest(
    async (tenantData: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/{propertyId}/addTenant',
        {
          params: { path: { propertyId: data.propertyId! } },
          body: tenantData
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Tenant added successfully!')
          onDataUpdate?.()
        }
      }
    }
  )

  const updateTenantRequest = useRequest(
    async (tenantData: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/{propertyId}/updateTenant',
        {
          params: { path: { propertyId: data.propertyId! } },
          body: tenantData
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Tenant updated successfully!')
          onDataUpdate?.()
        }
      }
    }
  )

  const deleteTenantRequest = useRequest(
    async (tenantData: any) => {
      const { error } = await client.POST(
        '/api/v1/pm/property/{propertyId}/deleteTenant',
        {
          params: { path: { propertyId: data.propertyId! } },
          body: tenantData
        }
      )
      return !error
    },
    {
      manual: true,
      onSuccess: v => {
        if (v) {
          Toast.success('Tenant removed successfully!')
          onDataUpdate?.()
        }
      }
    }
  )
  const basicInfo = [
    { label: 'Property Name', value: data?.propertyName },
    { label: 'Property Type', value: data?.propertyType },
    { label: 'Year Built', value: data?.yearBuilt },
    {
      label: 'Square Footage',
      value: data?.sizeSqFt ? data?.sizeSqFt + 'sq ft' : '-'
    },
    { label: 'Bedrooms', value: data?.bedroomCount },
    { label: 'Bathrooms', value: data?.bathroomCount }
  ]

  const locationInfo = [
    { label: 'Street Address', value: data?.streetAddress },
    { label: 'City', value: data?.city },
    { label: 'State', value: data?.state },
    { label: 'ZIP Code', value: data?.zipCode }
  ]

  const features = [
    { label: 'Description', value: data?.description },
    { label: 'Current Condition', value: data?.currentCondition },
    { label: 'Month Rent', value: '$' + data?.monthlyRent?.toLocaleString() },
    {
      label: 'Property Value',
      value: '$' + data?.propertyValue?.toLocaleString()
    }
  ]

  const owner = [
    {
      label: 'User Name',
      value: getFullName(data?.propertyOwner)
    },
    { label: 'Owner Email', value: data?.propertyOwner?.email || '-' },
    { label: 'Owner Phone', value: data?.propertyOwner?.phoneNumber || '-' },
    { label: 'Role', value: data?.propertyOwner?.role || '-' },
    {
      label: 'Emergency Contact',
      value: data?.propertyOwner?.emergencyContact || '-'
    },
    { label: 'Owner Notes', value: data?.propertyOwner?.userNotes || '-' }
  ]

  const leaseInfo = [
    { label: 'Property Status', value: data?.status },
    {
      label: 'Lease Period',
      value:
        data?.leaseBeginDate && data?.leaseEndDate
          ? `${formatDate(data.leaseBeginDate)} to ${formatDate(data.leaseEndDate)}`
          : '-'
    },
    {
      label: 'Monthly Rent (Total)',
      value: data?.monthlyRent ? `$${data.monthlyRent}/month` : '-'
    },
    { label: 'Security Deposit (Total)', value: '-' },
    { label: 'Lease Notes', value: '-' }
  ]

  return (
    <>
      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold">
            Basic Property Information
          </Text>
          <Pressable
            onPress={() => {
              basicInfoForm.setValue('propertyName', data?.propertyName || '')
              basicInfoForm.setValue('propertyType', data?.propertyType || '')
              basicInfoForm.setValue('yearBuilt', data?.yearBuilt || '')
              basicInfoForm.setValue('sizeSqFt', data?.sizeSqFt || 0)
              basicInfoForm.setValue('bedroomCount', data?.bedroomCount || 0)
              basicInfoForm.setValue('bathroomCount', data?.bathroomCount || 0)
              basicInfoForm.setValue('photos', data?.photos || [])
              setShowBasicInfoModal(true)
            }}
          >
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>
        <View className="mb-4">
          {basicInfo.map((item, idx) => (
            <View
              key={idx}
              className="flex-row items-center justify-between border-b border-gray-200 py-3"
            >
              <Text className="text-gray-500 mb-1 text-xs">{item.label}</Text>
              <Text className="text-sm font-semibold text-dark">
                {item.value}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold">Location Information</Text>
          <Pressable
            onPress={() => {
              locationForm.setValue('streetAddress', data?.streetAddress || '')
              locationForm.setValue('city', data?.city || '')
              locationForm.setValue('state', data?.state || '')
              locationForm.setValue('zipCode', data?.zipCode || '')
              setShowLocationModal(true)
            }}
          >
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>
        <View className="mb-4">
          {locationInfo.map((item, idx) => (
            <View
              key={idx}
              className="flex-row items-center justify-between border-b border-gray-200 py-3"
            >
              <Text className="text-gray-500 mb-1 text-xs">{item.label}</Text>
              <Text className="text-sm font-semibold text-dark">
                {item.value}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold"> Property Features</Text>
          <Pressable
            onPress={() => {
              featuresForm.setValue('description', data?.description || '')
              featuresForm.setValue(
                'currentCondition',
                data?.currentCondition || ''
              )
              featuresForm.setValue('monthlyRent', data?.monthlyRent || 0)
              featuresForm.setValue('propertyValue', data?.propertyValue || 0)
              setShowFeaturesModal(true)
            }}
          >
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>
        <View className="mb-4">
          {features.map((item, idx) => (
            <View
              key={idx}
              className="flex-row items-center justify-between border-b border-gray-200 py-3"
            >
              <Text className="text-gray-500 mb-1 text-xs">{item.label}</Text>
              <Text className="text-sm font-semibold text-dark">
                {item.value}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold">Owner Information</Text>
          <Pressable
            onPress={() => {
              ownerForm.setValue(
                'firstName',
                data?.propertyOwner?.firstName || ''
              )
              ownerForm.setValue(
                'lastName',
                data?.propertyOwner?.lastName || ''
              )
              ownerForm.setValue('email', data?.propertyOwner?.email || '')
              ownerForm.setValue(
                'phoneNumber',
                data?.propertyOwner?.phoneNumber || ''
              )
              ownerForm.setValue('role', data?.propertyOwner?.role || '')
              ownerForm.setValue(
                'emergencyContact',
                data?.propertyOwner?.emergencyContact || ''
              )
              ownerForm.setValue(
                'userNotes',
                data?.propertyOwner?.userNotes || ''
              )
              setShowOwnerModal(true)
            }}
          >
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>
        <View className="mb-4">
          {owner.map((item, idx) => (
            <View
              key={idx}
              className="flex-row items-center justify-between border-b border-gray-200 py-3"
            >
              <Text className="text-gray-500 mb-1 text-xs">{item.label}</Text>
              <Text className="text-sm font-semibold text-dark">
                {item.value}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Lease Information Card */}
      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold">Lease Information</Text>
          <Pressable
            onPress={() => {
              leaseForm.setValue('propertyStatus', 'Occupied') // Default value
              leaseForm.setValue('monthlyRent', data?.monthlyRent || 0)
              leaseForm.setValue('leaseBeginDate', data?.leaseBeginDate || '')
              leaseForm.setValue('leaseEndDate', data?.leaseEndDate || '')
              leaseForm.setValue('securityDeposit', 0) // Default value
              leaseForm.setValue('leaseNotes', '') // Default value
              setShowLeaseModal(true)
            }}
          >
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>
        <View className="mb-4">
          {leaseInfo.map((item, idx) => (
            <View
              key={idx}
              className="flex-row items-center justify-between border-b border-gray-200 py-3"
            >
              <Text className="text-gray-500 mb-1 text-xs">{item.label}</Text>
              <Text className="text-sm font-semibold text-dark">
                {item.value}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Tenant Information Card */}
      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center justify-between gap-2">
          <Text className="text-base font-semibold">
            Tenant Information
            <Text className="text-gray-500 text-sm font-normal">
              {data?.tenantList?.length
                ? ` (${data.tenantList.length} tenants)`
                : ' (0 tenants)'}
            </Text>
          </Text>
          <Pressable onPress={() => setShowTenantModal(true)}>
            <FontAwesome6 name="edit" size={16} color={Colors.primary} />
          </Pressable>
        </View>

        {/* Tenants List */}
        <View className="mb-4">
          {data?.tenantList && data.tenantList.length > 0 ? (
            data.tenantList.map((tenant, index) => (
              <View
                key={index}
                className="bg-gray-50 mb-3 rounded-lg border border-gray-200 p-3"
              >
                <Text className="text-base font-medium text-dark">
                  {tenant.userName || 'N/A'}
                </Text>
                <Text className="text-sm text-gray">
                  {tenant.email || 'N/A'}
                </Text>
                <Text className="text-sm text-gray">
                  {tenant.phoneNumber || 'N/A'}
                </Text>
              </View>
            ))
          ) : (
            <Text className="py-4 text-center text-gray">No tenants found</Text>
          )}
        </View>
      </View>

      {/* Basic Info Edit Modal */}
      <Modal
        visible={showBasicInfoModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowBasicInfoModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                Edit Basic Information
              </Text>
              <Pressable
                onPress={() => setShowBasicInfoModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Form
                form={basicInfoForm}
                onFinish={values => updateBasicInfoRequest.run(values)}
              >
                <FormItem
                  name="propertyName"
                  label="Property Name"
                  rules={{
                    required: {
                      value: true,
                      message: 'Property name is required'
                    }
                  }}
                >
                  <Input placeholder="Enter property name" />
                </FormItem>

                <FormItem
                  name="propertyType"
                  label="Property Type"
                  rules={{
                    required: {
                      value: true,
                      message: 'Property type is required'
                    }
                  }}
                >
                  <Input placeholder="Enter property type" />
                </FormItem>

                <FormItem name="yearBuilt" label="Year Built">
                  <Input placeholder="Enter year built" />
                </FormItem>

                <FormItem name="sizeSqFt" label="Square Footage">
                  <NumberInput placeholder="0" min={0} />
                </FormItem>

                <View className="flex-row gap-3">
                  <FormItem
                    className="flex-1"
                    name="bedroomCount"
                    label="Bedrooms"
                  >
                    <NumberInput placeholder="0" min={0} />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="bathroomCount"
                    label="Bathrooms"
                  >
                    <NumberInput placeholder="0" min={0} />
                  </FormItem>
                </View>

                <FormItem name="photos" label="Property Photos">
                  <AddPhotos
                    title=""
                    description="Add photos to showcase the property"
                    maxCount={9}
                  />
                </FormItem>
              </Form>
            </View>
          </ScrollView>

          <View className="border-t border-gray-200 bg-white p-4">
            <Button
              variant="primary"
              onPress={() =>
                basicInfoForm.handleSubmit(values =>
                  updateBasicInfoRequest.run(values)
                )()
              }
              loading={updateBasicInfoRequest.loading}
              className="w-full"
            >
              <Text className="text-white">Update Basic Info</Text>
            </Button>
          </View>
        </View>
      </Modal>

      {/* Location Edit Modal */}
      {data?.propertyId && (
        <EditLocationModal
          form={locationForm}
          propertyId={data.propertyId}
          onUpdated={() => onDataUpdate?.()}
          visible={showLocationModal}
          onClose={() => setShowLocationModal(false)}
        />
      )}

      {/* Features Edit Modal */}
      <Modal
        visible={showFeaturesModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFeaturesModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                Edit Property Features
              </Text>
              <Pressable
                onPress={() => setShowFeaturesModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Form
                form={featuresForm}
                onFinish={values => updateFeaturesRequest.run(values)}
              >
                <FormItem name="description" label="Description">
                  <Input
                    multiline
                    numberOfLines={3}
                    placeholder="Enter property description"
                  />
                </FormItem>

                <FormItem name="currentCondition" label="Current Condition">
                  <Input placeholder="Enter current condition" />
                </FormItem>

                <View className="flex-row gap-3">
                  <FormItem
                    className="flex-1"
                    name="monthlyRent"
                    label="Monthly Rent"
                  >
                    <NumberInput placeholder="$0" min={0} />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="propertyValue"
                    label="Property Value"
                  >
                    <NumberInput placeholder="$0" min={0} />
                  </FormItem>
                </View>
              </Form>
            </View>
          </ScrollView>

          <View className="border-t border-gray-200 bg-white p-4">
            <Button
              variant="primary"
              onPress={() =>
                featuresForm.handleSubmit(values =>
                  updateFeaturesRequest.run(values)
                )()
              }
              loading={updateFeaturesRequest.loading}
              className="w-full"
            >
              <Text className="text-white">Update Features</Text>
            </Button>
          </View>
        </View>
      </Modal>

      {/* Owner Edit Modal */}
      <Modal
        visible={showOwnerModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowOwnerModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                Edit Owner Information
              </Text>
              <Pressable
                onPress={() => setShowOwnerModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Form
                form={ownerForm}
                onFinish={values => updateOwnerRequest.run(values)}
              >
                <View className="flex-row gap-3">
                  <FormItem
                    className="flex-1"
                    name="firstName"
                    label="First Name"
                    rules={{
                      required: {
                        value: true,
                        message: 'First name is required'
                      }
                    }}
                  >
                    <Input placeholder="Enter first name" />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="lastName"
                    label="Last Name"
                    rules={{
                      required: {
                        value: true,
                        message: 'Last name is required'
                      }
                    }}
                  >
                    <Input placeholder="Enter last name" />
                  </FormItem>
                </View>

                <FormItem name="email" label="Owner Email">
                  <Input placeholder="Enter email address" />
                </FormItem>

                <FormItem
                  name="phoneNumber"
                  label="Owner Phone"
                  rules={{
                    validate: validateUSPhoneNumber
                  }}
                >
                  <PhoneNumberInput placeholder="Enter phone number" />
                </FormItem>

                <FormItem name="role" label="Owner Type">
                  <Select
                    dictType="PROPERTY_OWNER_TYPE"
                    placeholder="Select owner type (e.g., LLC, Individual)"
                  />
                </FormItem>

                <FormItem name="emergencyContact" label="Mailing Address">
                  <Input
                    multiline
                    numberOfLines={2}
                    placeholder="Enter mailing address"
                  />
                </FormItem>

                <FormItem name="userNotes" label="Owner Notes">
                  <Input
                    multiline
                    numberOfLines={3}
                    placeholder="Enter notes about the owner"
                  />
                </FormItem>
              </Form>
            </View>
          </ScrollView>

          <View className="border-t border-gray-200 bg-white p-4">
            <Button
              variant="primary"
              onPress={() =>
                ownerForm.handleSubmit(values =>
                  updateOwnerRequest.run(values)
                )()
              }
              loading={updateOwnerRequest.loading}
              className="w-full"
            >
              <Text className="text-white">Update Owner Info</Text>
            </Button>
          </View>
        </View>
      </Modal>

      {/* Lease Information Modal */}
      <Modal
        visible={showLeaseModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowLeaseModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                Edit Lease Information
              </Text>
              <Pressable
                onPress={() => setShowLeaseModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Form form={leaseForm} onFinish={updateLeaseRequest.run}>
                <FormItem name="propertyStatus" label="Property Status">
                  <Select
                    dictType="PROPERTY_STATUS"
                    placeholder="Select property status"
                  />
                </FormItem>

                <FormItem name="leaseBeginDate" label="Lease Start Date">
                  <DatePicker placeholder="Select start date" />
                </FormItem>

                <FormItem name="leaseEndDate" label="Lease End Date">
                  <DatePicker placeholder="Select end date" />
                </FormItem>

                <FormItem name="monthlyRent" label="Monthly Rent (Total)">
                  <NumberInput placeholder="3500" min={0} />
                </FormItem>

                <FormItem
                  name="securityDeposit"
                  label="Security Deposit (Total)"
                >
                  <NumberInput placeholder="3500" min={0} />
                </FormItem>

                <FormItem name="leaseNotes" label="Lease Notes">
                  <Input
                    multiline
                    numberOfLines={3}
                    placeholder="Any special terms or conditions..."
                  />
                </FormItem>

                <Button
                  variant="primary"
                  loading={updateLeaseRequest.loading}
                  onPress={() => leaseForm.submit()}
                  className="mt-4"
                >
                  Update Lease Information
                </Button>
              </Form>
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Tenant Management Modal - Simplified View */}
      <Modal
        visible={showTenantModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTenantModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                Tenant Management
              </Text>
              <Pressable
                onPress={() => setShowTenantModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Text className="mb-4 text-base font-medium text-dark">
                Tenant Information
                <Text className="text-gray-500 text-sm font-normal">
                  {data?.tenantList?.length
                    ? ` (${data.tenantList.length} tenants)`
                    : ' (0 tenants)'}
                </Text>
              </Text>

              {data?.tenantList && data.tenantList.length > 0 ? (
                data.tenantList.map((tenant, index) => (
                  <View
                    key={index}
                    className="bg-gray-50 mb-3 rounded-lg border border-gray-200 p-3"
                  >
                    <View className="flex-row items-start justify-between">
                      <View className="flex-1">
                        <Text className="text-base font-medium text-dark">
                          {tenant.userName || 'N/A'}
                        </Text>
                        <Text className="text-sm text-gray">
                          {tenant.email || 'N/A'}
                        </Text>
                        <Text className="text-sm text-gray">
                          {tenant.phoneNumber || 'N/A'}
                        </Text>
                        {tenant.role && (
                          <Text className="text-sm text-gray">
                            Role: {tenant.role}
                          </Text>
                        )}
                      </View>
                      <View className="flex-row gap-2">
                        <Pressable
                          onPress={() => {
                            // Edit tenant functionality
                            setEditingTenant(tenant)
                            tenantForm.setValue(
                              'userName',
                              tenant.userName || ''
                            )
                            tenantForm.setValue('email', tenant.email || '')
                            tenantForm.setValue(
                              'phoneNumber',
                              tenant.phoneNumber || ''
                            )
                            tenantForm.setValue('role', tenant.role || '')
                            setShowTenantFormModal(true)
                          }}
                          className="rounded bg-blue-100 px-2 py-1"
                        >
                          <FontAwesome6
                            name="edit"
                            size={12}
                            color={Colors.primary}
                          />
                        </Pressable>
                        <Pressable
                          onPress={() => {
                            // Delete tenant functionality
                            deleteTenantRequest.run({
                              userId: tenant.userId
                            })
                          }}
                          className="rounded bg-red-100 px-2 py-1"
                        >
                          <FontAwesome6
                            name="trash"
                            size={12}
                            color="#ef4444"
                          />
                        </Pressable>
                      </View>
                    </View>
                  </View>
                ))
              ) : (
                <Text className="py-4 text-center text-gray">
                  No tenants found
                </Text>
              )}

              <Button
                variant="primary"
                onPress={() => {
                  // Add new tenant functionality - open form modal
                  setEditingTenant(null)
                  tenantForm.reset()
                  setShowTenantFormModal(true)
                }}
                className="mt-4"
              >
                Add New Tenant
              </Button>

              <Text className="mb-2 mt-4 text-sm text-gray">
                Note: This is a simplified tenant management interface. For
                detailed tenant creation with forms, use the property creation
                flow.
              </Text>
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Tenant Form Modal */}
      <Modal
        visible={showTenantFormModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTenantFormModal(false)}
      >
        <View className="bg-gray-50 flex-1">
          <View className="border-b border-gray-200 bg-white px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold text-dark">
                {editingTenant ? 'Edit Tenant' : 'Add New Tenant'}
              </Text>
              <Pressable
                onPress={() => setShowTenantFormModal(false)}
                className="rounded-full bg-gray-100 p-2"
              >
                <FontAwesome6 name="xmark" size={16} color={Colors.gray} />
              </Pressable>
            </View>
          </View>

          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
          >
            <View
              className="rounded-default border border-border bg-white p-4"
              style={ShadowStyles.sm}
            >
              <Form
                form={tenantForm}
                onFinish={values => {
                  if (editingTenant) {
                    updateTenantRequest.run({
                      userId: editingTenant.userId,
                      ...values
                    })
                  } else {
                    addTenantRequest.run(values)
                  }
                  setShowTenantFormModal(false)
                }}
              >
                <View className="flex-row gap-2">
                  <FormItem
                    className="flex-1"
                    name="firstName"
                    label="First Name"
                    rules={{
                      required: {
                        value: true,
                        message: 'First name is required'
                      }
                    }}
                  >
                    <Input placeholder="Enter first name" />
                  </FormItem>
                  <FormItem
                    className="flex-1"
                    name="lastName"
                    label="Last Name"
                    rules={{
                      required: {
                        value: true,
                        message: 'Last name is required'
                      }
                    }}
                  >
                    <Input placeholder="Enter last name" />
                  </FormItem>
                </View>

                <FormItem
                  name="email"
                  label="Email Address"
                  rules={{
                    required: { value: true, message: 'Email is required' }
                  }}
                >
                  <Input placeholder="<EMAIL>" />
                </FormItem>

                <FormItem
                  name="phoneNumber"
                  label="Phone Number"
                  rules={{
                    validate: validateUSPhoneNumber
                  }}
                >
                  <PhoneNumberInput />
                </FormItem>

                <View className="mt-4 flex-row gap-3">
                  <Button
                    variant="outline"
                    onPress={() => {
                      tenantForm.reset()
                      setShowTenantFormModal(false)
                    }}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onPress={() => tenantForm.submit()}
                    className="flex-1"
                    loading={
                      editingTenant
                        ? updateTenantRequest.loading
                        : addTenantRequest.loading
                    }
                  >
                    {editingTenant ? 'Update Tenant' : 'Add Tenant'}
                  </Button>
                </View>
              </Form>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  )
}

export default PropertyDetailsTab
