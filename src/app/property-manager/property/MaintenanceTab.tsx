import React from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

const maintenanceHistory = [
  {
    title: 'HVAC Service',
    date: 'Jun 12, 2023',
    details:
      'Annual maintenance service for HVAC system. Replaced air filter and cleaned condenser unit.',
    cost: '$175.00'
  },
  {
    title: 'Dishwasher Repair',
    date: 'May 8, 2023',
    details:
      'Dishwasher not draining properly. Unclogged drain line and replaced pump assembly.',
    cost: '$285.00'
  },
  {
    title: 'Garage Door Repair',
    date: 'Mar 22, 2023',
    details:
      'Garage door opener malfunctioning. Replaced motor and adjusted sensors.',
    cost: '$210.00'
  },
  {
    title: 'Plumbing Leak Repair',
    date: 'Feb 15, 2023',
    details:
      'Water leak under kitchen sink. Replaced damaged pipes and water supply lines.',
    cost: '$195.00'
  }
]

const scheduledMaintenance = [
  {
    title: 'Lawn Care Service',
    date: 'Jul 28, 2023',
    details:
      'Monthly landscaping maintenance including lawn mowing, trimming, and general cleanup.',
    cost: '$120.00',
    label: 'Estimated Cost:'
  },
  {
    title: 'Exterior Paint Touch-up',
    date: 'Aug 15, 2023',
    details:
      'Touch-up painting on exterior trim and front door. Weather sealing of exterior surfaces.',
    cost: '$350.00',
    label: 'Estimated Cost:'
  }
]

const MaintenanceTab = () => {
  return (
    <>
      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center gap-2">
          <FontAwesome6
            name="screwdriver-wrench"
            size={16}
            color={Colors.primary}
          />
          <Text className="text-base font-semibold">Maintenance History</Text>
        </View>
        <View className="flex flex-col gap-3">
          {maintenanceHistory.map((item, idx) => (
            <View key={idx} className="rounded-default bg-light p-3">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="font-medium">{item.title}</Text>
                <Text className="text-gray-500 text-xs">{item.date}</Text>
              </View>
              <Text className="mb-2 text-[13px] text-dark">{item.details}</Text>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-500 text-xs">Cost:</Text>
                <Text className="font-semibold">{item.cost}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
      <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
        <View className="mb-3 flex-row items-center gap-2">
          <FontAwesome6
            name="calendar-check"
            size={16}
            color={Colors.primary}
          />
          <Text className="text-base font-semibold">Scheduled Maintenance</Text>
        </View>
        <View className="flex flex-col gap-3">
          {scheduledMaintenance.map((item, idx) => (
            <View key={idx} className="rounded-default bg-light p-3">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="font-medium">{item.title}</Text>
                <Text className="text-gray-500 text-xs">{item.date}</Text>
              </View>
              <Text className="mb-2 text-[13px] text-dark">{item.details}</Text>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-500 text-xs">
                  {item.label || 'Estimated Cost:'}
                </Text>
                <Text className="font-semibold">{item.cost}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </>
  )
}

export default MaintenanceTab
