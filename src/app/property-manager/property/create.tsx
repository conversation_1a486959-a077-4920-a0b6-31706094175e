import { useEffect } from 'react'
import { BackHandler } from 'react-native'
import { Stack } from 'expo-router'

import { Screen } from '@/components'
import {
  PropertyProvider,
  useProperty
} from '@/components/properties/create/context'
import { Step1 } from '@/components/properties/create/steps/Step1'
import { Step2 } from '@/components/properties/create/steps/Step2'
import { Step3 } from '@/components/properties/create/steps/Step3'
import { Step4 } from '@/components/properties/create/steps/Step4'
import Steps from '@/components/Steps'
import { Colors } from '@/theme/colors'

function CreatePropertyScreen() {
  const { currentStep, setCurrentStep } = useProperty()

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <Step1 />
      case 1:
        return <Step2 />
      case 2:
        return <Step3 />
      case 3:
        return <Step4 />
      default:
        return null
    }
  }

  useEffect(() => {
    // Prevent back button on Android
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (currentStep > 1) {
          setCurrentStep(currentStep - 1)
          return true
        }
        return false
      }
    )

    return () => backHandler.remove()
  }, [])

  return (
    <Screen
      preset="scroll"
      backgroundColor={Colors.white}
      contentContainerClass="p-4 bg-white"
      safeAreaEdges={['top']}
      keyboardBottomOffset={0}
      ScrollViewProps={{
        showsVerticalScrollIndicator: false,
        contentContainerStyle: { paddingBottom: 20 }
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Add Property'
        }}
      />
      <Steps
        className="mb-6"
        current={currentStep}
        items={[
          { title: 'Property' },
          { title: 'Owner' },
          { title: 'Tenant' },
          { title: 'Review' }
        ]}
      />
      {renderStepContent()}
    </Screen>
  )
}

export default function CreatePropertyScreenWithProvider() {
  return (
    <PropertyProvider>
      <CreatePropertyScreen />
    </PropertyProvider>
  )
}
