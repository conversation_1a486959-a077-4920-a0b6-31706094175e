import React from 'react'
import { Text, View } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'

import PhotoSwiper from '@/components/PhotoSwiper'

interface PropertyHeroProps {
  photos: string[]
  name: string
  status: string
  address: string
}

const PropertyHero = ({ photos, status, address, name }: PropertyHeroProps) => {
  return (
    <View className="relative mb-5 overflow-hidden rounded-default">
      <PhotoSwiper
        photos={photos}
        imageClassName="h-[220px] w-full"
        imageHeight={220}
      />
      {/* Use LinearGradient for background gradient overlay */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)']}
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          paddingHorizontal: 16,
          paddingVertical: 16
        }}
      >
        <Text className="mb-1 text-2xl font-bold text-white opacity-90">
          {name}
        </Text>
        <Text className="mb-1 text-base text-white">{address}</Text>
        <View className="flex flex-row gap-2">
          <Text className="rounded-full bg-success-light px-2 py-1 text-[10px] font-medium text-success">
            {status}
          </Text>
        </View>
      </LinearGradient>
    </View>
  )
}

export default PropertyHero
