import React from 'react'
import { Dimensions, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Colors } from '@/theme/colors'

import FinancialLineChart from './FinancialLineChart'

const stats = [
  { value: '$2,450', label: 'Monthly Rent' },
  { value: '$320K', label: 'Property Value' },
  { value: '9.8%', label: 'Annual ROI' }
]

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
const incomeData = [2500, 2500, 2500, 2500, 2500, 2500]
const expensesData = [2000, 1900, 2100, 1950, 1850, 2000]

const screenWidth = Dimensions.get('window').width - 32

const FinancialOverview = () => {
  return (
    <View className="mb-5 rounded-default bg-white p-4 shadow-sm">
      <View className="mb-3 flex-row items-center gap-2">
        <FontAwesome6 name="dollar-sign" size={16} color={Colors.success} />
        <Text className="text-base font-semibold">Financial Performance</Text>
      </View>
      <View className="mb-4 flex-row gap-4">
        {stats.map((item, idx) => (
          <View
            key={idx}
            className="flex-1 items-center rounded-default bg-light p-3"
          >
            <Text className="mb-1 text-lg font-semibold">{item.value}</Text>
            <Text className="text-gray-500 text-xs">{item.label}</Text>
          </View>
        ))}
      </View>
      {/* Chart */}
      <View className="mb-4 mt-4">
        <FinancialLineChart
          incomeData={incomeData}
          expensesData={expensesData}
          months={months}
          width={screenWidth}
          height={200}
        />
      </View>
    </View>
  )
}

export default FinancialOverview
