import React from 'react'
import { Image, Pressable, ScrollView, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams, useRouter } from 'expo-router'
import type { FC } from 'react'

import { Button } from '@/components/Button'
import PhotoSwiper from '@/components/PhotoSwiper'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

interface QuoteDetailData {
  vendorId: number
  vendorName: string
  vendorSpecialty: string
  vendorAvatar: string
  vendorRating: number
  vendorReviewCount: number
  quoteAmount: number
  budgetDifference: string
  estimatedDuration: string
  status: string
  estimatedStartDate: string
  actualStartDate?: string
  estimatedFinishDate: string
  actualFinishDate?: string
  activityLog: Array<{
    id: string
    title: string
    description: string
    date: string
    icon: string
    type: 'success' | 'info' | 'warning' | 'primary'
  }>
  workPhotos: string[]
  documents: Array<{
    id: string
    name: string
    type: string
    size: string
    url: string
  }>
}

const QuoteDetailPage: FC = () => {
  const router = useRouter()
  const { quoteId, vendorId } = useLocalSearchParams<{
    quoteId: string
    vendorId: string
  }>()

  // API call to get quote detail (TODO: Implement when API is available)
  const {
    data: quoteResponse,
    loading,
    error
  } = useRequest(
    async () => {
      if (!quoteId || !vendorId) return null

      // TODO: Replace with actual API call when available
      // const { data } = await client.GET('/api/v1/pm/quotes/detail/{quoteId}', {
      //   params: {
      //     path: { quoteId: parseInt(quoteId) }
      //   }
      // })

      // For now, return null to use mock data
      return null
    },
    {
      refreshDeps: [quoteId, vendorId]
    }
  )

  // Mock data for development (remove when API is ready)
  const mockData: QuoteDetailData = {
    vendorId: parseInt(vendorId || '1'),
    vendorName: "John's Kitchen Solutions",
    vendorSpecialty: 'Kitchen Specialist',
    vendorAvatar: 'https://randomuser.me/api/portraits/men/44.jpg',
    vendorRating: 4.2,
    vendorReviewCount: 24,
    quoteAmount: 790,
    budgetDifference: '+5.3%',
    estimatedDuration: '3 days',
    status: 'in-progress',
    estimatedStartDate: 'Jul 3, 2023',
    actualStartDate: 'Jul 3, 2023',
    estimatedFinishDate: 'Jul 6, 2023',
    actualFinishDate: undefined,
    activityLog: [
      {
        id: '1',
        title: 'Work Started',
        description:
          'Vendor arrived on-site and began work on kitchen sink replacement',
        date: 'July 3, 2023 - 9:15 AM',
        icon: 'play',
        type: 'success'
      },
      {
        id: '2',
        title: 'Materials Delivered',
        description: 'All required materials and equipment delivered to site',
        date: 'July 2, 2023 - 2:30 PM',
        icon: 'truck',
        type: 'info'
      },
      {
        id: '3',
        title: 'Quote Approved',
        description: 'Property Manager approved the vendor quote',
        date: 'July 1, 2023 - 2:30 PM',
        icon: 'check',
        type: 'primary'
      },
      {
        id: '4',
        title: 'Quote Submitted',
        description:
          'Vendor submitted detailed quote for kitchen sink replacement',
        date: 'June 30, 2023 - 4:45 PM',
        icon: 'file-alt',
        type: 'info'
      },
      {
        id: '5',
        title: 'Vendor Assigned',
        description: "John's Kitchen Solutions assigned to this work item",
        date: 'June 29, 2023 - 1:15 PM',
        icon: 'user-plus',
        type: 'warning'
      }
    ],
    workPhotos: [
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1556909088-4d7b9e3f623d?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1560185893-a55cbc8c57e8?w=300&h=300&fit=crop'
    ],
    documents: [
      {
        id: '1',
        name: 'Final Invoice',
        type: 'PDF',
        size: '1.2 MB',
        url: 'invoice.pdf'
      },
      {
        id: '2',
        name: 'Detailed Quote & Materials List',
        type: 'PDF',
        size: '2.3 MB',
        url: 'quote.pdf'
      },
      {
        id: '3',
        name: 'Work Completion Certificate',
        type: 'PDF',
        size: '0.8 MB',
        url: 'certificate.pdf'
      }
    ]
  }

  const quoteData = quoteResponse || mockData

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-success-light text-success border-success'
      case 'in-progress':
        return 'bg-warning-light text-warning border-warning'
      case 'completed':
        return 'bg-success-light text-success border-success'
      case 'pending':
        return 'bg-gray-light text-gray border-gray'
      default:
        return 'bg-gray-light text-gray border-gray'
    }
  }

  const getActivityIconColor = (type: string) => {
    switch (type) {
      case 'success':
        return Colors.success
      case 'info':
        return Colors.info
      case 'warning':
        return Colors.warning
      case 'primary':
        return Colors.primary
      default:
        return Colors.gray
    }
  }

  const handleMessageVendor = () => {
    console.log('Opening message interface...')
  }

  const handleMarkComplete = () => {
    console.log('Mark this work as completed')
  }

  const handleViewDocument = (document: any) => {
    console.log('View document:', document.name)
  }

  const handleDownloadDocument = (document: any) => {
    console.log('Download document:', document.name)
  }

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Loading quote details...</Text>
      </View>
    )
  }

  if (error) {
    return (
      <View className="flex-1 items-center justify-center p-4">
        <Text className="text-center text-danger">
          Failed to load quote details. Please try again.
        </Text>
        <Button
          variant="primary"
          className="mt-4"
          onPress={() => router.back()}
        >
          Go Back
        </Button>
      </View>
    )
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Quote Details',
          headerBackTitle: 'Back'
        }}
      />

      <ScrollView className="flex-1 bg-white">
        {/* Vendor Header */}
        <View className="border-b border-border bg-white p-4">
          <View className="flex-row items-start gap-4">
            <Image
              source={{ uri: quoteData.vendorAvatar }}
              className="h-16 w-16 rounded-full"
            />
            <View className="flex-1">
              <Text className="text-lg font-bold text-dark">
                {quoteData.vendorName}
              </Text>
              <Text className="mb-2 text-sm text-gray">
                {quoteData.vendorSpecialty}
              </Text>
              <View className="flex-row items-center gap-2">
                <View className="flex-row">
                  {[1, 2, 3, 4, 5].map(star => (
                    <FontAwesome6
                      key={star}
                      name="star"
                      size={14}
                      color={
                        star <= Math.floor(quoteData.vendorRating)
                          ? Colors.warning
                          : Colors.light
                      }
                      solid={star <= Math.floor(quoteData.vendorRating)}
                    />
                  ))}
                </View>
                <Text className="text-sm text-gray">
                  {quoteData.vendorRating} ({quoteData.vendorReviewCount}{' '}
                  reviews)
                </Text>
              </View>
            </View>
            <View
              className={classNames(
                'rounded-full border px-3 py-1',
                getStatusBadgeStyle(quoteData.status)
              )}
            >
              <Text className="text-xs font-medium capitalize">
                {quoteData.status.replace('-', ' ')}
              </Text>
            </View>
          </View>

          {/* Quote Summary */}
          <View className="mt-4 flex-row justify-between border-t border-border pt-4">
            <View className="items-center">
              <Text className="text-xl font-bold text-dark">
                ${quoteData.quoteAmount}
              </Text>
              <Text className="text-xs text-gray">Quote Amount</Text>
            </View>
            <View className="items-center">
              <Text className="text-xl font-bold text-warning">
                {quoteData.budgetDifference}
              </Text>
              <Text className="text-xs text-gray">vs Budget</Text>
            </View>
            <View className="items-center">
              <Text className="text-xl font-bold text-info">
                {quoteData.estimatedDuration}
              </Text>
              <Text className="text-xs text-gray">Duration</Text>
            </View>
          </View>
        </View>

        {/* Project Dates */}
        <View className="border-b border-border bg-white p-4">
          <View className="mb-4 flex-row items-center gap-3">
            <FontAwesome6
              name="calendar-alt"
              size={20}
              color={Colors.primary}
            />
            <Text className="text-lg font-semibold text-dark">
              Project Dates
            </Text>
          </View>

          <View className="flex-row flex-wrap gap-3">
            <View className="bg-gray-50 w-[48%] rounded-lg p-3">
              <Text className="mb-1 text-xs text-gray">Estimated Start</Text>
              <Text className="text-sm font-medium text-dark">
                {quoteData.estimatedStartDate}
              </Text>
              <Text className="text-xs text-info">Planned</Text>
            </View>
            <View className="bg-gray-50 w-[48%] rounded-lg p-3">
              <Text className="mb-1 text-xs text-gray">Actual Start</Text>
              <Text className="text-sm font-medium text-success">
                {quoteData.actualStartDate || '--'}
              </Text>
              <Text className="text-xs text-success">
                {quoteData.actualStartDate ? 'On Time' : 'Pending'}
              </Text>
            </View>
            <View className="bg-gray-50 w-[48%] rounded-lg p-3">
              <Text className="mb-1 text-xs text-gray">Estimated Finish</Text>
              <Text className="text-sm font-medium text-dark">
                {quoteData.estimatedFinishDate}
              </Text>
              <Text className="text-xs text-info">Expected</Text>
            </View>
            <View className="bg-gray-50 w-[48%] rounded-lg p-3">
              <Text className="mb-1 text-xs text-gray">Actual Finish</Text>
              <Text className="text-sm font-medium text-gray">
                {quoteData.actualFinishDate || '--'}
              </Text>
              <Text className="text-xs text-gray">
                {quoteData.actualFinishDate ? 'Completed' : 'Pending'}
              </Text>
            </View>
          </View>
        </View>

        {/* Activity Log */}
        <View className="border-b border-border bg-white p-4">
          <View className="mb-4 flex-row items-center gap-3">
            <FontAwesome6 name="history" size={20} color={Colors.primary} />
            <Text className="text-lg font-semibold text-dark">
              Activity Log
            </Text>
          </View>

          <View className="gap-4">
            {quoteData.activityLog.map(activity => (
              <View key={activity.id} className="flex-row gap-3">
                <View
                  className="h-10 w-10 items-center justify-center rounded-full"
                  style={{
                    backgroundColor: `${getActivityIconColor(activity.type)}20`
                  }}
                >
                  <FontAwesome6
                    name={activity.icon as any}
                    size={16}
                    color={getActivityIconColor(activity.type)}
                  />
                </View>
                <View className="flex-1">
                  <Text className="text-sm font-medium text-dark">
                    {activity.title}
                  </Text>
                  <Text className="mt-1 text-xs text-gray">
                    {activity.description}
                  </Text>
                  <Text className="mt-1 text-xs text-gray">
                    {activity.date}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Work Photos */}
        {quoteData.workPhotos.length > 0 && (
          <View className="border-b border-border bg-white p-4">
            <View className="mb-4 flex-row items-center gap-3">
              <FontAwesome6 name="camera" size={20} color={Colors.primary} />
              <Text className="text-lg font-semibold text-dark">
                Work Photos
              </Text>
            </View>

            <PhotoSwiper
              photos={quoteData.workPhotos}
              imageClassName="h-48"
              imageHeight={192}
            />
          </View>
        )}

        {/* Documents & Invoices */}
        <View className="border-b border-border bg-white p-4">
          <View className="mb-4 flex-row items-center gap-3">
            <FontAwesome6
              name="file-invoice-dollar"
              size={20}
              color={Colors.primary}
            />
            <Text className="text-lg font-semibold text-dark">
              Documents & Invoices
            </Text>
          </View>

          <View className="gap-3">
            {quoteData.documents.map(document => (
              <View
                key={document.id}
                className="bg-gray-50 flex-row items-center gap-3 rounded-lg p-3"
              >
                <View className="h-10 w-10 items-center justify-center rounded-lg bg-primary-light">
                  <FontAwesome6
                    name={document.type === 'PDF' ? 'file-pdf' : 'file'}
                    size={16}
                    color={Colors.primary}
                  />
                </View>
                <View className="flex-1">
                  <Text className="text-sm font-medium text-dark">
                    {document.name}
                  </Text>
                  <Text className="text-xs text-gray">
                    {document.type} • {document.size}
                  </Text>
                </View>
                <View className="flex-row gap-2">
                  <Pressable
                    className="h-8 w-8 items-center justify-center rounded-full bg-info-light"
                    onPress={() => handleViewDocument(document)}
                  >
                    <FontAwesome6 name="eye" size={12} color={Colors.info} />
                  </Pressable>
                  <Pressable
                    className="h-8 w-8 items-center justify-center rounded-full bg-success-light"
                    onPress={() => handleDownloadDocument(document)}
                  >
                    <FontAwesome6
                      name="download"
                      size={12}
                      color={Colors.success}
                    />
                  </Pressable>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Bottom spacing for action buttons */}
        <View className="h-20" />
      </ScrollView>

      {/* Action Buttons */}
      <View className="absolute bottom-0 left-0 right-0 border-t border-border bg-white p-4">
        <View className="flex-row gap-3">
          <Button
            variant="outline"
            className="flex-1"
            leftIcon="comment"
            onPress={handleMessageVendor}
          >
            Message
          </Button>
          <Button
            variant="success"
            className="flex-1"
            leftIcon="check"
            onPress={handleMarkComplete}
          >
            Mark Complete
          </Button>
        </View>
      </View>
    </>
  )
}

export default QuoteDetailPage
