import React, { useState } from 'react'
import { ScrollView, Text, TextInput, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams, useRouter } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { Button } from '@/components/Button'
import { VendorCard } from '@/components/property-manager/projects/VendorCard'
import { client } from '@/services/api'
import { Colors } from '@/theme/colors'
import { confirm } from '@/utils/confirm'

// API Types - matching the API response structure
interface VendorData {
  createdBy?: number
  createdTime?: string
  updatedBy?: number
  updatedTime?: string
  isDeleted?: boolean
  vendorId?: number
  isProfileOpened?: boolean
  isTopRated?: boolean
  title?: string
  experienceYear?: number
  aboutMe?: string
  serviceTypes?: string
  businessName?: string
  serviceArea?: string
  licenseNumber?: string
  specialties?: string
  experienceLevel?: string
  licenseType?: string
  licenseExpiryDate?: string
  insuranceProvider?: string
  insurancePolicyNumber?: string
  insuranceCoverage?: string
  insuranceExpiryDate?: string
  receiveNewJobNotice?: boolean
  receiveBidUpdateNotice?: boolean
  receiveProjectStartNotice?: boolean
  receivePushNotice?: boolean
  receiveEmailNotice?: boolean
  receiveSmsNotice?: boolean
  userName?: string
  email?: string
  phoneNumber?: string
  birthday?: string
  sex?: string
  avatar?: string
}

interface AssignRequest {
  projectId: number
  vendorId: number
  itemIdList: number[]
}

export default function VendorAssignScreen() {
  const router = useRouter()
  const {
    projectId = '0',
    selectedItemIds = '',
    selectedCount = '0'
  } = useLocalSearchParams<{
    projectId: string
    selectedItemIds: string
    selectedCount: string
  }>()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVendors, setSelectedVendors] = useState<number[]>([])
  const [isAssigning, setIsAssigning] = useState(false)

  // Parse selected item IDs
  const selectedItemIdList = selectedItemIds
    ? selectedItemIds
        .split(',')
        .map(id => parseInt(id.trim()))
        .filter(id => !isNaN(id))
    : []

  // API request for vendors list with search
  const {
    data: vendorsResponse,
    loading: isLoading,
    error
  } = useRequest(
    async () => {
      // Use POST method for vendor list
      const { data } = await client.POST('/api/v1/pm/vendor/list' as any, {
        body: searchQuery ? { searchText: searchQuery } : {}
      })

      console.log('Vendors data:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch vendors')
      }

      return data
    },
    {
      refreshDeps: [searchQuery],
      debounceWait: 300 // Debounce search
    }
  )

  // Extract vendors array from response
  const vendors: VendorData[] = Array.isArray(vendorsResponse?.data?.list)
    ? vendorsResponse.data.list
    : []

  // Show toast function similar to HTML implementation
  const showToast = (
    message: string,
    type: 'success' | 'error' | 'info' = 'success'
  ) => {
    switch (type) {
      case 'success':
        Toast.success(message)
        break
      case 'error':
        Toast.error(message)
        break
      case 'info':
        Toast.info(message)
        break
    }
  }

  // Filter vendors based on search (already handled by API call)
  const filteredVendors = vendors

  // Handle vendor selection with feedback (multi-select)
  const handleVendorSelect = (vendorId: number) => {
    setSelectedVendors(prev => {
      if (prev.includes(vendorId)) {
        // Remove if already selected
        return prev.filter(id => id !== vendorId)
      } else {
        // Add to selection
        return [...prev, vendorId]
      }
    })
  }

  const handleAssignVendor = async () => {
    if (isAssigning) return

    if (selectedVendors.length === 0) {
      showToast('Please select at least one vendor', 'error')
      return
    }

    if (selectedItemIdList.length === 0) {
      showToast('No items to assign', 'error')
      return
    }

    const selectedVendorObjects = selectedVendors
      .map(id => vendors.find(v => v.vendorId === id))
      .filter(
        (vendor): vendor is VendorData =>
          vendor != null && vendor.vendorId != null
      )

    const vendorNames = selectedVendorObjects
      .map(v => v.userName || `Vendor ${v.vendorId}`)
      .join(', ')

    // Show confirmation dialog
    confirm(`Assign ${vendorNames} to ${selectedCount} selected items?`, () =>
      confirmAssignment(selectedVendorObjects)
    )
  }

  const confirmAssignment = async (vendorObjects: VendorData[]) => {
    setIsAssigning(true)

    try {
      // Show loading toast
      showToast('Assigning vendors...', 'info')

      // Prepare assignment requests
      const assignmentRequests: AssignRequest[] = vendorObjects.map(vendor => ({
        projectId: parseInt(projectId),
        vendorId: vendor.vendorId!,
        itemIdList: selectedItemIdList
      }))

      // Make API call using client
      const { data } = await client.POST('/api/v1/pm/item/assign', {
        body: assignmentRequests
      })

      console.log('Assignment result:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to assign vendors')
      }

      const vendorNames = vendorObjects
        .map(v => v.businessName || v.userName || `Vendor ${v.vendorId}`)
        .join(', ')

      // Show success toast
      showToast(
        `✅ ${vendorNames} assigned to ${selectedCount} items!`,
        'success'
      )

      // Go back after showing toast and trigger refresh
      setTimeout(() => {
        // Use replace to go back with refresh parameter
        router.replace({
          pathname: `/property-manager/project/${projectId}`,
          params: {
            refresh: Date.now().toString(), // Add timestamp to trigger refresh
            tab: 'items' // Ensure we're on the items tab
          }
        })
      }, 1000)
    } catch (error) {
      console.error('Assignment error:', error)
      showToast('Failed to assign vendors. Please try again.', 'error')
    } finally {
      setIsAssigning(false)
    }
  }

  return (
    <View className="flex-1 bg-gray-100">
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Assign Vendor'
        }}
      />
      <ScrollView className="flex-1 px-4 pt-4">
        {/* Selected Items Info */}
        <View
          className="mb-5 rounded-xl bg-white p-4"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3
          }}
        >
          <View className="mb-2 flex-row items-center gap-3">
            <FontAwesome6
              name="check-circle"
              size={20}
              color={Colors.success}
            />
            <View className="flex-1">
              <Text className="text-base font-semibold text-dark">
                {selectedCount} Items Selected
              </Text>
              <Text className="text-sm text-gray">
                Ready for vendor assignment
              </Text>
            </View>
          </View>
          <Text className="text-sm font-medium text-primary">
            Choose a vendor to assign to all selected items
          </Text>
        </View>

        {/* Search Section */}
        <View className="mb-5">
          <View className="mb-4">
            <Text className="mb-1 text-lg font-semibold text-dark">
              Find Vendors
            </Text>
            <Text className="text-sm text-gray">
              Search for qualified vendors to assign to your items
            </Text>
          </View>

          <View className="relative">
            <FontAwesome6
              name="magnifying-glass"
              size={14}
              color={Colors.gray}
              style={{
                position: 'absolute',
                left: 12,
                top: 12,
                zIndex: 1
              }}
            />
            <TextInput
              className="w-full rounded-lg border border-border bg-white py-3 pl-10 pr-4 text-sm"
              placeholder="Search by name, specialty, or location..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1
              }}
            />
          </View>
        </View>

        {/* Vendor List */}
        <View className="mb-20">
          {isLoading ? (
            <View className="py-8">
              <Text className="text-center text-gray">Loading vendors...</Text>
            </View>
          ) : error ? (
            <View className="py-8">
              <Text className="mb-2 text-center text-red-500">
                {error.message || 'Failed to load vendors'}
              </Text>
              <Button
                variant="outline"
                size="sm"
                onPress={() => setSearchQuery('')}
              >
                Retry
              </Button>
            </View>
          ) : filteredVendors.length > 0 ? (
            filteredVendors
              .filter(vendor => vendor.vendorId != null)
              .map(vendor => (
                <VendorCard
                  key={vendor.vendorId}
                  vendor={vendor}
                  mode="selection"
                  isSelected={selectedVendors.includes(vendor.vendorId!)}
                  onSelect={() => handleVendorSelect(vendor.vendorId!)}
                />
              ))
          ) : (
            <View className="py-8">
              <Text className="text-center text-gray">
                {searchQuery
                  ? 'No vendors found matching your search.'
                  : 'No vendors available.'}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View
        className="absolute bottom-0 left-0 right-0 bg-white p-4"
        style={{
          borderTopWidth: 1,
          borderTopColor: Colors.border,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 5
        }}
      >
        <View className="flex-row gap-3">
          <Button
            variant="outline"
            className="flex-1"
            onPress={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            className="flex-1"
            onPress={handleAssignVendor}
            leftIcon={isAssigning ? undefined : 'user-plus'}
            disabled={selectedVendors.length === 0 && !isAssigning}
          >
            {isAssigning
              ? 'Assigning...'
              : selectedVendors.length === 0
                ? 'Select Vendors First'
                : `Assign to ${selectedCount} Item${parseInt(selectedCount) > 1 ? 's' : ''}`}
          </Button>
        </View>
      </View>
    </View>
  )
}
