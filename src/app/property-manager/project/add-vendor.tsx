import React, { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams, useRouter } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { Screen } from '@/components'
import { Button } from '@/components/Button'
import { ItemInfoCard } from '@/components/property-manager/projects/addVendor/ItemInfoCard'
import { VendorCard } from '@/components/property-manager/projects/VendorCard'
import { Search } from '@/components/Search'
import { client } from '@/services/api'
import { confirm } from '@/utils/confirm'

// API Types - matching the API response structure
interface VendorData {
  createdBy?: number
  createdTime?: string
  updatedBy?: number
  updatedTime?: string
  isDeleted?: boolean
  vendorId?: number
  isProfileOpened?: boolean
  isTopRated?: boolean
  title?: string
  experienceYear?: number
  aboutMe?: string
  serviceTypes?: string
  businessName?: string
  serviceArea?: string
  licenseNumber?: string
  specialties?: string
  experienceLevel?: string
  licenseType?: string
  licenseExpiryDate?: string
  insuranceProvider?: string
  insurancePolicyNumber?: string
  insuranceCoverage?: string
  insuranceExpiryDate?: string
  receiveNewJobNotice?: boolean
  receiveBidUpdateNotice?: boolean
  receiveProjectStartNotice?: boolean
  receivePushNotice?: boolean
  receiveEmailNotice?: boolean
  receiveSmsNotice?: boolean
  userName?: string
  email?: string
  phoneNumber?: string
  birthday?: string
  sex?: string
  avatar?: string
}

interface AddVendorRequest {
  projectId: number
  vendorId: number
  itemIdList: number[]
}

export default function AddVendorScreen() {
  const router = useRouter()
  const {
    projectId = '0',
    itemId = '0',
    itemTitle = 'Unknown Item',
    itemBudget = '$0'
  } = useLocalSearchParams<{
    projectId: string
    itemId: string
    itemTitle: string
    itemBudget: string
  }>()

  const [searchQuery, setSearchQuery] = useState('')
  const [isAdding, setIsAdding] = useState(false)

  // API request for vendors list with search
  const {
    data: vendorsResponse,
    loading: isLoading,
    error,
    refresh
  } = useRequest(
    async () => {
      const { data } = await client.POST('/api/v1/pm/vendor/list' as any, {
        body: searchQuery ? { searchText: searchQuery } : {}
      })

      console.log('Vendors data:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch vendors')
      }

      return data
    },
    {
      refreshDeps: [searchQuery],
      debounceWait: 300
    }
  )

  // Extract vendors array from response
  const vendors: VendorData[] = Array.isArray(vendorsResponse?.data?.list)
    ? vendorsResponse.data.list
    : []

  // Filter vendors based on search (already handled by API call)
  const filteredVendors = vendors

  // Handle vendor profile view
  const handleViewProfile = (vendorId: number) => {
    console.log('View vendor profile:', vendorId)
    // TODO: Navigate to vendor profile page
    Toast.info('Vendor profile feature coming soon')
  }

  // Handle add vendor to item
  const handleAddVendor = async (vendorId: number, vendorName: string) => {
    if (isAdding) return

    // Show confirmation dialog
    confirm(
      `Add ${vendorName} to this item?\n\nThis will:\n- Send project details to the vendor\n- Request a quote\n- Add vendor to item tracking`,
      () => confirmAddVendor(vendorId, vendorName)
    )
  }

  const confirmAddVendor = async (vendorId: number, vendorName: string) => {
    setIsAdding(true)

    try {
      // Show loading toast
      Toast.info('Adding vendor to item...')

      // Prepare add vendor request
      const addVendorRequest: AddVendorRequest = {
        projectId: parseInt(projectId),
        vendorId: vendorId,
        itemIdList: [parseInt(itemId)]
      }

      // Make API call using client
      const { data } = await client.POST('/api/v1/pm/item/assign', {
        body: [addVendorRequest]
      })

      console.log('Add vendor result:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to add vendor')
      }

      // Show success toast
      Toast.success(`✅ ${vendorName} has been added to this item!`)

      // Go back after showing toast
      setTimeout(() => {
        router.back()
      }, 1500)
    } catch (error) {
      console.error('Add vendor error:', error)
      Toast.error('Failed to add vendor. Please try again.')
    } finally {
      setIsAdding(false)
    }
  }

  return (
    <Screen>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Add Vendor'
        }}
      />
      <ScrollView className="flex-1">
        <View className="px-4 pt-4">
          {/* Item Info Card */}
          <ItemInfoCard
            title={itemTitle}
            budget={itemBudget}
            property="Oakwood Apartments - Unit 101"
          />

          {/* Search Section */}
          <Search
            placeholder="Search by name, specialty, or location..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSearch={setSearchQuery}
            showFilter={false}
          />

          {/* Vendor List */}
          <View className="mb-20">
            {isLoading ? (
              <View className="py-8">
                <Text className="text-center text-gray">
                  Loading vendors...
                </Text>
              </View>
            ) : error ? (
              <View className="py-8">
                <Text className="mb-2 text-center text-red-500">
                  {error.message || 'Failed to load vendors'}
                </Text>
                <Button variant="outline" size="sm" onPress={refresh}>
                  Retry
                </Button>
              </View>
            ) : filteredVendors.length === 0 ? (
              <View className="py-8">
                <Text className="text-center text-gray">
                  No vendors found. Try adjusting your search.
                </Text>
              </View>
            ) : (
              filteredVendors
                .filter(vendor => vendor.vendorId != null)
                .map(vendor => (
                  <VendorCard
                    key={vendor.vendorId}
                    vendor={vendor}
                    mode="actions"
                    onViewProfile={() => handleViewProfile(vendor.vendorId!)}
                    onAddVendor={() =>
                      handleAddVendor(
                        vendor.vendorId!,
                        vendor.userName ||
                          vendor.businessName ||
                          'Unknown Vendor'
                      )
                    }
                    isAdding={isAdding}
                  />
                ))
            )}
          </View>
        </View>
      </ScrollView>
    </Screen>
  )
}
