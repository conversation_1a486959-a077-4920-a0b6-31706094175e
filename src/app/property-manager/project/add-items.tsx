import { Text, View } from 'react-native'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import { Colors } from '@/theme/colors'

export default function AddProjectItemsScreen() {
  const { project_type } = useLocalSearchParams<{ project_type: string }>()

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="p-4"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Add Project Items'
        }}
      />

      <View>
        <Text>Project Type: {project_type}</Text>
      </View>
    </Screen>
  )
}
