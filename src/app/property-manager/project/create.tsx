import { useEffect } from 'react'
import { BackHand<PERSON> } from 'react-native'
import { Stack, useFocusEffect } from 'expo-router'
import { useCallback } from 'react'

import { Screen } from '@/components'
import { useProjectCreateStore } from '@/store/projectCreate'
import { Step1 } from '@/components/property-manager/projects/create/steps/Step1'
import { Step2 } from '@/components/property-manager/projects/create/steps/Step2'
import { Step3 } from '@/components/property-manager/projects/create/steps/Step3'
import { Step4 } from '@/components/property-manager/projects/create/steps/Step4'
import Steps from '@/components/Steps'
import { Colors } from '@/theme/colors'

const StepTitle = {
  0: 'Select Project Type',
  1: 'Project Details',
  2: 'Add Items',
  3: 'Review Items'
}

export default function CreateProjectScreen() {
  const { currentStep, setCurrentStep, resetStore } = useProjectCreateStore()

  // Reset store when entering the page
  useFocusEffect(
    useCallback(() => {
      resetStore()
    }, [resetStore])
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <Step1 />
      case 1:
        return <Step2 />
      case 2:
        return <Step3 />
      case 3:
        return <Step4 />
      default:
        return null
    }
  }

  useEffect(() => {
    // Prevent back button on Android
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (currentStep > 1) {
          setCurrentStep(currentStep - 1)
          return true
        }
        return false
      }
    )

    return () => backHandler.remove()
  }, [currentStep, setCurrentStep])

  return (
    <Screen
      preset="scroll"
      backgroundColor={Colors.white}
      contentContainerClass="p-4 bg-white"
      safeAreaEdges={['top']}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: StepTitle[currentStep as keyof typeof StepTitle]
        }}
      />
      {currentStep !== 0 ? (
        <Steps
          className="mb-6"
          current={currentStep}
          items={[
            { title: 'Type' },
            { title: 'Details' },
            { title: 'Items' },
            { title: 'Review' }
          ]}
        />
      ) : null}
      {renderStepContent()}
    </Screen>
  )
}
