import { useEffect } from 'react'
import { BackHandler } from 'react-native'
import { Stack } from 'expo-router'

import { Screen } from '@/components'
import {
  ProjectProvider,
  useProject
} from '@/components/property-manager/projects/create/context'
import { Step1 } from '@/components/property-manager/projects/create/steps/Step1'
import { Step2 } from '@/components/property-manager/projects/create/steps/Step2'
import { Step3 } from '@/components/property-manager/projects/create/steps/Step3'
import { Step4 } from '@/components/property-manager/projects/create/steps/Step4'
import Steps from '@/components/Steps'
import { Colors } from '@/theme/colors'

const StepTitle = {
  0: 'Select Project Type',
  1: 'Project Details',
  2: 'Add Items',
  3: 'Review Items'
}

function CreateProjectScreen() {
  const { currentStep, setCurrentStep } = useProject()

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <Step1 />
      case 1:
        return <Step2 />
      case 2:
        return <Step3 />
      case 3:
        return <Step4 />
      default:
        return null
    }
  }

  useEffect(() => {
    // Prevent back button on Android
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (currentStep > 1) {
          setCurrentStep(currentStep - 1)
          return true
        }
        return false
      }
    )

    return () => backHandler.remove()
  }, [])

  return (
    <Screen
      preset="scroll"
      backgroundColor={Colors.white}
      contentContainerClass="p-4 bg-white"
      safeAreaEdges={['top']}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: StepTitle[currentStep as keyof typeof StepTitle]
        }}
      />
      {currentStep !== 0 ? (
        <Steps
          className="mb-6"
          current={currentStep}
          items={[
            { title: 'Type' },
            { title: 'Details' },
            { title: 'Items' },
            { title: 'Review' }
          ]}
        />
      ) : null}
      {renderStepContent()}
    </Screen>
  )
}

export default function CreateProjectScreenWithProvider() {
  return (
    <ProjectProvider>
      <CreateProjectScreen />
    </ProjectProvider>
  )
}
