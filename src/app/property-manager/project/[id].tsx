import { useEffect, useState } from 'react'
import { ActivityIndicator, Text, View } from 'react-native'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import { ProjectDescription } from '@/components/property-manager/projects/detail/ProjectDescription'
import { ProjectHeader } from '@/components/property-manager/projects/detail/ProjectHeader'
import { ProjectItemsTab } from '@/components/property-manager/projects/detail/ProjectItemsTab'
import { ProjectProgress } from '@/components/property-manager/projects/detail/ProjectProgress'
import { ProjectTabs } from '@/components/property-manager/projects/detail/ProjectTabs'
import { QuickInfoGrid } from '@/components/property-manager/projects/detail/QuickInfoGrid'
import { TimelineProcess } from '@/components/property-manager/projects/detail/TimelineProcess'
import { UpcomingActivities } from '@/components/property-manager/projects/detail/UpcomingActivities'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import { getFullAddr } from '@/utils/addr'
import { formatRelativeDate } from '@/utils/formatDate'
import { loadString, remove, saveString } from '@/utils/storage'

export default function ProjectDetailScreen() {
  const { id, refresh, tab } = useLocalSearchParams<{
    id: string
    refresh?: string
    tab?: string
  }>()

  const [activeTab, setActiveTab] = useState<'overview' | 'items'>(() => {
    const savedTab = loadString('activeTab') as 'overview' | 'items'
    return savedTab || 'overview'
  })

  // Project type state
  const [projectType, setProjectType] = useState<'REHAB' | 'WORK_ORDER'>(
    'REHAB'
  )

  // Transform activities data for UpcomingActivities component
  const transformActivitiesData = (
    activities: components['schemas']['ActivityDTO'][] | undefined | null
  ) => {
    if (!activities) return []

    return activities.slice(0, 3).map(activity => {
      // Map activity types to icons
      const getActivityIcon = (type: string | undefined): any => {
        switch (type?.toLowerCase()) {
          case 'painting':
            return 'paint-roller'
          case 'installation':
            return 'tools'
          case 'quote':
          case 'review':
            return 'quote-right'
          case 'maintenance':
            return 'wrench'
          default:
            return 'calendar'
        }
      }

      const badgeText = formatRelativeDate(activity.createdTime)
      const badgeType =
        badgeText === 'Tomorrow' || badgeText === 'Today'
          ? 'warning'
          : 'outline'

      return {
        icon: getActivityIcon(activity.activityType),
        title: activity.title || 'Activity',
        subtitle: activity.description || 'No description available',
        badgeText,
        badgeType: badgeType as 'warning' | 'outline'
      }
    })
  }

  // Handle refresh and tab parameters from vendor assignment
  useEffect(() => {
    if (refresh) {
      console.log('Refreshing project data after vendor assignment')
    }

    // If tab parameter is provided, switch to that tab
    if (tab === 'items') {
      setActiveTab('items')
      saveString('activeTab', 'items')
    }
  }, [refresh, tab])

  // API request for project overview
  const {
    data: projectData,
    loading: projectLoading,
    error: projectError
  } = useRequest(
    async () => {
      if (!id) throw new Error('Project ID is required')

      const { data } = await client.GET(
        '/api/v1/pm/project/overview/{projectId}',
        {
          params: {
            path: {
              projectId: parseInt(id, 10)
            }
          }
        }
      )
      console.log('Project data:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch project data')
      }

      return data.data
    },
    {
      ready: !!id,
      refreshDeps: [id, refresh] // Add refresh parameter to trigger data reload
    }
  )

  // API request for activity data
  const { data: activitiesResponse } = useRequest(
    async () => {
      if (!id) return null

      const { data } = await client.POST('/api/v1/pm/activity', {
        body: {
          pageNum: 1,
          pageSize: 10,
          // managerId: projectData?.managerId,
          sortBy: 'createdTime',
          isAsc: 'false'
        }
      })

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch activities data')
      }

      return data.data
    },
    {
      ready: !!id && !!projectData,
      refreshDeps: [id, projectData?.managerId]
    }
  )

  // API request for property data
  const {
    data: propertyData,
    loading: propertyLoading,
    error: propertyError
  } = useRequest(
    async () => {
      if (!projectData?.propertyId) return null

      const { data } = await client.GET('/api/v1/pm/property/{propertyId}', {
        params: {
          path: {
            propertyId: projectData.propertyId
          }
        }
      })

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch property data')
      }

      return data.data
    },
    {
      ready: !!projectData?.propertyId,
      refreshDeps: [projectData?.propertyId]
    }
  )

  // Combined loading and error states
  const loading = projectLoading || propertyLoading
  const error = projectError || propertyError

  // Project type dynamic switching logic
  useEffect(() => {
    if (projectData?.projectType) {
      const type = projectData.projectType.toLowerCase()
      if (
        type.includes('work order') ||
        type.includes('workorder') ||
        type === 'maintenance'
      ) {
        setProjectType('WORK_ORDER')
      } else {
        setProjectType('REHAB')
      }
    }
  }, [projectData])

  // Tab state management
  const handleTabChange = (tab: 'overview' | 'items') => {
    setActiveTab(tab)
    saveString('activeTab', tab)
  }

  // Clear tab state
  useEffect(() => {
    return () => {
      if (activeTab === 'items') {
        remove('activeTab')
      }
    }
  }, [activeTab])

  // Timeline Today marker position calculation
  const calculateTodayPosition = (
    startDate?: string,
    endDate?: string
  ): number => {
    if (!startDate || !endDate) return 0

    const start = new Date(startDate).getTime()
    const end = new Date(endDate).getTime()
    const today = new Date().getTime()

    if (today < start) return 0
    if (today > end) return 100

    return ((today - start) / (end - start)) * 100
  }

  // Calculate days left
  const calculateDaysLeft = (endDate?: string): string => {
    if (!endDate) return 'TBD'

    const end = new Date(endDate).getTime()
    const today = new Date().getTime()
    const daysLeft = Math.max(
      0,
      Math.ceil((end - today) / (1000 * 60 * 60 * 24))
    )

    return daysLeft > 0 ? `${daysLeft} days \nleft` : 'Overdue'
  }

  // Transform API data to component props
  const transformedData = projectData
    ? {
        project: {
          name: projectData.projectName || 'Unknown Project',
          units: projectData.affectedUnits || 'N/A',
          type: projectType === 'WORK_ORDER' ? 'Work Order' : 'Rehab Project',
          projectType // Pass as 'REHAB' | 'WORK_ORDER'
        },
        property: {
          name:
            propertyData?.propertyName ||
            projectData.propertyName ||
            'Unknown Property',
          photos:
            propertyData?.photos?.filter(
              photo => !photo.isDeleted && photo.fileUrl
            ) || [],
          address: getFullAddr(propertyData)
        },
        quickInfoItems: [
          {
            value: projectData.sizeSqFt?.toLocaleString() || 'N/A',
            label: 'Sq. Ft.'
          },
          { value: projectData.itemCount?.toString() || '0', label: 'Items' },
          {
            value: `$${((projectData.budget || 0) / 1000).toFixed(1)}k`,
            label: 'Budget'
          },
          {
            value: projectData.endDate
              ? new Date(projectData.endDate).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric'
                })
              : 'TBD',
            label: 'Due Date'
          }
        ],
        projectDescription: {
          description: projectData.description || 'No description available',
          projectType: projectData.projectType || 'Unknown',
          affectedUnits: parseInt(projectData.affectedUnits || '0') || 0,
          priority: projectData.priority || 'Normal',
          projectManager: projectData.propertyManager?.name || 'Unknown'
        },
        progressStats: [
          {
            icon: 'clipboard-check',
            value: `${projectData.startItemCount || 0}/${projectData.itemCount || 0}`,
            label: 'Items Started',
            progress:
              projectData.itemCount && projectData.startItemCount
                ? (projectData.startItemCount / projectData.itemCount) * 100
                : 0
          },
          {
            icon: 'user-tie',
            value: `${projectData.assignedVendorCount || 0}/${projectData.vendorCount || 0}`,
            label: 'Vendors Assigned',
            progress:
              projectData.vendorCount && projectData.assignedVendorCount
                ? (projectData.assignedVendorCount / projectData.vendorCount) *
                  100
                : 0
          },
          {
            icon: 'file-invoice-dollar',
            value: `$${((projectData.budgetUsed || 0) / 1000).toFixed(1)}k`,
            total: `$${((projectData.budget || 0) / 1000).toFixed(1)}k`,
            label: 'Budget Used',
            progress:
              projectData.budget && projectData.budgetUsed
                ? (projectData.budgetUsed / projectData.budget) * 100
                : 0,
            iconBgColor: 'bg-[#e6f7ff]',
            iconColor: Colors.primary,
            progressColor: Colors.info
          },
          {
            icon: 'circle-check',
            value: `${projectData.completeItemCount || 0}/${projectData.itemCount || 0}`,
            label: 'Items Completed',
            progress:
              projectData.itemCount && projectData.completeItemCount
                ? (projectData.completeItemCount / projectData.itemCount) * 100
                : 0,
            iconBgColor: 'bg-[#e6fff0]',
            iconColor: Colors.primary,
            progressColor: Colors.success
          }
        ],
        overallProgress: parseFloat(projectData.completedPercent || '0')
      }
    : null

  // Loading state
  if (loading) {
    return (
      <Screen
        safeAreaEdges={[]}
        backgroundColor={Colors.light}
        contentContainerClass="flex-1 !justify-center items-center"
      >
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Project Details'
          }}
        />
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text className="mt-4 text-center text-gray-600">
          Loading project details...
        </Text>
      </Screen>
    )
  }

  // Error state
  if (error) {
    return (
      <Screen
        safeAreaEdges={[]}
        backgroundColor={Colors.light}
        contentContainerClass="flex-1 !justify-center items-center p-4"
      >
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Project Details'
          }}
        />
        <Text className="mb-2 text-center text-lg font-semibold text-red-600">
          Failed to load project
        </Text>
        <Text className="text-center text-gray-600">
          {error.message || 'An unexpected error occurred'}
        </Text>
      </Screen>
    )
  }

  // No data state
  if (!transformedData) {
    return (
      <Screen
        safeAreaEdges={[]}
        backgroundColor={Colors.light}
        contentContainerClass="flex-1 !justify-center items-center p-4"
      >
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Project Details'
          }}
        />
        <Text className="text-center text-lg text-gray-600">
          Project not found
        </Text>
      </Screen>
    )
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={[]}
      backgroundColor={Colors.light}
      contentContainerClass="p-4 bg-white"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: transformedData.project.name
        }}
      />

      <ProjectHeader
        project={transformedData.project}
        property={transformedData.property}
      />
      <QuickInfoGrid items={transformedData.quickInfoItems} />
      <ProjectTabs activeTab={activeTab} onTabChange={handleTabChange} />

      {activeTab === 'overview' && (
        <View className="gap-3">
          <ProjectDescription {...transformedData.projectDescription} />
          <ProjectProgress
            progress={transformedData.overallProgress}
            stats={transformedData.progressStats}
          >
            <TimelineProcess
              startDate={
                projectData?.startDate
                  ? new Date(projectData.startDate).toLocaleDateString(
                      'en-US',
                      { month: 'short', day: 'numeric' }
                    )
                  : 'TBD'
              }
              endDate={
                projectData?.endDate
                  ? new Date(projectData.endDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })
                  : 'TBD'
              }
              daysLeft={calculateDaysLeft(projectData?.endDate)}
              progress={transformedData.overallProgress}
              todayPosition={calculateTodayPosition(
                projectData?.startDate,
                projectData?.endDate
              )}
            />
          </ProjectProgress>
          <UpcomingActivities
            activities={transformActivitiesData(activitiesResponse)}
          />
        </View>
      )}

      {activeTab === 'items' && <ProjectItemsTab projectData={projectData} />}
    </Screen>
  )
}
