import React, { useState } from 'react'
import { ScrollView, Text, View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack, useLocalSearchParams } from 'expo-router'
import type { FC } from 'react'

// Import components from the new location
import { ItemActions } from '@/components/property-manager/projects/itemDetail/ItemActions'
import { ItemDescription } from '@/components/property-manager/projects/itemDetail/ItemDescription'
import { ItemHeroHeader } from '@/components/property-manager/projects/itemDetail/ItemHeroHeader'
import { ItemQuickStats } from '@/components/property-manager/projects/itemDetail/ItemQuickStats'
import type { ItemData } from '@/components/property-manager/projects/itemDetail/types'
import { VendorQuotes } from '@/components/property-manager/projects/itemDetail/VendorQuotes'
import { Screen } from '@/components/Screen'
import { client } from '@/services/api'

const ProjectItemDetailScreen: FC = () => {
  const { id, defaultVendorId } = useLocalSearchParams<{
    id: string
    defaultVendorId: string
  }>()
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [isHeroExpanded, setIsHeroExpanded] = useState(false)
  const [expandedVendors, setExpandedVendors] = useState<string[]>(
    defaultVendorId ? [defaultVendorId] : []
  )

  // API call to get item detail
  const {
    data: itemResponse,
    loading,
    error,
    refresh: refreshItemData
  } = useRequest(
    async () => {
      if (!id) throw new Error('Item ID is required')

      const { data } = await client.GET(
        '/api/v1/pm/project/item/detail/{itemId}',
        {
          params: {
            path: {
              itemId: parseInt(id)
            }
          }
        }
      )

      console.log('Item detail response:', data)

      if (data?.code !== 200) {
        throw new Error(data?.message || 'Failed to fetch item details')
      }

      return data
    },
    {
      refreshDeps: [id]
    }
  )

  // Extract item data from API response
  const apiItemData = itemResponse?.data

  // Extract quotes data from item detail response
  const quotesData = apiItemData?.quotes || []

  // Map API data to component format
  const itemData: ItemData = apiItemData
    ? {
        id: apiItemData.itemId?.toString() || id || '1',
        title: apiItemData.itemName || 'Unknown Item',
        category: apiItemData.areaName || 'Unknown Category',
        status: apiItemData.status || 'Unknown',
        budget: apiItemData.budget
          ? `$${apiItemData.budget.toLocaleString()}`
          : '$0',
        description: apiItemData.itemDesc || 'No description available',
        priority: apiItemData.priority || 'Medium',
        startDate: apiItemData.createdTime
          ? new Date(apiItemData.createdTime).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            })
          : 'Not set',
        estimatedCompletion: apiItemData.expectedCompletionDate
          ? new Date(apiItemData.expectedCompletionDate).toLocaleDateString(
              'en-US',
              { month: 'short', day: 'numeric' }
            )
          : 'Not set',
        bestQuote: apiItemData.quotes?.length
          ? `$${Math.min(...apiItemData.quotes.map(q => q.submittedQuote || 0)).toLocaleString()}`
          : 'No quotes'
      }
    : {
        id: id || '1',
        title: 'Loading...',
        category: 'Loading...',
        status: 'Loading...',
        budget: '$0',
        description: 'Loading...',
        priority: 'Medium',
        startDate: 'Loading...',
        estimatedCompletion: 'Loading...',
        bestQuote: 'Loading...'
      }

  const toggleVendorExpansion = (vendorId: string) => {
    setExpandedVendors(prev =>
      prev.includes(vendorId)
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    )
  }

  const handleEdit = () => {
    console.log('Edit item')
  }

  const handleAddVendor = () => {
    console.log('Add vendor')
    router.push({
      pathname: '/property-manager/project/add-vendor',
      params: {
        projectId: apiItemData?.projectId?.toString() || '1',
        itemId: apiItemData?.itemId?.toString() || id || '1',
        itemBudget: apiItemData?.budget?.toString() || '0'
      }
    })
  }

  const handleDelete = async () => {
    if (!apiItemData?.itemId) {
      console.error('Item ID is required for deletion')
      return
    }

    try {
      // Confirm deletion with user
      const confirmed = confirm(
        'Are you sure you want to delete this item? This action cannot be undone.'
      )
      if (!confirmed) {
        return
      }

      // Call delete API
      const response = await client.GET(
        '/api/v1/pm/project/item/delete/{itemId}',
        {
          params: {
            path: {
              itemId: apiItemData.itemId
            }
          }
        }
      )

      if (response.data?.code === 200) {
        console.log('Item deleted successfully')
        // Navigate back to project detail page with refresh parameter
        router.push({
          pathname: `/property-manager/project/${apiItemData.projectId}`,
          params: {
            refresh: Date.now().toString(), // Use timestamp to ensure refresh
            tab: 'items' // Switch to items tab to show updated list
          }
        })
      } else {
        console.error(
          'Failed to delete item:',
          response.error?.message || 'Unknown error'
        )
      }
    } catch (error) {
      console.error('Delete item error:', error)
    }
  }

  // Handle loading state for item details
  // if (loading) {
  //   return (
  //     <Screen>
  //       <Stack.Screen
  //         options={{
  //           headerShown: true,
  //           headerTitle: 'Loading...',
  //           headerBackTitle: 'Back'
  //         }}
  //       />
  //       <View className="flex-1 items-center justify-center bg-gray-100">
  //         <Text className="text-lg text-gray">Loading item details...</Text>
  //       </View>
  //     </Screen>
  //   )
  // }

  // Handle error state for item details
  if (error) {
    return (
      <Screen>
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Error',
            headerBackTitle: 'Back'
          }}
        />
        <View className="flex-1 items-center justify-center bg-gray-100 p-4">
          <Text className="mb-4 text-center text-lg text-red-500">
            Failed to load item details
          </Text>
          <Text className="text-center text-gray">
            {error.message || 'An unexpected error occurred'}
          </Text>
        </View>
      </Screen>
    )
  }

  return (
    <Screen>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: itemData.title,
          headerBackTitle: 'Back'
        }}
      />
      <ScrollView className="flex-1 bg-gray-100">
        {/* Hero Header */}
        <ItemHeroHeader
          item={itemData}
          isExpanded={isHeroExpanded}
          onToggleExpand={() => setIsHeroExpanded(!isHeroExpanded)}
          photos={
            apiItemData?.photos?.map(photo => photo.fileUrl).filter(Boolean) ||
            []
          }
        />

        {/* Quick Stats */}
        <ItemQuickStats item={itemData} />

        {/* Description */}
        <ItemDescription
          description={itemData.description}
          isExpanded={isDescriptionExpanded}
          onToggleExpand={() =>
            setIsDescriptionExpanded(!isDescriptionExpanded)
          }
        />

        {/* Vendor Quotes */}
        <VendorQuotes
          vendorQuotesData={quotesData}
          expandedVendors={expandedVendors}
          onToggleVendor={toggleVendorExpansion}
          isLoading={loading}
          error={error}
          projectId={apiItemData?.projectId}
          itemId={apiItemData?.itemId}
          onQuoteApproved={() => {
            // Refresh quotes data after approval
            refreshItemData()
          }}
        />

        {/* Action Buttons */}
        <ItemActions
          onEdit={handleEdit}
          onAddVendor={handleAddVendor}
          onDelete={handleDelete}
        />
      </ScrollView>
    </Screen>
  )
}

export default ProjectItemDetailScreen
