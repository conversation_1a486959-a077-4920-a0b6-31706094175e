import { useMemo, useRef, useState } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { Stack } from 'expo-router'

import type { InfiniteScrollRef } from '@/components'
import { InfiniteScroll, Screen, Segmented } from '@/components'
import { QuoteCard } from '@/components/property-manager/quotes/QuoteCard'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'

type Quote = components['schemas']['ItemReviewQuotesDTO']
export type SearchParams = {
  quotesStatus: string
}

export default function ReviewQuotesScreen() {
  const { getDictItems } = useDict()
  const [selectedFilter, setSelectedFilter] = useState('all')
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)

  const itemQuoteStatusRequest = useRequest(() =>
    getDictItems('ITEM_QUOTES_STATUS')
  )

  const request = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/pm/quotes/list', {
        body: {
          pageNum: page,
          pageSize,
          ...args
        }
      })
      return {
        data: data?.data?.list ?? [],
        total: data?.data?.total || 0,
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { manual: true, refreshDeps: [selectedFilter] }
  )
  const searchParams = useMemo<SearchParams>(() => {
    return {
      quotesStatus: selectedFilter === 'all' ? '' : selectedFilter
    }
  }, [selectedFilter])

  const renderQuote = (quote: Quote, index: number) => (
    <QuoteCard
      quote={quote}
      onApproved={() => {
        const clone = [...request.data!.data!]
        clone[index]!.quoteStatus = 'APPROVED'
        request.mutate({
          data: clone,
          hasMore: request.data!.hasMore,
          total: request.data!.total
        })
      }}
    />
  )

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="flex-1"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Review Quotes'
        }}
      />

      <View className="flex-1 p-4">
        <Segmented
          value={selectedFilter}
          className="mb-4 flex-shrink-0 flex-grow-0"
          itemClassName="text-center"
          onChange={setSelectedFilter}
          options={[
            {
              label: 'All',
              value: 'all'
            },
            ...(itemQuoteStatusRequest.data?.map(item => ({
              label: item.label!,
              value: item.code!
            })) ?? [])
          ]}
        />

        <InfiniteScroll<Quote, SearchParams>
          ref={infiniteScrollRef}
          renderItem={renderQuote}
          requestArgs={searchParams}
          onRequest={request.runAsync}
          emptyText="No quote found."
          className="flex-1"
          itemClassName="w-full"
          numColumns={1}
          pageSize={10}
        />
      </View>
    </Screen>
  )
}
