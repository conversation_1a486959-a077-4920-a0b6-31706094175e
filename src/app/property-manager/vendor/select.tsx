import { useMemo, useRef, useState } from 'react'
import { Pressable, ScrollView, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Stack, useRouter, useLocalSearchParams } from 'expo-router'

import type { InfiniteScrollRef } from '@/components'
import { InfiniteScroll, ProjectIcon, Search } from '@/components'
import Alert from '@/components/Alert'
import { BorderCard } from '@/components/BorderCard'
import { Button } from '@/components/Button'
import { VendorCard } from '@/components/property-manager/vendor/VendorCard'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'
import type { ProjectWorkType } from '@/types'

const CATEGORIES = [
  { id: 'all', label: 'All Vendors' },
  { id: 'plumbing', label: 'Plumbing' },
  { id: 'electrical', label: 'Electrical' },
  { id: 'carpentry', label: 'Carpentry' },
  { id: 'hvac', label: 'HVAC' },
  { id: 'painting', label: 'Painting' }
]

const ProjectTypeConfig = {
  REHAB: {
    borderColor: Colors.success
  },
  WORK_ORDER: {
    borderColor: Colors.primary
  }
} as const

type SearchParams = {
  searchText?: string
  // cat?: string
}

type Vendor = components['schemas']['VendorSelectDTO']

export default function VendorSelectScreen() {
  const router = useRouter()
  const { workType = 'REHAB' } = useLocalSearchParams<{
    workType: ProjectWorkType
  }>()
  const [searchInput, setSearchInput] = useState('')
  const [searchText, setSearchText] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedVendorId, setSelectedVendorId] = useState<number | null>(null)
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)

  const config = ProjectTypeConfig[workType]

  const vendorsRequest = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/pm/vendor/list', {
        body: {
          pageNum: page,
          pageSize: pageSize,
          ...args
        }
      })
      return {
        data: data?.data?.list ?? [],
        total: data?.data?.total ?? 0,
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    {
      manual: true
    }
  )

  const searchParams = useMemo<SearchParams>(() => {
    return {
      searchText
    }
  }, [searchText])

  const handleAssignVendor = () => {
    if (!selectedVendorId) return

    // Navigate back with the selected vendor ID as a parameter
    router.back()
    // Use replace to pass the selected vendor ID back to the previous screen
    setTimeout(() => {
      router.replace({
        pathname: '/property-manager/project/create',
        params: { selectedVendorId: selectedVendorId.toString() }
      })
    }, 100)
  }

  const renderVendor = (vendor: Vendor) => (
    <VendorCard
      key={vendor.vendorId}
      name={vendor.userName || ''}
      specialty={vendor.specialties || ''}
      rating={3}
      reviewCount={12}
      availability={'Available in 2 days'}
      distance={'5.2 miles away'}
      isSelected={selectedVendorId === vendor.vendorId}
      onSelect={() => setSelectedVendorId(vendor.vendorId!)}
      onCall={() => {}}
      onEmail={() => {}}
    />
  )

  return (
    <View className="flex-1 bg-[#F4F5F7]">
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Assign Vendor'
        }}
      />
      <ScrollView className="flex-1 px-4 pt-4">
        {/* Project Type Indicator */}
        <BorderCard className="mb-4" color={config.borderColor}>
          <View className="flex-row items-center">
            <ProjectIcon
              type={workType}
              className="mr-3 h-12 w-12 rounded-default"
            />
            <View className="flex-1">
              <Text className="mb-0.5 text-base font-semibold">
                {workType === 'REHAB' ? 'Rehab' : 'Work Order'} Project
              </Text>
              <Text className="text-sm text-gray">
                {workType === 'REHAB'
                  ? 'Major renovations and property improvements'
                  : 'Maintenance, repairs, and smaller tasks'}
              </Text>
            </View>
          </View>
        </BorderCard>

        {/* Empty Project Notice */}
        <View className="mb-4">
          <Alert
            leftIcon={
              <FontAwesome6
                name="circle-info"
                size={20}
                color={Colors.success}
              />
            }
            type="success"
            title="Creating Empty Project"
            message="You're creating an empty project. Please assign a vendor to get started."
          />
        </View>

        {/* Search and Filter */}
        <View className="mb-4 rounded-lg bg-white p-3">
          <Search
            className="w-full"
            placeholder="Search vendors..."
            value={searchInput}
            onChangeText={setSearchInput}
            onClear={() => {
              setSearchText('')
            }}
            onSearch={v => {
              setSearchText(v)
            }}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="mt-3 pb-2"
          >
            <View className="flex-row gap-2">
              {CATEGORIES.map(category => (
                <Pressable
                  key={category.id}
                  onPress={() => setSelectedCategory(category.id)}
                  className={classNames(
                    'rounded-full px-3 py-1.5',
                    selectedCategory === category.id
                      ? 'bg-[#E6F7EF]'
                      : 'bg-[#F4F5F7]'
                  )}
                >
                  <Text
                    className={classNames(
                      'text-xs',
                      selectedCategory === category.id
                        ? 'text-success'
                        : 'text-[#42526E]'
                    )}
                  >
                    {category.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Vendor List */}

        <InfiniteScroll<Vendor, SearchParams>
          ref={infiniteScrollRef}
          // initialLoad={false}
          renderItem={renderVendor}
          requestArgs={searchParams}
          onRequest={vendorsRequest.runAsync}
          emptyText="No vendors found."
          className="flex-1"
          itemClassName="w-full"
          numColumns={1}
          pageSize={10}
        />
      </ScrollView>

      {/* Bottom Bar */}
      <View className="absolute bottom-0 left-0 right-0 flex-row items-center border-t border-[#DFE1E6] bg-white p-4">
        <Button
          variant="cancel"
          onPress={() => {
            router.back()
          }}
          leftIcon="arrow-left"
        >
          Back to Details
        </Button>
        <Button
          variant={workType === 'REHAB' ? 'success' : 'primary'}
          onPress={handleAssignVendor}
          disabled={!selectedVendorId}
          leftIcon={<FontAwesome6 name="user-plus" size={16} color="white" />}
        >
          Assign Vendor
        </Button>
      </View>
    </View>
  )
}
