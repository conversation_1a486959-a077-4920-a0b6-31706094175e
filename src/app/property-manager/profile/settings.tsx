import { Text, View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack } from 'expo-router'
import { Toast } from 'toastify-react-native'

import {
  Button,
  Form,
  FormItem,
  Input,
  Screen,
  Select,
  Switch
} from '@/components'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'

type SettingsFormData = components['schemas']['PMProfileVO']

// Form type with string values for numeric inputs
type SettingsForm = Omit<SettingsFormData, 'pmThreshold' | 'ownerThreshold'> & {
  pmThreshold?: string
  ownerThreshold?: string
}

export default function SettingsScreen() {
  const form = Form.useForm<SettingsForm>()

  // Load profile settings using useRequest
  const { loading } = useRequest(
    async () => {
      const { data, error } = await client.GET('/api/v1/pm/profile/my')
      if (error) {
        throw new Error('Failed to load settings')
      }
      return data?.data?.profileInfo
    },
    {
      onError: error => {
        Toast.error(error.message || 'Failed to load settings')
      },
      onSuccess: profileInfo => {
        if (profileInfo) {
          // Set form values with API data
          form.setValue(
            'enableAutoApprove',
            profileInfo.enableAutoApprove ?? false
          )
          form.setValue('pmThreshold', String(profileInfo.pmThreshold ?? 500))
          form.setValue(
            'enableOwnerApprove',
            profileInfo.enableOwnerApprove ?? false
          )
          form.setValue(
            'ownerThreshold',
            String(profileInfo.ownerThreshold ?? 1000)
          )
          // form.setValue('ownerApprovalRequiredForAllQuotes', profileInfo.ownerApprovalRequiredForAllQuotes ?? false)
        }
      }
    }
  )

  // Save settings using useRequest
  const { loading: saving, run: saveSettings } = useRequest(
    async (values: SettingsForm) => {
      // Convert string values back to numbers for API
      const apiData: SettingsFormData = {
        ...values,
        pmThreshold: values.pmThreshold
          ? Number(values.pmThreshold)
          : undefined,
        ownerThreshold: values.ownerThreshold
          ? Number(values.ownerThreshold)
          : undefined
      }

      const { data, error } = await client.PUT('/api/v1/pm/profile/my', {
        body: apiData
      })

      if (error) {
        throw new Error('Failed to save settings')
      }
      return data
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.success('Settings saved successfully')
        router.back()
      },
      onError: error => {
        Toast.error(error.message || 'Failed to save settings')
      }
    }
  )

  const DollarIcon = () => (
    // <FontAwesome6 name="dollar-sign" size={12} color={Colors.gray} />
    <Text className="text-gray">$</Text>
  )

  if (loading) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={['top']}
        backgroundColor={Colors.white}
      >
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Settings'
          }}
        />
        <View className="flex-1 items-center justify-center">
          <Text className="text-gray">Loading settings...</Text>
        </View>
      </Screen>
    )
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={['top']}
      backgroundColor={Colors.white}
      contentContainerClass="py-4"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Settings'
        }}
      />

      <Text className="mb-4 px-4 text-base font-semibold text-dark">
        Quote Approval Settings
      </Text>

      <Form<SettingsForm>
        form={form}
        className="px-4"
        initialValues={{
          enableAutoApprove: true,
          pmThreshold: '500',
          enableOwnerApprove: true,
          ownerThreshold: '1000'
          // ownerApprovalRequiredForAllQuotes: false
        }}
        onFinish={saveSettings}
      >
        {/* Auto-Approval Settings */}
        <View
          className="mb-4 rounded-lg bg-white p-4"
          style={[crossPlatformShadow('default')]}
        >
          <View className="mb-4">
            <Text className="mb-1 text-base font-semibold text-dark">
              Auto-Approval Rules
            </Text>
            <Text className="text-xs text-gray">
              Configure when vendor quotes can be auto-approved without your
              review
            </Text>
          </View>

          <FormItem
            className="border-b border-b-border py-3"
            name="enableAutoApprove"
            layout="horizontal"
            label="Enable Auto-Approval"
          >
            <Switch />
          </FormItem>

          <FormItem name="pmThreshold" dependencies={['enableAutoApprove']}>
            {({ enableAutoApprove }: { enableAutoApprove: boolean }) =>
              enableAutoApprove && (
                <View className="mt-4">
                  <FormItem name="pmThreshold" label="PM Threshold">
                    <Input
                      keyboardType="numeric"
                      LeftAccessory={DollarIcon}
                      placeholder="Enter amount"
                    />
                  </FormItem>
                  <Text className="text-xs text-gray">
                    Quotes below this amount are auto-approved
                  </Text>
                </View>
              )
            }
          </FormItem>
        </View>

        {/* Owner Approval Settings */}
        <View
          className="mb-4 rounded-lg bg-white p-4"
          style={[crossPlatformShadow('default')]}
        >
          <View className="mb-4">
            <Text className="mb-1 text-base font-semibold text-dark">
              Owner Approval Rules
            </Text>
            <Text className="text-xs text-gray">
              Configure when quotes require owner approval
            </Text>
          </View>

          <FormItem
            className="border-b border-b-border py-3"
            name="enableOwnerApprove"
            layout="horizontal"
            label="Enable Owner Approval"
          >
            <Switch />
          </FormItem>

          <FormItem name="ownerThreshold" dependencies={['enableOwnerApprove']}>
            {({ enableOwnerApprove }: { enableOwnerApprove: boolean }) =>
              enableOwnerApprove && (
                <View className="mt-4">
                  <FormItem name="ownerThreshold" label="Owner Threshold">
                    <Input
                      keyboardType="numeric"
                      LeftAccessory={DollarIcon}
                      placeholder="Enter amount"
                    />
                  </FormItem>
                  <Text className="text-xs text-gray">
                    Quotes exceeding this amount require owner approval
                  </Text>

                  <FormItem
                    name="ownerApprovalRequiredForAllQuotes"
                    label="Owner Approval Required for All Quotes"
                    className="mt-4"
                  >
                    <Switch />
                  </FormItem>

                  <FormItem
                    name="ownerContact"
                    label="Default Owner Contact"
                    className="mt-4"
                  >
                    <Select
                      options={[
                        {
                          label: 'John Smith (<EMAIL>)',
                          value: 'john'
                        },
                        {
                          label: 'Mary Johnson (<EMAIL>)',
                          value: 'mary'
                        },
                        {
                          label: 'Add New Owner...',
                          value: 'custom'
                        }
                      ]}
                    />
                  </FormItem>
                </View>
              )
            }
          </FormItem>
        </View>

        <Button
          variant="primary"
          onPress={() => form.submit()}
          className="mt-4"
          loading={saving}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </Form>
    </Screen>
  )
}
