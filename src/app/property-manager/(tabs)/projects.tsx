import { useMemo, useRef, useState } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack, useLocalSearchParams } from 'expo-router'

import type { InfiniteScrollRef } from '@/components'
import {
  IconButton,
  InfiniteScroll,
  RadioButtonGroup,
  Screen
} from '@/components'
import { ProjectCard } from '@/components/property-manager/projects/ProjectCard'
import { Search } from '@/components/Search'
import { client } from '@/services/api'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'
import type { Project } from '@/types'

export const colors = {
  DRAFT: [Colors.white, Colors.gray],
  SUBMITTED: [Colors.white, Colors.tenant],
  PENDING_QUOTES: [Colors.white, Colors.warning],
  IN_PROGRESS: [Colors.white, Colors.info],
  COMPLETED: [Colors.white, Colors.success]
} as const

export type SearchParams = {
  searchText?: string
  projectStatus?:
    | 'DRAFT'
    | 'SUBMITTED'
    | 'PENDING_QUOTES'
    | 'IN_PROGRESS'
    | 'COMPLETED'
    | 'CANCELLED'
}

export default function ProjectsScreen() {
  const { projectStatus = 'all' } = useLocalSearchParams<{
    projectStatus: string
  }>()
  const [searchInput, setSearchInput] = useState('')
  const [searchText, setSearchText] = useState('')
  const { getDictItems } = useDict()
  const [selectedFilter, setSelectedFilter] = useState(projectStatus)
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)

  const projectStatusRequest = useRequest(() => getDictItems('PROJECT_STATUS'))

  const request = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/pm/project/list', {
        body: {
          pageNum: page,
          pageSize,
          ...args
        }
      })
      return {
        data: data?.data?.list ?? [],
        total: data?.data?.total || 0,
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { manual: true, refreshDeps: [searchText, selectedFilter] }
  )
  const searchParams = useMemo<SearchParams>(() => {
    return {
      searchText,
      projectStatus:
        selectedFilter === 'all'
          ? undefined
          : (selectedFilter as SearchParams['projectStatus'])
    }
  }, [searchText, selectedFilter])

  const renderProject = (project: Project) => <ProjectCard project={project} />

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="flex-1"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'My Projects',
          headerRight: () => (
            <IconButton
              className="mr-4"
              name="plus"
              onPress={() => {
                router.push('/property-manager/project/create')
              }}
            />
          )
        }}
      />
      <View className="flex-1 p-4">
        <Search
          value={searchInput}
          onChange={setSearchInput}
          placeholder="Search projects..."
          onClear={() => {
            setSearchText('')
          }}
          onSearch={v => {
            setSearchText(v)
          }}
        />
        <RadioButtonGroup
          value={selectedFilter}
          className="my-4 flex-shrink-0 flex-grow-0"
          onChange={setSelectedFilter}
          items={[
            {
              label: 'All',
              value: 'all',
              activeColor: Colors.white,
              activeBgColor: Colors.primary
            },
            ...(projectStatusRequest.data?.map(item => ({
              label: item.label!,
              value: item.code!,
              activeColor: colors[item.code! as keyof typeof colors]?.[0],
              activeBgColor: colors[item.code! as keyof typeof colors]?.[1]
            })) ?? [])
          ]}
        />

        <InfiniteScroll<Project, SearchParams>
          ref={infiniteScrollRef}
          // initialLoad={false}
          renderItem={renderProject}
          requestArgs={searchParams}
          onRequest={request.runAsync}
          emptyText="No projects found."
          className="flex-1"
          itemClassName="w-full"
          numColumns={1}
          pageSize={10}
        />
      </View>
    </Screen>
  )
}
