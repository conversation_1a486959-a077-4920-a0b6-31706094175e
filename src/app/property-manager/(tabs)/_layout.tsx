import { Platform } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { Tabs } from 'expo-router'

import { HapticTab } from '@/components/HapticTab'
import TabBarBackground from '@/components/ui/TabBarBackground'
import { Colors } from '@/theme/colors'

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute'
          },
          default: {}
        })
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          // @ts-expect-error
          tabBarIcon: ({ color }) => <FA name="house" size={20} color={color} />
        }}
      />
      <Tabs.Screen
        name="properties"
        options={{
          title: 'Properties',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="building" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="drafts"
        options={{
          title: 'Drafts',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="file-pen" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="projects"
        options={{
          title: 'Projects',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="list-check" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="messages"
        options={{
          title: 'Messages',
          // @ts-expect-error
          tabBarIcon: ({ color }) => (
            <FA size={20} name="comments" color={color} />
          )
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          // @ts-expect-error
          tabBarIcon: ({ color }) => <FA size={20} name="user" color={color} />
        }}
      />
    </Tabs>
  )
}
