import { useMemo, useRef, useState } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { router, Stack } from 'expo-router'

import type { InfiniteScrollRef } from '@/components'
import {
  IconButton,
  InfiniteScroll,
  RadioButtonGroup,
  Screen
} from '@/components'
import { PropertyCard } from '@/components/property-manager/properties/PropertyCard'
import { Search } from '@/components/Search'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'

type Property = components['schemas']['PropertyInfoDTO']

export type SearchParams = {
  searchText?: string
  propertyStatus?: 'DRAFT' | 'VACANT' | 'OCCUPIED'
}

export const colors = {
  DRAFT: [Colors.white, Colors.gray],
  SUBMITTED: [Colors.white, Colors.tenant],
  PENDING_QUOTES: [Colors.white, Colors.warning],
  IN_PROGRESS: [Colors.white, Colors.info],
  COMPLETED: [Colors.white, Colors.success]
} as const

export default function MyPropertiesScreen() {
  const [searchInput, setSearchInput] = useState('')
  const [searchText, setSearchText] = useState('')
  const { getDictItems } = useDict()
  const [selectedFilter, setSelectedFilter] = useState('all')
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)

  const propertyStatusRequest = useRequest(() =>
    getDictItems('PROPERTY_STATUS')
  )

  const request = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/pm/property/list', {
        body: {
          pageNum: page,
          pageSize: pageSize,

          ...args
        }
      })
      return {
        data: data?.data?.list ?? [],
        total: data?.data?.total || 0,
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { manual: true, refreshDeps: [searchText, selectedFilter] }
  )

  const searchParams = useMemo<SearchParams>(() => {
    return {
      searchText,
      propertyStatus:
        selectedFilter === 'all'
          ? undefined
          : (selectedFilter as SearchParams['propertyStatus'])
    }
  }, [searchText, selectedFilter])

  const renderProperty = (property: Property) => (
    <PropertyCard
      property={property}
      onPress={() => {
        router.push(`/property-manager/property/${property.propertyId}`)
      }}
    />
  )
  return (
    <Screen
      preset="fixed"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="flex-1"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'My Properties',
          headerRight: () => (
            <IconButton
              className="mr-4"
              name="plus"
              onPress={() => {
                router.push('/property-manager/property/create')
              }}
            />
          )
        }}
      />

      <View className="flex-1 p-4">
        <Search
          value={searchInput}
          onChange={setSearchInput}
          placeholder="Search projects..."
          onClear={() => {
            setSearchText('')
          }}
          onSearch={v => {
            setSearchText(v)
          }}
        />
        <RadioButtonGroup
          value={selectedFilter}
          className="my-4 flex-shrink-0 flex-grow-0"
          onChange={setSelectedFilter}
          items={[
            {
              label: 'All',
              value: 'all',
              activeColor: Colors.white,
              activeBgColor: Colors.primary
            },
            ...(propertyStatusRequest.data?.map(item => ({
              label: item.label!,
              value: item.code!,
              activeColor: colors[item.code! as keyof typeof colors]?.[0],
              activeBgColor: colors[item.code! as keyof typeof colors]?.[1]
            })) ?? [])
          ]}
        />

        <InfiniteScroll<Property, SearchParams>
          ref={infiniteScrollRef}
          renderItem={renderProperty}
          requestArgs={searchParams}
          onRequest={request.runAsync}
          emptyText="No property found."
          className="flex-1"
          itemClassName="w-[48%]"
          numColumns={2}
          pageSize={10}
        />
      </View>
    </Screen>
  )
}
