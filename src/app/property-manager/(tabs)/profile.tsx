import React from 'react'
import { useState } from 'react'
import { Pressable, ScrollView, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Stack } from 'expo-router'
import { Toast } from 'toastify-react-native'

import { EditProfileModal } from '@/components/common/profile/EditProfileModal'
import { PerformanceStats } from '@/components/common/profile/PerformanceStats'
import { Personal } from '@/components/common/profile/Personal'
import { PersonalInformation } from '@/components/common/profile/PersonalInformation'
import { AccountSettingsCard } from '@/components/property-owner/profile/AccountSettingsCard'
import { SupportLegalCard } from '@/components/property-owner/profile/SupportLegalCard'
import { client } from '@/services/api'
import { useAuth } from '@/store'
import { usePMStat } from '@/store/pmStat'
import { Colors } from '@/theme/colors'
import { getFullName } from '@/utils/user'
import { validateUSPhoneNumber } from '@/utils/validators'

type PMProfile = {
  firstName: string
  lastName: string
  phoneNumber: string
  location: string
  company: string
}

export default function ProfileScreen() {
  const {
    computed: { avatar },
    refreshMe
  } = useAuth()
  const [editVisible, setEditVisible] = useState(false)

  const profileRequest = useRequest(async () => {
    const { data } = await client.GET('/api/v1/pm/profile/my')
    return data?.data
  })

  const onUpdateProfile = async (values: PMProfile) => {
    const { error } = await client.PUT('/api/v1/pm/profile/my', {
      body: values
    })
    if (!error) {
      Toast.success('Profile updated')
      profileRequest.mutate({
        baseInfo: {
          firstName: values.firstName,
          lastName: values.lastName,
          phoneNumber: values.phoneNumber
        },
        profileInfo: {
          location: values.location,
          company: values.company
        }
      })
      refreshMe()
    }
    return !error
  }

  const profile = profileRequest.data

  const {
    totalPropertyCount,
    activeProjectCount,
    // pendingQuotesCount,
    // completeThisMonthProjectCount,
    vendorCount
    // refresh
  } = usePMStat()

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Profile',
          headerRight: () => (
            <Pressable className="mr-6" onPress={() => setEditVisible(true)}>
              <FontAwesome6
                name="pen-to-square"
                size={20}
                color={Colors.dark}
              />
            </Pressable>
          )
        }}
      />
      <View style={{ flex: 1, backgroundColor: Colors.white }}>
        <ScrollView contentContainerStyle={{ padding: 16 }}>
          <Personal
            avatar={avatar}
            name={profile?.baseInfo?.userName || ''}
            email={profile?.baseInfo?.email || ''}
            role="Property Manager"
          />
          <PerformanceStats
            items={[
              {
                label: 'Properties',
                value: totalPropertyCount,
                color: Colors.primary
              },
              { label: 'Vendors', value: vendorCount, color: Colors.success },
              {
                label: 'Active Projects',
                value: activeProjectCount,
                color: Colors.warning
              }
            ]}
          />
          <PersonalInformation
            items={[
              { label: 'Full Name', value: getFullName(profile?.baseInfo) },
              {
                label: 'Phone Number',
                value: profile?.baseInfo?.phoneNumber || ''
              },
              {
                label: 'Location',
                value: profile?.profileInfo?.location || ''
              },
              { label: 'Company', value: profile?.profileInfo?.company || '' }
            ]}
          />
          <AccountSettingsCard />
          <SupportLegalCard />
        </ScrollView>
        <EditProfileModal<PMProfile>
          visible={editVisible}
          initialValues={{
            firstName: profile?.baseInfo?.firstName || '',
            lastName: profile?.baseInfo?.lastName || '',
            phoneNumber: profile?.baseInfo?.phoneNumber || '',
            location: profile?.profileInfo?.location || '',
            company: profile?.profileInfo?.company || ''
          }}
          columns={[
            {
              label: 'First Name',
              name: 'firstName'
            },
            {
              label: 'Last Name',
              name: 'lastName'
            },
            {
              label: 'Phone Number',
              name: 'phoneNumber',
              type: 'phone',
              rules: { validate: validateUSPhoneNumber }
            },
            {
              label: 'Location',
              name: 'location'
            },
            {
              label: 'Company',
              name: 'company'
            }
          ]}
          onClose={() => setEditVisible(false)}
          onSave={onUpdateProfile}
        />
      </View>
    </>
  )
}
