import { useCallback, useRef } from 'react'
import type { NativeScrollEvent, NativeSyntheticEvent } from 'react-native'
import { Pressable, ScrollView } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { router, Stack } from 'expo-router'

import { Badge } from '@/components'
import { PropertyOverview } from '@/components/property-manager/home/<USER>'
import { QuickActions } from '@/components/property-manager/home/<USER>'
import {
  RecentActivities,
  type RecentActivitiesRef
} from '@/components/property-manager/home/<USER>'
import { RecentProperties } from '@/components/property-manager/home/<USER>'
import { UserInfo } from '@/components/property-manager/home/<USER>'
import { Colors } from '@/theme/colors'

export default function HomeScreen() {
  const scrollViewRef = useRef<ScrollView>(null)
  const recentActivitiesRef = useRef<RecentActivitiesRef>(null)

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent
      const paddingToBottom = 20
      const isCloseToBottom =
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom

      if (isCloseToBottom && recentActivitiesRef.current?.canLoadMore()) {
        console.log('load act')
        recentActivitiesRef.current.loadMore()
      }
    },
    []
  )

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Dashboard',
          headerCenter: true,
          headerLeft: () => (
            <Pressable
              className="ml-6"
              onPress={() => router.push('/property-manager/profile/settings')}
            >
              <FA name="gear" solid size={24} color={Colors.dark} />
            </Pressable>
          ),
          headerRight: () => (
            <Badge
              onPress={() =>
                router.push('/property-manager/notifications/list')
              }
              className="mr-6"
              number={3}
              trigger={<FA name="bell" size={24} color={Colors.danger} />}
            />
          )
        }}
      />
      <ScrollView
        ref={scrollViewRef}
        className="flex-1"
        contentContainerClassName="p-4"
        style={{ backgroundColor: Colors.white }}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        <UserInfo />
        <QuickActions />
        <PropertyOverview />
        <RecentProperties />
        <RecentActivities ref={recentActivitiesRef} />
      </ScrollView>
    </>
  )
}
