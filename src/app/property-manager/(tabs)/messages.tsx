import React from 'react'
import { Text, View } from 'react-native'

import { Screen } from '@/components'
import ChatList from '@/components/messages/ChatList'

export default function MessagesScreen() {
  console.log('MessagesScreen rendered')

  return (
    <Screen preset="scroll" safeAreaEdges={['top']}>
      <View className="flex-1">
        <View className="border-b border-gray-200 bg-white p-4">
          <Text className="text-lg font-semibold text-gray-900">Messages</Text>
          <Text className="text-gray-500 mt-1 text-sm">
            Tap on a chat to start messaging
          </Text>
        </View>
        <ChatList />
      </View>
    </Screen>
  )
}
