import React, { use<PERSON><PERSON>back, useReducer } from 'react'
import { Alert, StyleSheet, Text, View } from 'react-native'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import ChatRoom from '@/components/messages/ChatRoom'

// Mock data for testing when API is not available
const mockMessages = [
  {
    _id: 1,
    text: 'Hello! Welcome to the chat.',
    createdAt: new Date(),
    user: {
      _id: 2,
      name: 'Support Team'
    },
    sent: true,
    received: true
  },
  {
    _id: 2,
    text: 'Hi there! How can I help you today?',
    createdAt: new Date(Date.now() - 60000),
    user: {
      _id: 1,
      name: 'Me'
    }
  },
  {
    _id: 3,
    text: 'I have a question about the property management system.',
    createdAt: new Date(Date.now() - 120000),
    user: {
      _id: 1,
      name: 'Me'
    }
  }
]

interface ChatState {
  messages: any[]
  isLoadingEarlier: boolean
  loadEarlier: boolean
  isTyping: boolean
}

enum ActionKind {
  SET_MESSAGES = 'SET_MESSAGES',
  ADD_MESSAGE = 'ADD_MESSAGE',
  LOAD_EARLIER_START = 'LOAD_EARLIER_START',
  LOAD_EARLIER_END = 'LOAD_EARLIER_END',
  SET_IS_TYPING = 'SET_IS_TYPING'
}

interface StateAction {
  type: ActionKind
  payload?: any
}

function reducer(state: ChatState, action: StateAction): ChatState {
  switch (action.type) {
    case ActionKind.SET_MESSAGES:
      return {
        ...state,
        messages: action.payload
      }
    case ActionKind.ADD_MESSAGE:
      return {
        ...state,
        messages: [action.payload, ...state.messages]
      }
    case ActionKind.LOAD_EARLIER_START:
      return {
        ...state,
        isLoadingEarlier: true
      }
    case ActionKind.LOAD_EARLIER_END:
      return {
        ...state,
        isLoadingEarlier: false,
        loadEarlier: action.payload
      }
    case ActionKind.SET_IS_TYPING:
      return {
        ...state,
        isTyping: action.payload
      }
    default:
      return state
  }
}

export default function ChatDetailScreen() {
  const params = useLocalSearchParams()
  const sessionId = Number(params.id)
  const insets = useSafeAreaInsets()

  const [state, dispatch] = useReducer(reducer, {
    messages: mockMessages,
    isLoadingEarlier: false,
    loadEarlier: true,
    isTyping: false
  })

  // Debug: Log the session ID
  console.log('ChatDetailScreen - sessionId:', sessionId, 'params:', params)

  const onSend = useCallback((messages: any[]) => {
    const newMessage = messages[0]
    dispatch({ type: ActionKind.ADD_MESSAGE, payload: newMessage })
  }, [])

  const onLoadEarlier = useCallback(() => {
    dispatch({ type: ActionKind.LOAD_EARLIER_START })

    // Simulate loading more messages
    setTimeout(() => {
      const earlierMessages = [
        {
          _id: Math.random(),
          text: 'Earlier message ' + new Date().toLocaleTimeString(),
          createdAt: new Date(Date.now() - 86400000), // 1 day ago
          user: {
            _id: 2,
            name: 'Support Team'
          }
        }
      ]

      dispatch({
        type: ActionKind.SET_MESSAGES,
        payload: [...earlierMessages, ...state.messages]
      })
      dispatch({ type: ActionKind.LOAD_EARLIER_END, payload: false })
    }, 1000)
  }, [state.messages])

  const onPressAvatar = useCallback((user: any) => {
    Alert.alert('User Profile', `Viewing profile of ${user.name}`)
  }, [])

  const onLongPress = useCallback((context: any, message: any) => {
    if (!message.text) return

    const options = ['Copy text', 'Cancel']
    const cancelButtonIndex = options.length - 1

    context.actionSheet().showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex
      },
      (buttonIndex: number) => {
        if (buttonIndex === 0) {
          // Copy text functionality would go here
          Alert.alert('Copied!', 'Message text copied to clipboard')
        }
      }
    )
  }, [])

  if (!sessionId || isNaN(sessionId)) {
    return (
      <Screen preset="fixed" safeAreaEdges={['top']}>
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: 'Chat',
            headerBackTitle: 'Back'
          }}
        />
        <View className="flex-1 items-center justify-center">
          <Text className="text-lg text-red-500">Invalid session ID</Text>
          <Text className="text-gray-500 mt-2">Session ID: {params.id}</Text>
        </View>
      </Screen>
    )
  }

  return (
    <SafeAreaView style={[styles.fill, styles.container]}>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: `Chat #${sessionId}`,
          headerBackTitle: 'Back',
          headerStyle: {
            backgroundColor: '#ffffff'
          },
          headerTintColor: '#3B82F6',
          headerShadowVisible: false
        }}
      />
      <View style={[styles.fill, styles.content]}>
        <ChatRoom
          sessionId={sessionId}
          messages={state.messages}
          onSend={onSend}
          onLoadEarlier={onLoadEarlier}
          loadEarlier={state.loadEarlier}
          isLoadingEarlier={state.isLoadingEarlier}
          onPressAvatar={onPressAvatar}
          onLongPress={onLongPress}
          isTyping={state.isTyping}
          bottomOffset={-insets.bottom}
        />
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  fill: {
    flex: 1
  },
  container: {
    backgroundColor: '#f5f5f5'
  },
  content: {
    backgroundColor: '#ffffff'
  }
})
