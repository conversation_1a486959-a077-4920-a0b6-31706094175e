import { useState } from 'react'
import { View } from 'react-native'

import { Screen } from '@/components'
import { Segmented } from '@/components/Segmented'

const ComponentsTestScreen = () => {
  const [value, setValue] = useState('email')
  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <Segmented
          value={value}
          onChange={setValue}
          options={[
            { icon: 'envelope', label: 'Email login', value: 'email' },
            { icon: 'mobile-screen', label: 'Phone login', value: 'phone' }
          ]}
        />
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
