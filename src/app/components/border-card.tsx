import { Text, View } from 'react-native'

import { Screen } from '@/components'
import { BorderCard } from '@/components/BorderCard'
import { Colors } from '@/theme/colors'

const ComponentsTestScreen = () => {
  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <BorderCard color={Colors.primary}>
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
        <BorderCard color={Colors.warning}>
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
        <BorderCard color={Colors.success}>
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
        <BorderCard position="right">
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
        <BorderCard position="top">
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
        <BorderCard position="bottom">
          <View className="h-20">
            <Text>Hello</Text>
          </View>
        </BorderCard>
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
