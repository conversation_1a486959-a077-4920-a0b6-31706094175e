import { View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { Toast } from 'toastify-react-native'

import { Screen } from '@/components'
import { Badge } from '@/components/Badge'

const ComponentsTestScreen = () => {
  return (
    <Screen>
      <View className="flex flex-col items-start gap-4 p-8">
        <Badge
          number={8}
          trigger={<FA name="bell" size={20} />}
          onPress={() => {
            Toast.success('badge 8')
          }}
        />
        <Badge number={22} trigger={<FA name="bell" size={20} />} />
        <Badge number={102} trigger={<FA name="bell" size={20} />} />
        <Badge number={200} max={300} trigger={<FA name="bell" size={20} />} />
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
