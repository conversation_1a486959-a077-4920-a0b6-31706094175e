import { useState } from 'react'
import { View } from 'react-native'

import { Screen } from '@/components'
import Steps from '@/components/Steps'
import { Text } from '@/components/Text'

const ComponentsTestScreen = () => {
  const [current, setCurrent] = useState(0)
  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <Steps
          items={[
            { title: 'Details' },
            { title: 'Rehab Items' },
            { title: 'Review' }
          ]}
          current={current}
          onChange={setCurrent}
        />
        <Text>Current index: {current}</Text>
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
