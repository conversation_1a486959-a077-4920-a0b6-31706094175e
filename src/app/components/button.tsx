import { View } from 'react-native'

import { But<PERSON>, Screen } from '@/components'

const ComponentsTestScreen = () => {
  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <Button leftIcon="user">common</Button>
        <Button leftIcon="save" variant="primary">
          primary
        </Button>
        <Button leftIcon="save" variant="primary" size="sm">
          primary sm
        </Button>
        <Button leftIcon="save" variant="primary" size="xs">
          primary xs
        </Button>
        <Button variant="primary" disabled>
          disabled
        </Button>
        <Button variant="primary" loading className="mx-4">
          loading
        </Button>
        <Button leftIcon="save" variant="primary" block={false}>
          inline
        </Button>
        <Button variant="primary-light">primary-light</Button>
        <Button variant="approval">approval</Button>
        <Button variant="outline">outline</Button>
        <Button variant="tenant">tenant</Button>
        <Button variant="warning">warning</Button>
        <Button variant="default">default</Button>
        <Button variant="danger">danger</Button>
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
