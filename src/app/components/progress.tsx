import { View } from 'react-native'

import { Screen } from '@/components'
import { Progress } from '@/components/Progress'
import { Colors } from '@/theme/colors'

const ComponentsTestScreen = () => {
  return (
    <Screen>
      <View className="flex flex-col items-start gap-4 p-8">
        <Progress percentage={30} />
        <Progress percentage={50} color={Colors.primary} />
        <Progress percentage={80} color={Colors.info} />
        <Progress percentage={100} color={Colors.success} />
        <Progress percentage={30} className="h-[10px]" radius={5} />
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
