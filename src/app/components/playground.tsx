import React, { useState } from 'react'
import { <PERSON>rollView, View } from 'react-native'
import { router } from 'expo-router'

import { Button, Screen } from '@/components'
import { AddPhotos } from '@/components/AddPhotos'

const ComponentsTestScreen = () => {
  const [photos, setPhotos] = useState<any[]>([])

  return (
    <Screen>
      <ScrollView className="flex-1" contentContainerClassName="p-4">
        <View className="flex flex-col gap-4">
          <Button
            onPress={() => {
              router.push('/components/alert')
            }}
          >
            Alert
          </Button>
          <Button
            onPress={() => {
              router.push('/components/badge')
            }}
          >
            Badge
          </Button>
          <Button
            onPress={() => {
              router.push('/components/border-card')
            }}
          >
            BorderCard
          </Button>
          <Button
            onPress={() => {
              router.push('/components/button')
            }}
          >
            Button
          </Button>
          <Button
            onPress={() => {
              router.push('/components/form')
            }}
          >
            Form
          </Button>
          <Button
            onPress={() => {
              router.push('/components/progress')
            }}
          >
            Progress
          </Button>
          <Button
            onPress={() => {
              router.push('/components/radio-button-group')
            }}
          >
            RadioButtonGroup
          </Button>
          <Button
            onPress={() => {
              router.push('/components/steps')
            }}
          >
            Steps
          </Button>
          <Button
            onPress={() => {
              router.push('/components/segmented')
            }}
          >
            Segmented
          </Button>
          <Button
            onPress={() => {
              router.push('/components/camera')
            }}
          >
            Camera
          </Button>
        </View>
        <View className="mt-8">
          <AddPhotos value={photos} onChange={setPhotos} />
        </View>
      </ScrollView>
    </Screen>
  )
}

export default ComponentsTestScreen
