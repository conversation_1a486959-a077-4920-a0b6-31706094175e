import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>View, Text, View } from 'react-native'
import { Image } from 'expo-image'

import { Button } from '@/components/Button'
import { Camera } from '@/components/Camera'
import { Screen } from '@/components/Screen'

export default function CameraScreen() {
  const [showCamera, setShowCamera] = useState(false)
  const [capturedImages, setCapturedImages] = useState<string[]>([])

  const handlePhotoTaken = (uri: string) => {
    setCapturedImages(prev => [...prev, uri])
    setShowCamera(false)
    Alert.alert('Success', 'Photo captured successfully!')
  }

  const handleCloseCamera = () => {
    setShowCamera(false)
  }

  const clearImages = () => {
    setCapturedImages([])
  }

  if (showCamera) {
    return (
      <Camera
        onPhotoTaken={handlePhotoTaken}
        onClose={handleCloseCamera}
        className="flex-1"
      />
    )
  }

  return (
    <Screen preset="scroll" contentContainerClass="flex-1">
      <View className="p-4">
        <Text className="mb-4 text-2xl font-bold">Camera Demo</Text>

        <View className="mb-6">
          <Text className="mb-2 text-lg">Take Photos</Text>
          <Text className="mb-4 text-gray-600">
            Use the camera to capture photos. You can take multiple photos and
            view them below.
          </Text>

          <View className="flex-row space-x-3">
            <Button
              onPress={() => setShowCamera(true)}
              variant="primary"
              className="flex-1"
            >
              Open Camera
            </Button>

            {capturedImages.length > 0 && (
              <Button onPress={clearImages} variant="danger" size="sm">
                Clear All
              </Button>
            )}
          </View>
        </View>

        {capturedImages.length > 0 && (
          <View className="mb-6">
            <Text className="mb-3 text-lg">
              Captured Photos ({capturedImages.length})
            </Text>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="mb-4"
            >
              <View className="flex-row space-x-3">
                {capturedImages.map((uri, index) => (
                  <View
                    key={index}
                    className="h-24 w-24 overflow-hidden rounded-lg border border-gray-200"
                  >
                    <Image
                      source={{ uri }}
                      className="h-full w-full"
                      contentFit="cover"
                    />
                  </View>
                ))}
              </View>
            </ScrollView>
          </View>
        )}

        <View className="bg-gray-50 rounded-lg p-4">
          <Text className="mb-2 text-lg font-semibold">Features</Text>
          <View className="space-y-2">
            <Text className="text-gray-700">
              • Take photos using device camera
            </Text>
            <Text className="text-gray-700">• Preview captured images</Text>
            <Text className="text-gray-700">• Retake photos if needed</Text>
            <Text className="text-gray-700">• Multiple photo capture</Text>
            <Text className="text-gray-700">• Permission handling</Text>
          </View>
        </View>
      </View>
    </Screen>
  )
}
