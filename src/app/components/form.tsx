import type { FieldValues } from 'react-hook-form'
import { View } from 'react-native'
import { Toast } from 'toastify-react-native'

import {
  Button,
  Checkbox,
  CheckboxGroup,
  Form,
  FormItem,
  Input,
  PasswordInput,
  Screen,
  Select
} from '@/components'
import { CheckboxButtonGroup } from '@/components/CheckboxButtonGroup'
import { VerifyCodeInput } from '@/components/VerifyCodeInput'

const ComponentsTestScreen = () => {
  const form = Form.useForm()

  const handleFinish = (values: FieldValues) => {
    Toast.info(JSON.stringify(values))
  }

  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <Form form={form} onFinish={handleFinish}>
          <FormItem
            name="email"
            label="Email"
            rules={{
              required: {
                value: true,
                message: 'Email is required'
              }
            }}
          >
            <Input
              placeholder="Enter your email"
              keyboardType="email-address"
            />
          </FormItem>
          <FormItem
            name="password"
            label="Password"
            rules={{
              required: {
                value: true,
                message: 'Password is required'
              }
            }}
          >
            <PasswordInput placeholder="Enter your password" />
          </FormItem>
          <FormItem name="sex" label="Sex">
            <Select
              placeholder="Select your sex"
              options={[
                { label: 'Male', value: 'male' },
                { label: 'Female', value: 'female' }
              ]}
            />
          </FormItem>
          <FormItem name="categories" label="Service Categories">
            <CheckboxGroup
              options={[
                { label: 'Plumbing', value: 'plumbing' },
                { label: 'Electrical', value: 'electrical' },
                { label: 'HVAC', value: 'hvac' },
                { label: 'Carpentry', value: 'carpentry' },
                { label: 'Flooring', value: 'flooring' },
                { label: 'Painting', value: 'painting' },
                { label: 'Landscaping', value: 'landscaping' },
                { label: 'Cleaning', value: 'cleaning' },
                { label: 'Other', value: 'other' }
              ]}
            />
          </FormItem>
          <FormItem name="categories1" label="Service Categories">
            <CheckboxButtonGroup
              options={[
                { label: 'Plumbing', value: 'plumbing' },
                { label: 'Electrical', value: 'electrical' },
                { label: 'HVAC', value: 'hvac' },
                { label: 'Carpentry', value: 'carpentry' },
                { label: 'Flooring', value: 'flooring' },
                { label: 'Painting', value: 'painting' },
                { label: 'Landscaping', value: 'landscaping' },
                { label: 'Cleaning', value: 'cleaning' },
                { label: 'Other', value: 'other' }
              ]}
            />
          </FormItem>
          <FormItem name="code">
            <VerifyCodeInput />
          </FormItem>
          <FormItem name="remember">
            <Checkbox>Remember me</Checkbox>
          </FormItem>
          <Button variant="primary" onPress={form.submit}>
            Submit
          </Button>
        </Form>
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen
