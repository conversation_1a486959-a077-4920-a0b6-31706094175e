import { useState } from 'react'
import { View } from 'react-native'
import { Text } from 'react-native'
import clsx from 'clsx'

import { Screen } from '@/components'
import { RadioButtonGroup } from '@/components/RadioButtonGroup'
import { Colors } from '@/theme/colors'

const ComponentsTestScreen = () => {
  const [selected, setSelected] = useState('')
  return (
    <Screen>
      <View className="flex flex-col gap-4 p-4">
        <RadioButtonGroup
          value={selected}
          onChange={setSelected}
          items={[
            {
              label: 'All Requests',
              value: '',
              activeColor: Colors.tenant,
              activeBgColor: Colors['tenant-light'],
              rightNode(active) {
                return (
                  <CountItem
                    num={4}
                    activeClassName="bg-tenant"
                    active={active}
                  />
                )
              }
            },
            {
              label: 'Open',
              value: 'open',
              activeColor: Colors.warning,
              activeBgColor: Colors['warning-light'],
              rightNode(active) {
                return (
                  <CountItem
                    num={1}
                    activeClassName="bg-warning"
                    active={active}
                  />
                )
              }
            },
            {
              label: 'In Progress',
              value: 'progress',
              activeColor: Colors.info,
              activeBgColor: Colors['info-light'],
              rightNode(active) {
                return (
                  <CountItem
                    num={1}
                    activeClassName="bg-info"
                    active={active}
                  />
                )
              }
            },
            {
              label: 'Completed',
              value: 'completed',
              activeColor: Colors.success,
              activeBgColor: Colors['success-light'],
              rightNode(active) {
                return (
                  <CountItem
                    num={1}
                    activeClassName="bg-success"
                    active={active}
                  />
                )
              }
            },
            {
              label: 'Cancelled',
              value: 'cancelled',
              activeColor: Colors.danger,
              activeBgColor: Colors['danger-light'],
              rightNode(active) {
                return (
                  <CountItem
                    num={1}
                    activeClassName="bg-danger"
                    active={active}
                  />
                )
              }
            }
          ]}
        />
        <View className="mt-5">
          <Text>Current: {selected}</Text>
        </View>
      </View>
    </Screen>
  )
}

export default ComponentsTestScreen

function CountItem({
  active,
  num,
  activeClassName
}: {
  active: boolean
  num: number
  activeClassName: string
}) {
  return (
    <Text
      className={clsx(
        'ml-[6px] h-5 w-5 rounded-full text-center text-[0.7rem] leading-5',
        active ? 'text-white' : 'text-gray',
        active ? activeClassName : 'bg-light-gray'
      )}
    >
      {num}
    </Text>
  )
}
