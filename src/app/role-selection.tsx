import { type ComponentProps } from 'react'
import { Pressable, Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import { router } from 'expo-router'

import { Screen } from '@/components'
import { BorderCard } from '@/components/BorderCard'
import { type UserRole } from '@/store'
import { Colors } from '@/theme/colors'
import { crossPlatformShadow } from '@/theme/shadow'

const RoleSelectionPage = () => {
  // const setUserRole = useAuth(state => state.setUserRole)

  const handleRoleSelect = (role: UserRole) => {
    // setUserRole(role)
    router.push(`/auth/login?type=${role}`)
  }

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="pt-10">
        <Pressable
          onPress={() => {
            router.push('/components/playground')
          }}
        >
          <View className="mb-4 flex flex-row items-center justify-center">
            <FA
              name="house"
              className="mr-3 !text-primary"
              size={36}
              color="black"
            />
            <Text className="text-[28px] font-bold text-primary">Paibox</Text>
          </View>
        </Pressable>
        <View className="mb-2">
          <Text className="text-center text-2xl text-dark">
            Welcome to PaiBox
          </Text>
        </View>
        <View className="mb-6">
          <Text className="text-center text-base text-gray">
            Select your portal to continue
          </Text>
        </View>
        <View className="my-6 flex flex-col gap-y-4 px-4">
          <RoleChooseItem
            name="Vendor"
            desc="Provide services, manage work orders, and invoices"
            icon="hammer"
            color={Colors.primary}
            onPress={() => handleRoleSelect('vendor')}
          />
          <RoleChooseItem
            name="Property Manager"
            desc="Manage properties, projects, vendors, and clients"
            icon="building"
            color="#1e90ff"
            onPress={() => handleRoleSelect('property-manager')}
          />
          <RoleChooseItem
            name="Property Owner"
            desc="Manage your properties, investments, and approvals"
            icon="house"
            color="#9c27b0"
            onPress={() => handleRoleSelect('property-owner')}
          />
          <RoleChooseItem
            name="Tenant"
            desc="View and manage your rental properties and requests"
            icon="key"
            color="#4caf50"
            onPress={() => handleRoleSelect('tenant')}
          />
        </View>
      </View>
    </Screen>
  )
}

export default RoleSelectionPage

const RoleChooseItem = ({
  name,
  desc,
  icon,
  color,
  onPress
}: {
  name: string
  desc: string
  color: string
  icon: ComponentProps<typeof FA>['name']
  onPress: () => void
}) => {
  return (
    <Pressable onPress={onPress}>
      <BorderCard
        className="flex flex-row items-center p-6"
        position="right"
        color={color}
        style={[crossPlatformShadow('lg')]}
      >
        <View
          className="flex-inline h-[64px] w-[64px] items-center justify-center rounded-2xl"
          style={{
            backgroundColor: color
          }}
        >
          <FA name={icon} size={26} color="white" />
        </View>
        <View className="ml-5 mr-3 flex flex-1 flex-col">
          <Text className="mb-[6px] text-lg font-bold leading-[27px] text-dark">
            {name}
          </Text>
          <Text className="leading--[21px] text-sm text-gray">{desc}</Text>
        </View>
        <FA name="angle-right" size={24} color={Colors.gray} />
      </BorderCard>
    </Pressable>
  )
}
