import React from 'react'
import { <PERSON>, <PERSON><PERSON> } from 'expo-router'

import { ThemedText } from '@/components/ThemedText'
import { ThemedView } from '@/components/ThemedView'

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <ThemedView className="flex-1 items-center justify-center p-5">
        <ThemedText type="title">This screen does not exist.</ThemedText>
        <Link href="/role-selection" className="mt-4 py-4">
          <ThemedText type="link">Go to role selection screen!</ThemedText>
        </Link>
      </ThemedView>
    </>
  )
}
