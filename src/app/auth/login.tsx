import { useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import FA from '@expo/vector-icons/FontAwesome6'
import type { Href } from 'expo-router'
import { Link, router, useLocalSearchParams, useNavigation } from 'expo-router'

import { Screen } from '@/components'
import EmailLoginForm from '@/components/auth/EmailLoginForm'
import PhoneLoginForm from '@/components/auth/PhoneLoginForm'
import { Segmented } from '@/components/Segmented'
import type { APIRole } from '@/store'
import { useAuth, type UserRole } from '@/store'

const RoleDescriptions: Record<
  UserRole,
  {
    apiRole: APIRole
    name: string
    welcome: string
  }
> = {
  'property-manager': {
    apiRole: 'pm',
    name: 'Property Manager',
    welcome: 'Login to manage your properties and projects'
  },
  'property-owner': {
    apiRole: 'property_owner',
    name: 'Property Owner',
    welcome: 'Login to view and manage your properties'
  },
  'tenant': {
    apiRole: 'tenant',
    name: 'Tenant',
    welcome: 'Login to manage your rental experience'
  },
  'vendor': {
    apiRole: 'vendor',
    name: 'Vendor',
    welcome: 'Login to manage your service business'
  }
}

export default function PropertyManagerLoginScreen() {
  const { type } = useLocalSearchParams()
  const navigation = useNavigation()
  const roleDescription = RoleDescriptions[type as UserRole]
  const setUserRole = useAuth(state => state.setUserRole)

  const [loginType, setLoginType] = useState<'email' | 'phone'>('email')

  const onLoggedIn = () => {
    setUserRole(type as UserRole)
    setTimeout(() => {
      router.replace(`/${type}/(tabs)` as Href)
    }, 100)
  }

  useEffect(() => {
    ;(navigation as any).setOptions({ title: `${roleDescription.name} Login` })
  }, [type])

  return (
    <Screen preset="scroll" safeAreaEdges={['bottom']} backgroundColor="white">
      <View className="flex-1 px-6 py-8">
        <View className="mb-4 flex flex-row items-center justify-center">
          <FA
            name="house"
            className="mr-3 !text-primary"
            size={36}
            color="black"
          />
          <Text className="text-[28px] font-bold text-primary">Paibox</Text>
        </View>
        <View className="mb-2">
          <Text className="text-center text-2xl text-dark">
            Welcome {roleDescription.name}
          </Text>
        </View>
        <View className="mb-8">
          <Text className="text-center text-gray">
            {roleDescription.welcome}
          </Text>
        </View>
        <Segmented
          className="mb-6"
          value={loginType}
          onChange={setLoginType}
          options={[
            { icon: 'envelope', label: 'Email login', value: 'email' },
            { icon: 'mobile-screen', label: 'Phone login', value: 'phone' }
          ]}
        />
        {loginType === 'email' ? (
          <EmailLoginForm
            role={roleDescription.apiRole}
            onLoggedIn={onLoggedIn}
          />
        ) : (
          <PhoneLoginForm
            role={roleDescription.apiRole}
            onLoggedIn={onLoggedIn}
          />
        )}
        <View className="mt-8 items-center">
          <Text className="text-sm text-gray">
            By continuing, you agree to our{' '}
            <Link
              href="/common/terms-of-service"
              className="text-primary underline"
            >
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              href="/common/privacy-policy"
              className="text-primary underline"
            >
              Privacy Policy
            </Link>
          </Text>
        </View>
      </View>
    </Screen>
  )
}
