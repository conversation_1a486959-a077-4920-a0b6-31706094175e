import { useState } from 'react'
import { Alert, View } from 'react-native'
import { router } from 'expo-router'

import { Button, Screen, Text } from '@/components'
import { TextField } from '@/components/TextField'

export default function RegisterScreen() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    companyName: '',
    password: '',
    confirmPassword: ''
  })
  const [loading, setLoading] = useState(false)

  const handleRegister = async () => {
    const {
      firstName,
      lastName,
      email,
      phone,
      companyName,
      password,
      confirmPassword
    } = formData

    if (
      !firstName ||
      !lastName ||
      !email ||
      !phone ||
      !companyName ||
      !password ||
      !confirmPassword
    ) {
      Alert.alert('Error', 'Please fill in all required fields')
      return
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match')
      return
    }

    setLoading(true)
    try {
      // TODO: Call registration API
      Alert.alert('Registration Successful', 'Please sign in to your account', [
        {
          text: 'OK',
          onPress: () => router.back()
        }
      ])
      // eslint-disable-next-line unused-imports/no-unused-vars
    } catch (err) {
      Alert.alert('Error', 'An error occurred during registration')
    } finally {
      setLoading(false)
    }
  }

  const updateFormData = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }

  return (
    <Screen preset="scroll" safeAreaEdges={['bottom']} backgroundColor="white">
      <View className="flex-1 px-6 py-8">
        <View className="mb-8">
          <Text className="mb-2 text-3xl font-bold text-dark">
            Create Property Manager Account
          </Text>
          <Text className="text-base text-gray">
            Fill in the information to complete registration
          </Text>
        </View>

        <View className="mb-6 flex-row gap-2">
          <TextField
            className="flex-1"
            label="First Name"
            placeholder="Enter your first name"
            value={formData.firstName}
            onChangeText={value => updateFormData('firstName', value)}
          />
          <TextField
            className="flex-1"
            label="Last Name"
            placeholder="Enter your last name"
            value={formData.lastName}
            onChangeText={value => updateFormData('lastName', value)}
          />
        </View>

        <View className="mb-6">
          <TextField
            label="Email"
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={value => updateFormData('email', value)}
            autoCapitalize="none"
            keyboardType="email-address"
          />
        </View>

        <View className="mb-6">
          <TextField
            label="Phone"
            placeholder="Enter phone number"
            value={formData.phone}
            onChangeText={value => updateFormData('phone', value)}
            keyboardType="phone-pad"
          />
        </View>

        <View className="mb-6">
          <TextField
            label="Company Name"
            placeholder="Enter company name"
            value={formData.companyName}
            onChangeText={value => updateFormData('companyName', value)}
          />
        </View>

        <View className="mb-6">
          <TextField
            label="Password"
            placeholder="Enter password"
            value={formData.password}
            onChangeText={value => updateFormData('password', value)}
            secureTextEntry
          />
        </View>

        <View className="mb-8">
          <TextField
            label="Confirm Password"
            placeholder="Re-enter password"
            value={formData.confirmPassword}
            onChangeText={value => updateFormData('confirmPassword', value)}
            secureTextEntry
          />
        </View>

        <Button
          variant="primary"
          onPress={handleRegister}
          loading={loading}
          disabled={loading}
          className="mb-4"
        >
          Sign Up
        </Button>

        <Button variant="outline" onPress={() => router.back()}>
          Already have an account? Sign in
        </Button>
      </View>
    </Screen>
  )
}
