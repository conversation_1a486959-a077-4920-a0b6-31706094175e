import { FlatList, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Screen } from '@/components'
import { Button } from '@/components'
import { CardWrapper } from '@/components/CardWrapper'

const data = [
  {
    name: 'Deck Board Replacement',
    description: 'Pressure-treated lumber, 2x6, 16ft',
    quantity: 24,
    amount: 1200
  },
  {
    name: 'Deck Staining',
    description: 'Premium weather-resistant stain application',
    quantity: 1,
    amount: 800
  },
  {
    name: 'Railing Repair',
    description: 'Replacement of damaged railing posts and balusters',
    quantity: 1,
    amount: 950
  },
  {
    name: 'Deck Cleaning',
    description: 'Power washing and cleaning services',
    quantity: 1,
    amount: 350
  }
]

const VendorInvoiceDetailPage = () => {
  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="p-4">
        <View className="mb-4">
          <CardWrapper>
            <View className="p-4">
              <View className="flex-row justify-between">
                <View>
                  <Text className="mb-1 text-2xl font-bold">INV-2023-001</Text>
                  <View className="flex-row items-center self-start rounded-full bg-[rgba(46,125,50,0.1)] px-3 py-[2px]">
                    <FontAwesome6
                      name="check-circle"
                      className="text-success"
                    ></FontAwesome6>
                    <Text className="ml-1 text-xs text-success">Paid</Text>
                  </View>
                </View>
                <View className="flex">
                  <Text className="text-gary-600 self-end text-sm text-gray-600">
                    Amount
                  </Text>
                  <Text className="text-2xl font-bold text-primary">
                    $3,500.00
                  </Text>
                </View>
              </View>
              <View className="mt-3 flex-row gap-4">
                <View>
                  <Text className="mb-1 text-xs text-gray-600">
                    Issued Date
                  </Text>
                  <Text>Jul 10, 2023</Text>
                </View>
                <View>
                  <Text className="mb-1 text-xs text-gray-600">
                    Issued Date
                  </Text>
                  <Text>Jul 10, 2023</Text>
                </View>
              </View>
            </View>
          </CardWrapper>
        </View>
        <View className="mb-4">
          <CardWrapper>
            <View className="p-4">
              <InvoiceTable />
              <CreditInfo />
              <View className="mt-8 flex-row justify-between">
                <Button leftIcon={'download'}>Download PDF</Button>
                <Button variant="primary" leftIcon={'history'}>
                  View History
                </Button>
              </View>
            </View>
          </CardWrapper>
        </View>
        <View>
          <CardWrapper>
            <View className="p-4">
              <Text className="mb-2 text-base font-semibold">Notes</Text>
              <Text className="text-sm text-gray-700">
                Thank you for your business! Payment was received on time.
                Please let us know if you have any questions about this invoice.
              </Text>
            </View>
          </CardWrapper>
        </View>
      </View>
    </Screen>
  )
}

const InvoiceTable = () => {
  const subtotal = data.reduce((sum, item) => sum + item.amount, 0)
  const tax = subtotal * 0.06
  const total = subtotal + tax

  return (
    <View>
      {/* Header */}
      <Text className="mb-4 border-b border-b-gray-200 pb-2 text-base font-semibold">
        Invoice Items
      </Text>
      <View className="flex-row justify-between border-b border-b-gray-200">
        <Text className="w-[50%] px-2 py-3 text-xs font-medium text-gray-700">
          Item
        </Text>
        <Text className="w-[25%] px-2 py-3 text-center text-xs font-medium text-gray-700">
          Quantity
        </Text>
        <Text className="w-[25%] px-2 py-3 text-center text-xs font-medium text-gray-700">
          Amount
        </Text>
      </View>

      {/* Items */}
      <FlatList
        data={data}
        keyExtractor={(item, index) => index.toString()}
        ItemSeparatorComponent={() => (
          <View className="my-2 h-[1px] bg-gray-200" />
        )}
        renderItem={({ item }) => (
          <View className="flex-row items-center justify-between">
            <View className="w-[50%] px-2 py-3">
              <Text className="font-medium">{item.name}</Text>
              <Text className="text-xs text-gray-600">{item.description}</Text>
            </View>
            <Text className="w-[25%] px-2 py-3 text-center">
              {item.quantity}
            </Text>
            <Text className="w-[25%] px-2 py-3 text-right">
              ${item.amount.toFixed(2)}
            </Text>
          </View>
        )}
      />

      {/* Summary */}
      <View className="border-t border-t-gray-200 pt-6">
        <View className="mb-1 flex-row justify-between">
          <Text className="text-gray-600">Subtotal</Text>
          <Text className="text-gray-600">${subtotal.toFixed(2)}</Text>
        </View>
        <View className="mb-2 flex-row justify-between">
          <Text className="text-gray-600">Tax (6%)</Text>
          <Text className="text-gray-600">${tax.toFixed(2)}</Text>
        </View>
        <View className="flex-row justify-between border-t border-t-gray-200 pt-2">
          <Text className="text-lg font-bold">Total</Text>
          <Text className="text-lg font-bold">${total.toFixed(2)}</Text>
        </View>
      </View>
    </View>
  )
}

const CreditInfo = () => {
  return (
    <View className="mt-4 rounded-default bg-primary-light p-4">
      <View className="mb-3 flex-row items-center gap-2">
        <FontAwesome6 name="credit-card" className="text-white" />
        <Text className="text-base font-semibold text-white">
          Payment Information
        </Text>
      </View>

      <View className="mb-2 flex-row justify-between">
        <Text className="text-sm text-white">Payment Method:</Text>
        <Text className="text-white">Credit Card</Text>
      </View>

      <View className="mb-2 flex-row justify-between">
        <Text className="text-sm text-white">Transaction ID:</Text>
        <Text className="text-sm text-white">TXN-872365</Text>
      </View>

      <View className="mb-2 flex-row justify-between">
        <Text className="text-sm text-white">Payment Date:</Text>
        <Text className="text-sm text-white">Jul 15, 2023</Text>
      </View>
    </View>
  )
}

export default VendorInvoiceDetailPage
