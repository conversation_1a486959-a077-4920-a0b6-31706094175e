import { useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'

import { Progress, Screen, Segmented } from '@/components'
import { CardWrapper } from '@/components/CardWrapper'
import classNames from '@/utils/classname'

const VendorAnalyticsPage = () => {
  const [value, setValue] = useState('7')

  const metrics = [
    {
      title: 'TOTAL REVENUE',
      value: '$12,750',
      valueColor: 'text-green-600',
      change: '+22.6%',
      icon: 'arrow-up',
      changeColor: 'text-green-500'
    },
    {
      title: 'PROJECTS',
      value: '32',
      valueColor: 'text-blue-600',
      change: '+15.4%',
      icon: 'arrow-up',
      changeColor: 'text-green-500'
    },
    {
      title: 'AVG RATING',
      value: '4.9',
      valueColor: 'text-amber-500',
      change: '+0.2',
      icon: 'arrow-up',
      changeColor: 'text-green-500'
    },
    {
      title: 'ON-TIME RATE',
      value: '96%',
      valueColor: 'text-green-600',
      change: '+3%',
      icon: 'arrow-up',
      changeColor: 'text-green-500'
    }
  ]

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="p-4">
        <View>
          <Segmented
            value={value}
            onChange={setValue}
            options={[
              { label: '7D', value: '7' },
              { label: '30D', value: '30' },
              { label: '90D', value: '90' },
              { label: '1Y', value: '365' }
            ]}
          />
        </View>
        <View>
          <KeyMetricsCard metrics={metrics} />
        </View>
        <View>
          <ProjectPerformanceCard />
        </View>
        <View>
          <RatingCard />
        </View>
      </View>
    </Screen>
  )
}

function KeyMetricsCard({
  metrics
}: {
  metrics: {
    title: string
    value: string
    valueColor: string
    change: string
    icon: string
    changeColor: string
  }[]
}) {
  return (
    <CardWrapper>
      <View className="p-4">
        {/* Header */}
        <View className="flex-row items-center space-x-2">
          <FontAwesome6 name="chart-bar" size={16} color="#f59e0b" />
          <Text className="text-base font-semibold text-black">
            Key Metrics
          </Text>
        </View>

        {/* Metrics Grid */}
        <View className="-mx-2 flex-row flex-wrap">
          {metrics.map((metric, index) => (
            <View key={index} className="w-1/2 px-2 py-2">
              <View className="space-y-1 rounded-xl border border-gray-200 p-3">
                <Text className={`text-xl font-semibold ${metric.valueColor}`}>
                  {metric.value}
                </Text>
                <Text className="text-gray-500 text-xs tracking-wide">
                  {metric.title}
                </Text>
                <View className="flex-row items-center space-x-1">
                  <FontAwesome6
                    name={metric.icon}
                    size={10}
                    className="text-green-500"
                  />
                  <Text className={`text-xs ${metric.changeColor}`}>
                    {metric.change}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    </CardWrapper>
  )
}

function ProjectPerformanceCard() {
  const items = [
    {
      title: 'Completed Projects',
      description: 'This month',
      value: '8',
      diff: 0.33
    },
    {
      title: 'Active Projects',
      description: 'Currently in progress',
      value: '3',
      diff: 0.12
    },
    {
      title: 'Average Project Value',
      description: 'Per project',
      value: '$1,594',
      diff: 0.12
    },
    {
      title: 'Completion Rate',
      description: 'Projects finished on time',
      value: '96%'
    }
  ]
  return (
    <CardWrapper>
      <View className="p-4">
        {/* Header */}
        <View className="mb-4 flex-row items-center">
          <FontAwesome6 name="tasks" size={16} color="#3b82f6" />
          <Text className="text-base font-semibold text-black">
            Project Performance
          </Text>
        </View>

        {items.map((item, index) => (
          <AnalayticsListItem key={index} {...item} />
        ))}
      </View>
    </CardWrapper>
  )
}

function AnalayticsListItem({
  title,
  description,
  value,
  diff
}: {
  title: string
  description: string
  value: string
  diff?: number
}) {
  return (
    <View className="flex-row items-center justify-between border-b border-gray-100 py-4">
      <View>
        <Text className="text-sm font-medium text-black">{title}</Text>
        <Text className="text-gray-400 mt-0.5 text-xs">{description}</Text>
      </View>
      <View className="flex items-end justify-center">
        <Text className="text-sm font-semibold text-black">{value}</Text>
        {diff && (
          <Text
            className={classNames(
              'mt-0.5 text-xs',
              diff > 0 ? 'text-green-500' : 'text-red-500'
            )}
          >
            {diff > 0 ? '+' : ''}
            {diff * 100}%
          </Text>
        )}
      </View>
    </View>
  )
}

function RatingCard({
  rating = 4.9,
  ratingCount = 48,
  ratingCategoryCount = {
    '5': 40,
    '4': 8,
    '3': 0
  }
}: {
  rating?: number
  ratingCount?: number
  ratingCategoryCount?: Record<string, number>
}) {
  return (
    <CardWrapper>
      <View className="p-4">
        <View className="mb-4 flex-row items-center">
          <FontAwesome6 name="star" size={16} className="text-warning" />
          <Text className="text-base font-semibold text-black">
            Project Performance
          </Text>
        </View>
        <View className="flex items-center">
          <Text className="text-4xl font-bold text-warning">{rating}</Text>
          <View className="mb-1 flex-row items-center">
            <Rating />
          </View>
          <Text className="text-[14px] text-gray">
            Based on {ratingCount} reviews
          </Text>
        </View>
        <View>
          {Object.entries(ratingCategoryCount).map(([key, value]) => (
            <View key={key} className="flex-row items-center">
              <Text>{key} stars</Text>
              <View className="flex-1">
                <Progress percentage={value / ratingCount} />
              </View>
              <Text>({value})</Text>
            </View>
          ))}
        </View>
      </View>
    </CardWrapper>
  )
}

function Rating() {
  return (
    <View className="mb-1 flex-row items-center">
      <FontAwesome6 name="star" size={16} className="text-warning" />
      <FontAwesome6 name="star" size={16} className="text-warning" />
      <FontAwesome6 name="star" size={16} className="text-warning" />
      <FontAwesome6 name="star" size={16} className="text-warning" />
      <FontAwesome6 name="star" size={16} className="text-warning" />
    </View>
  )
}

export default VendorAnalyticsPage
