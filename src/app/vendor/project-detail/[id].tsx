import { useMemo } from 'react'
import { View } from 'react-native'
import { useRequest } from 'ahooks'
import { Stack, useLocalSearchParams } from 'expo-router'

import { Screen } from '@/components'
import AcceptProject from '@/components/vendor/project-detail/AcceptProject'
import ApprovedProject from '@/components/vendor/project-detail/ApprovedProject'
import CompleteProject from '@/components/vendor/project-detail/CompleteProject'
import NewProject from '@/components/vendor/project-detail/NewProject'
import ProjectInfo from '@/components/vendor/project-detail/ProjectInfo'
import StartedProject from '@/components/vendor/project-detail/StartedProject'
import { client } from '@/services/api'
import { useAuth } from '@/store/auth'
import type { DICT_ITEM_PROJECT_STATUS } from '@/types'

const VendorProjectDetailPage = () => {
  const { id } = useLocalSearchParams()
  const { user } = useAuth()

  const { data } = useRequest(
    async () => {
      const res = await client.GET('/api/v1/vendor/project/{projectId}', {
        params: {
          path: {
            projectId: Number.parseInt(id as string, 10)
          }
        }
      })
      return res.data?.data
    },
    { refreshDeps: [id] }
  )

  const projectStatus = data?.status as DICT_ITEM_PROJECT_STATUS

  const list = useMemo(() => {
    return data?.items || []
  }, [data])

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: data?.projectName || 'Project Details'
        }}
      />
      <View className="p-4">
        <ProjectInfo project={data} />
        {projectStatus === 'PENDING_QUOTES' && (
          <NewProject
            projectItemList={list}
            projectId={data?.projectId}
            vendorId={user?.userId}
            projectData={data}
          />
        )}
        {projectStatus === 'SUBMITTED' && (
          <ApprovedProject
            projectItemList={list}
            projectId={data?.projectId}
            vendorId={user?.userId}
          />
        )}
        {projectStatus === 'IN_PROGRESS' && (
          <AcceptProject
            projectItemList={list}
            projectId={data?.projectId}
            vendorId={user?.userId}
          />
        )}
        {projectStatus === 'DRAFT' && (
          <StartedProject
            projectItemList={list}
            projectId={data?.projectId}
            vendorId={user?.userId}
          />
        )}
        {projectStatus === 'COMPLETED' && (
          <CompleteProject projectItemList={list} />
        )}
        {projectStatus === 'CANCELLED' && (
          <CompleteProject projectItemList={list} />
        )}
      </View>
    </Screen>
  )
}

export default VendorProjectDetailPage
