import { useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { router } from 'expo-router'

import { Button, Screen } from '@/components'
import { CardWrapper } from '@/components/CardWrapper'
import { FilterTabs } from '@/components/FilterTabs'
import { Search } from '@/components/Search'

const tabs = [
  { label: 'All', value: 'all', labelCount: 8 },
  { label: 'Paid', value: 'paid', labelCount: 5 },
  { label: 'Pending', value: 'pending', labelCount: 2 },
  { label: 'Overdue', value: 'overdue', labelCount: 1 }
]

const VendorInvoicesPage = () => {
  const [filter, setFilter] = useState<string>('all')

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="p-4">
        <View className="mb-4 flex-row gap-3">
          <View className="flex-1 items-center bg-white p-4 shadow-sm">
            <Text className="text-2xl font-bold text-success">$8450</Text>
            <Text className="text-xs text-gray-600">Paid</Text>
          </View>
          <View className="flex-1 items-center bg-white p-4 shadow-sm">
            <Text className="text-2xl font-bold text-warning">$3250</Text>
            <Text className="text-xs text-gray-600">Pending</Text>
          </View>
        </View>
        <View>
          <FilterTabs
            options={tabs}
            value={filter}
            onChange={v => {
              setFilter(v)
            }}
          />
        </View>
        <View className="mb-4 flex-row justify-between">
          <Button leftIcon={'download'} variant={'outline'} size="xs">
            Download All Invoices
          </Button>
          <Button leftIcon={'check-square'} variant={'outline'} size="xs">
            Select Multiple
          </Button>
        </View>
        <View>
          <Search />
        </View>
        <View>
          <InvoiceCard />
        </View>
      </View>
    </Screen>
  )
}

const InvoiceCard = () => {
  const handleDownload = () => {
    // FIXME: download invoice
    console.log('download')
  }

  return (
    <View>
      <CardWrapper borderLeftColor="border-l-success">
        <View className="flex-row items-center justify-between border-b-[1px] border-gray-100 p-4">
          <View>
            <Text className="text-base font-semibold">INV-2023-001</Text>
            <Text className="text-xs text-gray-600">Issued: Jul 10, 2023</Text>
          </View>
          <View>
            <Text className="text-base font-bold text-primary">$3,500</Text>
          </View>
        </View>

        <View className="p-4">
          <View className="flex-row gap-3">
            <View className="flex h-10 w-10 items-center justify-center rounded-[8px] bg-primary-light">
              <FontAwesome6 name="hammer" color="white"></FontAwesome6>
            </View>
            <View className="flex-1">
              <Text className="text-sm font-semibold">Deck Restoration</Text>
              <Text className="text-xs text-gray-600">
                789 Maple Dr, Austin, TX
              </Text>
            </View>
          </View>
          <View className="mt-4 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="mr-[6px] h-2 w-2 rounded-full bg-success"></View>
              <Text className="text-xs font-semibold text-success">Paid</Text>
            </View>
            <View className="text-xs text-gray-700">Due: Aug 3, 2023</View>
          </View>
          <View className="mt-4 flex-row justify-end gap-2">
            <Button size="xs" leftIcon={'download'} onPress={handleDownload}>
              Download
            </Button>
            <Button
              size="xs"
              variant={'primary'}
              // FIXME: replace with actual invoice id
              onPress={() => router.push('/vendor/invoice-detail/testId')}
            >
              View Details
            </Button>
          </View>
        </View>
      </CardWrapper>
    </View>
  )
}

export default VendorInvoicesPage
