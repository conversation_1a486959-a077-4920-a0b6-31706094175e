import { useState } from 'react'
import type { ColorValue } from 'react-native'
import { Pressable, ScrollView, Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'

import { Screen } from '@/components'
import { Segmented } from '@/components/Segmented'
import { Colors } from '@/theme/colors'
import classNames from '@/utils/classname'

const VendorSchedulePage = () => {
  const [value, setValue] = useState('day')

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="flex flex-col gap-4 p-4">
        <Segmented
          value={value}
          onChange={setValue}
          options={[
            { icon: 'calendar-day', label: 'Day', value: 'day' },
            { icon: 'calendar-week', label: 'Week', value: 'week' },
            { icon: 'calendar', label: 'Month', value: 'month' }
          ]}
        />
      </View>
      <View className="p-4">
        {value === 'day' && <CalendarOfDay />}
        {value === 'week' && <CalendarOfWeek />}
        {value === 'month' && <CalendarOfMonth />}
      </View>
    </Screen>
  )
}

const CalendarOfDay = () => {
  const [selected, setSelected] = useState('')
  const weekDays = ['Sun', 'Mon', 'Tue', 'Web', 'Thu', 'Fri', 'Sat']

  const meetingList = [
    {
      title: 'Kitchen Sink Install',
      type: 'work',
      location: '123 Park Ave',
      beginTime: 9,
      endTime: 10
    },
    {
      title: 'Kitchen Sink Install',
      type: 'meeting',
      location: '123 Park Ave',
      beginTime: 11,
      endTime: 12
    },
    {
      title: 'Kitchen Sink Install',
      type: 'work',
      location: '123 Park Ave',
      beginTime: 13,
      endTime: 15
    },
    {
      title: 'Kitchen Sink Install',
      type: 'deadline',
      location: '123 Park Ave',
      beginTime: 17,
      endTime: 18
    }
  ]

  const meetingBgColors: Record<
    string,
    readonly [ColorValue, ColorValue, ...ColorValue[]]
  > = {
    work: [Colors.warning, Colors['warning-light']],
    meeting: [Colors.primary, Colors['primary-light']],
    deadline: [Colors.success, Colors['success-light']]
  }

  const meetingDotColors: Record<string, ColorValue> = {
    work: Colors.warning,
    meeting: Colors.primary,
    deadline: Colors.success
  }

  return (
    <View>
      <View className="mb-4 flex-row items-center justify-between">
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-left"
            size={16}
            color={Colors.dark}
            className="font-[900]"
          />
        </Pressable>
        <View className="flex items-center justify-center">
          <Text className="text-3xl font-bold">July 7</Text>
          <Text className="text-sm text-gray">Friday, July 7, 2023</Text>
        </View>
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-right"
            color={Colors.dark}
            size={16}
            className="font-[900]"
          />
        </Pressable>
      </View>
      <View>
        {/* Week Overview */}
        <View className="mb-8 flex-row justify-between">
          {weekDays.map(day => {
            const isActive = day === 'Mon'

            return (
              <Pressable
                className={classNames(
                  'flex h-[45px] w-[45px] items-center justify-center rounded-full',
                  isActive ? 'bg-primary' : 'bg-[#f5f5f5]'
                )}
              >
                <Text
                  className={classNames(
                    isActive ? 'text-white' : '',
                    'text-xs'
                  )}
                >
                  {day}
                </Text>
                <Text
                  className={classNames(
                    isActive ? 'text-white' : '',
                    'text-sm font-semibold'
                  )}
                >
                  1
                </Text>
              </Pressable>
            )
          })}
        </View>

        {/* Filter Group */}
        <View className="mb-4">
          <RadioTagsGroup
            value={selected}
            onChange={setSelected}
            items={[
              {
                label: 'All',
                value: '',
                activeBgColor: Colors['primary-light'],
                activeColor: Colors.primary
              },
              {
                label: 'Site Work',
                value: 'work',
                activeBgColor: Colors['primary-light'],
                activeColor: Colors.primary,
                dotColor: Colors.warning
              },
              {
                label: 'Meeting',
                value: 'metting',
                activeBgColor: Colors['primary-light'],
                activeColor: Colors.primary,
                dotColor: Colors.primary
              },
              {
                label: 'Deadlines',
                value: 'deadlines',
                activeBgColor: Colors['primary-light'],
                activeColor: Colors.primary,
                dotColor: Colors.success
              }
            ]}
          />
        </View>

        <View className="flex-row">
          {/* Time */}
          <View
            className="mr-4 w-[50px] justify-between border-r"
            style={{
              borderRightColor: '#e0e0e0',
              borderStyle: 'dashed' //
            }}
          >
            {Array(12)
              .fill(8)
              .map((time, index) => {
                return (
                  <View className="h-[50px]">
                    <Text className="pr-2 text-right text-gray">
                      {time + index}:00
                    </Text>
                  </View>
                )
              })}
          </View>

          {/* Time line */}
          <View className="relative flex-1">
            {meetingList.map(item => {
              return (
                <View
                  className="absolute left-[-21px] h-[10px] w-[10px] rounded-full"
                  style={{
                    backgroundColor: meetingDotColors[item.type],
                    top: (item.beginTime - 8) * 50
                  }}
                />
              )
            })}
            {meetingList.map(item => {
              return (
                <Pressable
                  className="absolute w-full"
                  style={{
                    height: (item.endTime - item.beginTime) * 50,
                    top: (item.beginTime - 8) * 50
                  }}
                >
                  <LinearGradient
                    className="h-full w-full rounded-lg px-3 py-2"
                    // TODO: !
                    colors={meetingBgColors[item.type]!}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Text className="text-sm font-semibold text-white">
                      {item.title}
                    </Text>
                    <View className="flex-row justify-between">
                      <View className="flex-row items-center gap-1">
                        <FontAwesome6 name="map" color="#fff" />
                        <Text className="text-xs text-white">
                          {item.location}
                        </Text>
                      </View>
                      <Text className="text-xs text-white">
                        {item.beginTime}:00 - {item.endTime}:00
                      </Text>
                    </View>
                  </LinearGradient>
                </Pressable>
              )
            })}
          </View>
        </View>
      </View>
    </View>
  )
}

const CalendarOfWeek = () => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  const dates = ['2', '3', '4', '5', '6', '7', '8']
  const timeSlots = ['8 AM', '12 PM', '4 PM']

  const events = [
    {
      day: 1,
      time: '8 AM',
      title: 'Kitchen Sink',
      timeRange: '9:00-10:00',
      color: 'bg-orange-200 text-orange-800'
    },
    {
      day: 2,
      time: '8 AM',
      title: 'Bathroom Plumbing',
      timeRange: '9:00-11:00',
      color: 'bg-orange-200 text-orange-800'
    },
    {
      day: 3,
      time: '8 AM',
      title: 'Flooring Install',
      timeRange: '10:30-12:00',
      color: 'bg-orange-200 text-orange-800'
    },
    {
      day: 1,
      time: '12 PM',
      title: 'Client Meeting',
      timeRange: '11:30-12:30',
      color: 'bg-indigo-100 text-indigo-700'
    },
    {
      day: 2,
      time: '12 PM',
      title: 'PM Check-in',
      timeRange: '2:00-2:30',
      color: 'bg-indigo-100 text-indigo-700'
    },
    {
      day: 1,
      time: '4 PM',
      title: 'Living Room Paint',
      timeRange: '2:00-4:00',
      color: 'bg-orange-200 text-orange-800'
    },
    {
      day: 3,
      time: '4 PM',
      title: 'Site Cleanup',
      timeRange: '4:00-5:00',
      color: 'bg-green-100 text-green-700'
    }
  ]
  return (
    <View>
      <View className="mb-4 flex-row items-center justify-between">
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-left"
            size={16}
            color={Colors.dark}
            className="font-[900]"
          />
        </Pressable>
        <View className="flex items-center justify-center">
          <Text className="text-3xl font-bold">This Week</Text>
          <Text className="text-sm text-gray">Jul 2 - Jul 8, 2023</Text>
        </View>
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-right"
            color={Colors.dark}
            size={16}
            className="font-[900]"
          />
        </Pressable>
      </View>
      <ScrollView horizontal className="rounded-xl bg-white p-2">
        <View className="flex-row">
          {/* Left Time Labels */}
          <View className="w-16">
            <View className="h-24 justify-start">
              <Text className="text-gray-500 text-xs">Time</Text>
            </View>
            {timeSlots.map((slot, i) => (
              <View key={i} className="h-24 justify-start">
                <Text className="text-gray-500 text-xs">{slot}</Text>
              </View>
            ))}
          </View>

          {/* Calendar Grid */}
          <View className="flex-row">
            {days.map((day, i) => (
              <View key={i} className="w-24 border-l border-gray-200">
                {/* Header */}
                <View className="h-12 items-center justify-center">
                  <Text className="text-xs font-bold text-violet-600">
                    {day}
                  </Text>
                  <Text className="text-xs text-violet-600">{dates[i]}</Text>
                </View>

                {/* Time Slots */}
                {timeSlots.map((_, j) => (
                  <View key={j} className="h-24 border-t border-gray-100" />
                ))}

                {/* Events */}
                {events
                  .filter(e => e.day === i)
                  .map((event, idx) => {
                    const topOffset =
                      event.time === '8 AM'
                        ? 48
                        : event.time === '12 PM'
                          ? 48 + 96
                          : 48 + 96 * 2
                    return (
                      <View
                        key={idx}
                        className={`absolute left-0 right-0 mx-1 rounded-md p-1 ${event.color}`}
                        style={{ top: topOffset, height: 48 }}
                      >
                        <Text className="text-xs font-medium">
                          {event.title}
                        </Text>
                        <Text className="text-[10px]">{event.timeRange}</Text>
                      </View>
                    )
                  })}
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

const CalendarOfMonth = () => {
  const events = {
    '2024-06-02': [{ title: 'Sink Install', color: 'bg-orange-500' }],
    '2024-06-03': [
      { title: 'Kitchen', color: 'bg-orange-500' },
      { title: 'Meeting', color: 'bg-indigo-600' },
      { title: 'Paint', color: 'bg-orange-500' }
    ],
    '2024-06-04': [
      { title: 'Plumbing', color: 'bg-orange-500' },
      { title: 'Check-in', color: 'bg-indigo-600' }
    ],
    '2024-06-05': [
      { title: 'Flooring', color: 'bg-orange-500' },
      { title: 'Cleanup', color: 'bg-green-500' }
    ],
    '2024-06-10': [{ title: 'Inspection', color: 'bg-green-500' }],
    '2024-06-12': [{ title: 'Design Review', color: 'bg-indigo-600' }],
    '2024-06-15': [{ title: 'Deadline', color: 'bg-green-500' }],
    '2024-06-17': [{ title: 'New Project', color: 'bg-orange-500' }],
    '2024-06-24': [{ title: 'Client Call', color: 'bg-indigo-600' }]
  }

  function generateCalendar(year: number, month: number) {
    const date = new Date(year, month, 1)
    const firstDayIndex = date.getDay()
    const lastDay = new Date(year, month + 1, 0).getDate()

    const prevMonthLastDate = new Date(year, month, 0).getDate()
    const calendarDays = []

    for (let i = 0; i < 42; i++) {
      let day, currentMonth

      if (i < firstDayIndex) {
        day = prevMonthLastDate - (firstDayIndex - i - 1)
        currentMonth = month - 1
      } else if (i >= firstDayIndex + lastDay) {
        day = i - (firstDayIndex + lastDay) + 1
        currentMonth = month + 1
      } else {
        day = i - firstDayIndex + 1
        currentMonth = month
      }

      const thisYear =
        currentMonth < 0 ? year - 1 : currentMonth > 11 ? year + 1 : year
      const normalizedMonth = (currentMonth + 12) % 12

      const dateKey = `${thisYear}-${String(normalizedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      calendarDays.push({
        date: dateKey,
        day,
        currentMonth: normalizedMonth === month
      })
    }

    return calendarDays
  }

  const calendarDays = generateCalendar(2024, 5)

  return (
    <View>
      <View className="mb-4 flex-row items-center justify-between">
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-left"
            size={16}
            color={Colors.dark}
            className="font-[900]"
          />
        </Pressable>
        <View className="flex items-center justify-center">
          <Text className="text-3xl font-bold">June 2023</Text>
          <Text className="text-sm text-gray">Monthly overview</Text>
        </View>
        <Pressable className="flew-row h-10 w-10 items-center justify-center rounded-lg border border-border">
          <FontAwesome6
            name="chevron-right"
            color={Colors.dark}
            size={16}
            className="font-[900]"
          />
        </Pressable>
      </View>
      <ScrollView className="rounded-xl bg-white p-2">
        {/* Header */}
        <View className="mb-2 flex-row justify-around">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((d, i) => (
            <Text
              key={i}
              className="text-gray-500 w-[40px] text-center font-medium"
            >
              {d}
            </Text>
          ))}
        </View>

        {/* Calendar Grid */}
        <View className="flex-row flex-wrap">
          {calendarDays.map(({ date, day, currentMonth }, idx) => (
            <View
              key={idx}
              className={`m-[2px] h-[60px] w-[40px] rounded-md p-[2px] ${currentMonth ? 'bg-white' : 'bg-gray-100'}`}
            >
              <Text
                className={`mb-[2px] text-center text-xs ${currentMonth ? 'text-black' : 'text-gray-400'}`}
              >
                {day}
              </Text>
              {events[date as keyof typeof events]
                ?.slice(0, 3)
                .map((event, i: number) => (
                  <View
                    key={i}
                    className={`mb-[1px] rounded px-1 ${event.color}`}
                  >
                    <Text className="truncate text-[10px] text-white">
                      {event.title}
                    </Text>
                  </View>
                ))}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

const RadioTagsGroup = (props: {
  value: string
  onChange: (value: string) => void
  items: Array<{
    label: string
    value: string
    activeBgColor: string
    activeColor: string
    dotColor?: string
  }>
}) => {
  const { items, value, onChange } = props
  console.log('itemmmm', items)
  return (
    <ScrollView
      horizontal
      contentContainerClassName="flex flex-row items-center gap-[10px]"
      {...props}
    >
      {items.map(item => {
        const isActive = item.value === value
        return (
          <Pressable
            key={item.value}
            className={
              'flex flex-row items-center justify-center rounded-[20px] px-3 py-1.5'
            }
            style={{
              backgroundColor: isActive ? item.activeBgColor : '#f5f5f5'
            }}
            onPress={() => onChange?.(item.value)}
          >
            {item.dotColor && (
              <View
                className="mr-[6px] h-2 w-2 rounded-full"
                style={{
                  backgroundColor: item.dotColor
                }}
              />
            )}
            <Text
              className={'text-xs'}
              style={{
                color: isActive ? item.activeColor : Colors.dark
              }}
            >
              {item.label}
            </Text>
          </Pressable>
        )
      })}
    </ScrollView>
  )
}

export default VendorSchedulePage
