import { Platform, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { Tabs } from 'expo-router'

import { Colors } from '@/theme/colors'

import '../../../global.css'

const tabIcon =
  (name: string) =>
  ({ color }: { color: string }) => (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <FontAwesome6 name={name} size={22} color={color} />
    </View>
  )

export default function TabLayout() {
  // const colorScheme = useColorScheme()

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: '#6b7280',
        headerShown: false,
        tabBarShowLabel: true,
        tabBarLabelStyle: {
          fontSize: 14,
          marginTop: 2,
          marginBottom: 2,
          lineHeight: 16,
          textAlign: 'center'
        },
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
            height: 68,
            // paddingBottom: 8,
            paddingTop: 4
          },
          default: {
            height: 68,
            // paddingBottom: 8,
            paddingTop: 4
          }
        }),
        tabBarIconStyle: {
          marginTop: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }}
    >
      <Tabs.Screen
        name="dashboard"
        options={{
          title: 'Home',
          tabBarIcon: tabIcon('house')
        }}
      />
      <Tabs.Screen
        name="projects"
        options={{
          title: 'Projects',
          tabBarIcon: tabIcon('briefcase')
        }}
      />
      <Tabs.Screen
        name="schedule"
        options={{
          title: 'Schedule',
          tabBarIcon: tabIcon('calendar')
        }}
      />
      <Tabs.Screen
        name="invoices"
        options={{
          title: 'Invoices',
          tabBarIcon: tabIcon('file-invoice-dollar')
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: tabIcon('user')
        }}
      />
    </Tabs>
  )
}
