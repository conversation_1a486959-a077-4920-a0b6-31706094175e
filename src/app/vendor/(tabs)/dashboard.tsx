import { useEffect } from 'react'
import { View } from 'react-native'
import { format } from 'date-fns'

import { Screen } from '@/components'
import { DashboardUserHeader } from '@/components/DashboardUserHeader'
import AnalyticsCard from '@/components/vendor/dashboard/AnalyticsCard'
import { NewOpportunities } from '@/components/vendor/dashboard/NewOpportunities'
import { RecentActivity } from '@/components/vendor/dashboard/RecentActivity'
import { UpcomingList } from '@/components/vendor/dashboard/UpcomingList'
import { VendorOverviewCard } from '@/components/VendorOverviewCard'
import { useVendor } from '@/store'

const VendorDashboardPage = () => {
  const init = useVendor(state => state.init)
  const name = useVendor(state => state.name)
  const avatar = useVendor(state => state.avatar)
  const today = format(new Date(), 'MMMM d, yyyy')

  useEffect(() => {
    init()
  }, [init])

  return (
    <Screen safeAreaEdges={['top']} preset="scroll" backgroundColor="white">
      <View className="p-4">
        <View className="pb-4">
          <DashboardUserHeader
            title={`Hello, ${name}`}
            description={`Today is ${today}`}
            userAvatar={avatar}
          />
        </View>
        <VendorOverviewCard />
        <UpcomingList />
        <RecentActivity />
        <AnalyticsCard />
        <NewOpportunities />
      </View>
    </Screen>
  )
}

export default VendorDashboardPage
