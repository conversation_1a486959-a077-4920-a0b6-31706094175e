import React from 'react'
import { useState } from 'react'
import { ScrollView, View } from 'react-native'

import { AccountSettingsCard } from '@/components/property-owner/profile/AccountSettingsCard'
import { EditProfileModal } from '@/components/property-owner/profile/EditProfileModal'
import { Header } from '@/components/property-owner/profile/Header'
import { Personal } from '@/components/property-owner/profile/Personal'
import { PersonalInformation } from '@/components/property-owner/profile/PersonalInformationCard'
import { ActionSheetModal } from '@/components/property-owner/profile/PhotoActionSheetModal'
import { PortfolioStatsCard } from '@/components/property-owner/profile/PortfolioStatsCard'
import { SupportLegalCard } from '@/components/property-owner/profile/SupportLegalCard'
import { Colors } from '@/theme/colors'

export default function VendorProfilePage() {
  const [editVisible, setEditVisible] = useState(false)
  const [photoSheetVisible, setPhotoSheetVisible] = useState(false)
  const [profile, setProfile] = useState({
    name: '<PERSON>',
    phone: '(*************',
    location: 'Dallas, TX',
    focus: 'Residential Properties'
  })

  const handleChange = (field: string, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }))
  }

  // TODO: Implement specific image selection/take photo logic
  // eslint-disable-next-line unused-imports/no-unused-vars
  const handlePhotoAction = (action: 'choose' | 'take' | 'cancel') => {
    setPhotoSheetVisible(false)
    // TODO: Implement specific image selection/take photo logic
  }

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <Header onEdit={() => setEditVisible(true)} />
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <Personal
          name={profile.name}
          email="<EMAIL>"
          role="Property Owner"
          onChangePhoto={() => setPhotoSheetVisible(true)}
        />
        <PortfolioStatsCard />
        <PersonalInformation
          name={profile.name}
          phone={profile.phone}
          location={profile.location}
          focus={profile.focus}
        />
        <AccountSettingsCard />
        <SupportLegalCard />
      </ScrollView>
      <EditProfileModal
        visible={editVisible}
        name={profile.name}
        phone={profile.phone}
        location={profile.location}
        focus={profile.focus}
        onChange={handleChange}
        onClose={() => setEditVisible(false)}
        onCancel={() => setEditVisible(false)}
        onSave={() => setEditVisible(false)}
      />
      <ActionSheetModal
        visible={photoSheetVisible}
        onClose={() => setPhotoSheetVisible(false)}
        onAction={handlePhotoAction}
      />
    </View>
  )
}
