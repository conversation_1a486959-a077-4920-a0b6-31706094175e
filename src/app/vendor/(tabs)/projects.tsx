import { useMemo, useRef, useState } from 'react'
import { Text, View } from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import { useRequest } from 'ahooks'
import { Stack } from 'expo-router'
import { router } from 'expo-router'

import type { SearchParams } from '@/app/property-manager/(tabs)/projects'
import { colors } from '@/app/property-manager/(tabs)/projects'
import type { InfiniteScrollRef } from '@/components'
import { BorderCard, InfiniteScroll, RadioButtonGroup } from '@/components'
import { Button } from '@/components/Button'
import { Screen } from '@/components/Screen'
import { Search } from '@/components/Search'
import { getRequestColor } from '@/components/tenant/RequestCard'
import { client } from '@/services/api'
import { useDict } from '@/store'
import { Colors } from '@/theme/colors'
import type { Project } from '@/types'
import { formatDate } from '@/utils/formatDate'

// const projects = [
//   {
//     id: '1',
//     name: 'Fireplace Renovation',
//     type: 'new',
//     pm: '<PERSON>',
//     deadline: 'Aug 5, 2023',
//     address: '789 Elm St, Austin, TX'
//   },
//   {
//     id: '2',
//     name: 'Bathroom Renovation',
//     type: 'approved',
//     pm: '<PERSON>',
//     deadline: 'Aug 5, 2023',
//     address: '789 Elm St, Austin, TX'
//   },
//   {
//     id: '3',
//     name: 'Living Room Painting',
//     type: 'accepted',
//     pm: 'John Davis',
//     deadline: 'Aug 5, 2023',
//     address: '789 Elm St, Austin, TX'
//   },
//   {
//     id: '4',
//     name: 'Sunset Villa Renovation',
//     type: 'started',
//     pm: 'John Davis',
//     deadline: 'Aug 5, 2023',
//     address: '789 Elm St, Austin, TX'
//   },
//   {
//     id: '5',
//     name: 'Deck Restoration',
//     type: 'completed',
//     pm: 'John Davis',
//     deadline: 'Aug 5, 2023',
//     address: '789 Elm St, Austin, TX'
//   }
// ]

const VendorProjectsPage = () => {
  const [searchInput, setSearchInput] = useState('')
  const [searchText, setSearchText] = useState('')
  const { getDictItems } = useDict()
  const [selectedFilter, setSelectedFilter] = useState('all')
  const infiniteScrollRef = useRef<InfiniteScrollRef>(null)

  const projectStatusRequest = useRequest(() => getDictItems('PROJECT_STATUS'))

  const request = useRequest(
    async (page: number, pageSize: number, args?: SearchParams) => {
      const { data } = await client.POST('/api/v1/vendor/project/list', {
        body: {
          pageNum: page,
          pageSize,
          ...args
        }
      })
      return {
        data: data?.data?.list ?? [],
        hasMore: data?.data?.hasNextPage ?? false
      }
    },
    { manual: true, refreshDeps: [searchText, selectedFilter] }
  )
  const searchParams = useMemo<SearchParams>(() => {
    return {
      searchText,
      projectStatus:
        selectedFilter === 'all'
          ? undefined
          : (selectedFilter as SearchParams['projectStatus'])
    }
  }, [searchText, selectedFilter])

  const renderProject = (project: Project) => <ProjectCard project={project} />

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={[]}
      backgroundColor={Colors.white}
      contentContainerClass="flex-1"
    >
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'My Projects'
        }}
      />
      <View className="flex-1 p-4">
        <Search
          value={searchInput}
          onChange={setSearchInput}
          placeholder="Search projects..."
          onClear={() => {
            setSearchText('')
          }}
          onSearch={v => {
            setSearchText(v)
          }}
        />
        <RadioButtonGroup
          value={selectedFilter}
          className="my-4 flex-shrink-0 flex-grow-0"
          onChange={setSelectedFilter}
          items={[
            {
              label: 'All (12)',
              value: 'all',
              activeColor: Colors.white,
              activeBgColor: Colors.primary
            },
            ...(projectStatusRequest.data?.map(item => ({
              label: item.label!,
              value: item.code!,
              activeColor: colors[item.code! as keyof typeof colors]?.[0],
              activeBgColor: colors[item.code! as keyof typeof colors]?.[1]
            })) ?? [])
          ]}
        />

        <Button
          className="mb-5 w-full"
          variant="primary"
          leftIcon="plus"
          onPress={() => router.push('/vendor/project-create')}
        >
          Create New Project
        </Button>

        <InfiniteScroll<Project, SearchParams>
          ref={infiniteScrollRef}
          // initialLoad={false}
          renderItem={renderProject}
          requestArgs={searchParams}
          onRequest={request.runAsync}
          emptyText="No projects found."
          className="flex-1"
          containerClassName="gap-3"
          itemClassName="w-full"
          numColumns={1}
          pageSize={10}
        />
      </View>
    </Screen>
  )
}

const ProjectCard = ({ project }: { project: Project }) => {
  const [color, bgColor] = getRequestColor(project.status)
  return (
    <BorderCard color={color} position="left">
      <View className="mb-3 flex flex-row items-start justify-between">
        <View>
          <Text className="mb-1 text-base font-semibold">
            {project.projectName}
          </Text>
          <View className="flex-row items-center gap-2">
            <FontAwesome6 name="location-dot" color={Colors.gray} />
            <Text className="text-sm text-gray">
              {/* TODO: different project types have different addresses */}
              {project.vendorPropertyAddress}
            </Text>
          </View>
        </View>
        <View
          className="rounded-full px-2 py-1"
          style={{
            backgroundColor: bgColor
          }}
        >
          <Text
            className="text-sm"
            style={{
              backgroundColor: bgColor,
              color: color
            }}
          >
            {project.status}
          </Text>
        </View>
      </View>
      <View className="mb-3 flex-row items-center">
        <FontAwesome6 name="calendar-alt" size={14} color={Colors.gray} />
        <Text className="ml-2 mr-6 text-sm text-gray">
          Deadline: {formatDate(project.estimateCompleteDate)}
        </Text>
        <FontAwesome6 name="user" size={14} color={Colors.gray} />
        <Text className="ml-2 text-sm text-gray">
          PM: {project.propertyManager?.name}
        </Text>
      </View>
      <BorderCard
        color={color}
        position="left"
        className="mb-6"
        style={{
          backgroundColor: bgColor
        }}
      >
        <View
          className="flex-row items-center"
          style={{
            backgroundColor: bgColor
          }}
        >
          <FontAwesome6 name="circle-info" size={14} color={color} />
          {/* TODO: wrong field */}
          <Text className="ml-2 text-sm" style={{ color: color }}>
            {project.additionalNotes}
          </Text>
        </View>
      </BorderCard>
      <View className="flex items-center">
        <Button
          variant="primary"
          onPress={() =>
            router.push(`/vendor/project-detail/${project.projectId}`)
          }
        >
          View Details
        </Button>
      </View>
    </BorderCard>
    // <CardWrapper borderLeftColor={Colors.primary}>
    //   <View className="p-4">
    //     <View className="mb-3 flex-row justify-between">
    //       <View>
    //         <View className="text-base font-semibold">{name}</View>
    //         <View className="flex-row items-center">
    //           <FontAwesome6
    //             name="fa-map-marker-alt"
    //             className="mr-1"
    //             color={Colors.gray}
    //           />
    //           <Text className="text-sm text-gray">{address}</Text>
    //         </View>
    //       </View>
    //       <View
    //         className={classNames(
    //           'h-6 flex-row items-center justify-center rounded-full px-2',
    //           type === 'new' ? 'bg-[rgba(21,101,192,0.1)]' : '',
    //           type === 'approved' ? 'bg-[rgba(255,143,0,0.1)]' : '',
    //           type === 'accepted' ? 'bg-[rgba(2,136,209,0.1)]' : '',
    //           type === 'started' ? 'bg-[rgba(255,143,0,0.1)]' : '',
    //           type === 'completed' ? 'bg-[rgba(46,125,50,0.1)]' : ''
    //         )}
    //       >
    //         <Text
    //           className={classNames(
    //             'text-xs font-semibold',
    //             type === 'new' ? 'text-primary' : '',
    //             type === 'approved' ? 'text-warning' : '',
    //             type === 'accepted' ? 'text-info' : '',
    //             type === 'started' ? 'text-warning' : '',
    //             type === 'completed' ? 'text-success' : ''
    //           )}
    //         >
    //           {projectTags(type)}
    //         </Text>
    //       </View>
    //     </View>

    //     <View className="mb-1 flex-row gap-4">
    //       <View className="flex-row items-center gap-[6px]">
    //         <FontAwesome6 name="calendar-alt" />
    //         <Text className="text-xs text-gray">
    //           {type === 'completed' ? 'Completed:' : 'Deadline:'} {deadline}
    //         </Text>
    //       </View>
    //       <View className="flex-row items-center gap-[6px]">
    //         <FontAwesome6 name="user" />
    //         <Text className="text-xs text-gray">PM: {pm}</Text>
    //       </View>
    //     </View>

    //     {type === 'new' && (
    //       <View className="mt-3 flex-row items-center gap-2 rounded-lg bg-gray-100 p-3">
    //         <FontAwesome6 name="clipboard-list" color={Colors.primary} />
    //         <Text className="text-sm">Project needs quote</Text>
    //       </View>
    //     )}

    //     {type === 'approved' && (
    //       <View className="mt-3 flex-row items-center gap-2 rounded-lg bg-[rgba(255,143,0,0.08)] p-3">
    //         <FontAwesome6 name="circle-info" className="mr-2 text-warning" />
    //         <Text className="text-sm">
    //           Your quote has been approved. Action needed.
    //         </Text>
    //       </View>
    //     )}

    //     {type === 'accepted' && (
    //       <View className="mt-3 flex-row items-center gap-2 rounded-lg bg-[rgba(2,136,209,0.08)] p-3">
    //         <FontAwesome6 name="circle-info" className="mr-2 text-info" />
    //         <Text className="text-sm">Ready to start. Confirm in details.</Text>
    //       </View>
    //     )}

    //     {type === 'completed' && (
    //       <View className="mt-4 flex-row items-center bg-[rgba(46,125,50,0.05)] p-3">
    //         <Text className="mr-2">Client Rating:</Text>
    //         <Rating rating={4} />
    //       </View>
    //     )}

    //     <View className="mt-4 flex-row justify-end">
    //       <Button
    //         variant="primary"
    //         onPress={() => router.push(`/vendor/project-detail/${id}`)}
    //       >
    //         View Details
    //       </Button>
    //     </View>
    //   </View>
    // </CardWrapper>
  )
}

export default VendorProjectsPage
