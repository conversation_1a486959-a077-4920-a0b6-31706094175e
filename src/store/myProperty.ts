import { create } from 'zustand'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'

export type MyProperty = components['schemas']['PropertyInfo']

export type MyPropertyState = {
  ready: boolean
  myProperty: MyProperty | null
  getMyProperty: () => Promise<MyProperty | null>
}

// eslint-disable-next-line unused-imports/no-unused-vars
export const useMyProperty = create<MyPropertyState>((set, get) => ({
  ready: false,
  myProperty: null,
  async getMyProperty() {
    const { data } = await client.GET('/api/v1/tenant/property')
    if (data?.data) {
      set({
        ready: true,
        myProperty: data.data!
      })
      return data.data!
    }
    set({ ready: true })
    return null
  }
}))
