import { router } from 'expo-router'
import { Toast } from 'toastify-react-native'
import { create } from 'zustand'

import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { loadString, remove, saveString } from '@/utils/storage'

const TOKEN_STORAGE_KEY = 'user.token'
const USER_ROLE_STORAGE_KEY = 'user.role'

async function fetchMe() {
  const { data, error } = await client.GET('/api/v1/admin/user/me')
  if (!error) {
    const user = data!.data!
    return { user, error: null }
  }
  return { user: null, error }
}

type User = NonNullable<components['schemas']['SysUserVo']>

// type LoginType = 'password' | 'oauth'
export type UserRole =
  | 'property-manager'
  | 'property-owner'
  | 'tenant'
  | 'vendor'

export type APIRole = 'vendor' | 'pm' | 'property_owner' | 'tenant'

export type AuthState = {
  accessToken: string | null
  user: User | null
  userRole: UserRole | null
  ready: boolean
  loading: boolean
  language: string
  computed: {
    logged: boolean
    username?: string
    avatar?: string
  }
  init: () => Promise<[UserRole | null, User | null]>
  setUserRole: (role: UserRole) => void
  loginByPassword: (
    role: APIRole,
    username: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<[Error, null] | [null, User]>
  loginByPhone: (
    role: APIRole,
    phone: string,
    code: string
  ) => Promise<[Error, null] | [null, User]>
  loginByOauth: (
    provider: string,
    code: string,
    state: string
  ) => Promise<[Error, null] | [null, User]>
  logout: (showMsg?: boolean) => void
  refreshMe: () => Promise<User | null>
  updateMyInfo: (args: Omit<User, 'userId'>) => Promise<Error | null>
  updateMyPassword: (newPassword: string) => Promise<Error | null>
}

export const useAuth = create<AuthState>((set, get) => ({
  accessToken: null,
  user: null,
  userRole: null,
  ready: false,
  loading: false,
  language: 'en',
  computed: {
    get logged() {
      return Boolean(get().user)
    },
    get username() {
      return get().user?.userName
    },
    get avatar() {
      return get().user?.avatar
    }
  },
  async init() {
    set({ loading: true })
    const userRole = loadString(USER_ROLE_STORAGE_KEY) as UserRole | null
    if (userRole) {
      set({ userRole: userRole as UserRole })
    }
    const accessToken = loadString(TOKEN_STORAGE_KEY)
    if (accessToken) {
      set({ accessToken })
      const { user } = await fetchMe()
      if (user) {
        set({ user, ready: true, loading: false })
        return [userRole, user]
      }
      set({ accessToken: null })
      remove(TOKEN_STORAGE_KEY)
      remove(USER_ROLE_STORAGE_KEY)
    }
    set({ ready: true, loading: false })
    return [null, null]
  },
  setUserRole(role) {
    set({ userRole: role })
    saveString(USER_ROLE_STORAGE_KEY, role)
  },

  async loginByPassword(role, email, password, rememberMe = true) {
    const { error, data } = await client.POST('/api/v1/login', {
      // @ts-ignore
      body: { email, password, grantType: 'email', role }
    })
    if (!error) {
      if (rememberMe) {
        saveString(TOKEN_STORAGE_KEY, data.data!.token!)
      }
      set({ accessToken: data.data!.token })
      const userResp = await fetchMe()
      if (!userResp.error) {
        set({ user: userResp.user })
        return [null, userResp.user!]
      } else {
        set({ accessToken: null })
        remove(TOKEN_STORAGE_KEY)
      }
    }

    return [new Error(error!.message), null]
  },

  async loginByPhone(role, phone, code) {
    const { error, data } = await client.POST('/api/v1/login', {
      // @ts-ignore
      body: {
        phoneNumber: phone,
        verifyCode: code,
        grantType: 'phone',
        role
      }
    })
    if (!error) {
      saveString(TOKEN_STORAGE_KEY, data.data!.token!)
      set({ accessToken: data.data!.token })
      const userResp = await fetchMe()
      if (!userResp.error) {
        set({ user: userResp.user })
        return [null, userResp.user!]
      } else {
        set({ accessToken: null })
        remove(TOKEN_STORAGE_KEY)
      }
    }

    return [new Error(error!.message), null]
  },
  // eslint-disable-next-line unused-imports/no-unused-vars
  async loginByOauth(provider, code, state) {
    return [null, {} as User]
  },
  async refreshMe() {
    const { user } = await fetchMe()
    if (user) {
      set({ user })
      return user
    }
    get().logout(false)
    return null
  },
  logout(showMsg = true) {
    remove(TOKEN_STORAGE_KEY)
    remove(USER_ROLE_STORAGE_KEY)
    set({ accessToken: null, user: null, userRole: null })
    if (showMsg) {
      Toast.success('You have successfully logged out')
    }
    router.dismissAll()
    router.replace('/role-selection')
  },
  // eslint-disable-next-line unused-imports/no-unused-vars
  async updateMyInfo(args) {
    if (!get().computed.logged) {
      return null
    }
    // eslint-disable-next-line unused-imports/no-unused-vars
    const { user } = get()
    // const { error } = await client.mutate({
    //   operationName: 'user/casdoor/updateUser',
    //   input: {
    //     userId: user!.userId!,
    //     ...args
    //   }
    // })
    // if (!error) {
    //   set({ user: { ...user!, ...args } })
    //   Toast.success('Update success')
    //   return true
    // }
    return null
  },
  // eslint-disable-next-line unused-imports/no-unused-vars
  async updateMyPassword(newPwd) {
    // eslint-disable-next-line unused-imports/no-unused-vars
    const { user, logout } = get()
    // const { error } = await client.mutate({
    //   operationName: 'user/casdoor/updateUser',
    //   input: {
    //     userId: user!.userId!,
    //     password: newPwd
    //   }
    // })
    // if (!error) {
    //   Toast.success('Password updated, please login again')
    //   setTimeout(() => {
    //     logout(false)
    //   }, 2000)
    //   return null
    // } else {
    //   Toast.error('Update failed')
    //   return false
    // }
    return null
  }
}))
