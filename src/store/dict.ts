import { create } from 'zustand'

import { client } from '@/services/api'
import type { DictItemType, DictType } from '@/types'

export type DictState = {
  cachedTypes: Map<string, DictItemType[]>
  getDictItems: (type: DictType) => Promise<DictItemType[]>
}

export const useDict = create<DictState>((set, get) => ({
  cachedTypes: new Map<string, DictItemType[]>(),
  getDictItems: async type => {
    const cache = get().cachedTypes
    const cached = cache.get(type)
    if (cached) {
      return cached
    }
    const { data } = await client.GET('/api/v1/dictionaries/{type}', {
      params: { path: { type } }
    })
    const items = data?.data || []
    cache.set(type, items)
    set({ cachedTypes: cache })
    return items
  }
}))
