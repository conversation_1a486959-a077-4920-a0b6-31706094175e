import { create } from 'zustand'

export type SelectedVendor = {
  vendorId: number
  vendorName: string
  vendorAvatar?: string
}

export type MyPropertyState = {
  selectedVendors: SelectedVendor[] | null
  setSelectedVendors: (v: SelectedVendor[] | null) => void
  clearSelected: VoidFunction
}

// eslint-disable-next-line unused-imports/no-unused-vars
export const useSelectVendor = create<MyPropertyState>((set, get) => ({
  selectedVendors: null,
  setSelectedVendors(v) {
    set({ selectedVendors: v })
  },
  clearSelected() {
    set({ selectedVendors: null })
  }
}))
