import { create } from 'zustand'

import { client } from '@/services/api'

export type PMStatState = {
  loading: boolean
  totalPropertyCount: number
  activeProjectCount: number
  pendingQuotesCount: number
  completeThisMonthProjectCount: number
  vendorCount: number
  refresh: () => Promise<void>
}

// eslint-disable-next-line unused-imports/no-unused-vars
export const usePMStat = create<PMStatState>((set, get) => ({
  loading: false,
  totalPropertyCount: 0,
  activeProjectCount: 0,
  pendingQuotesCount: 0,
  completeThisMonthProjectCount: 0,
  vendorCount: 0,
  async refresh() {
    set({ loading: true })
    const { data } = await client.GET('/api/v1/pm/property/stat')
    if (data?.data) {
      set({
        loading: false,
        totalPropertyCount: data.data.totalPropertyCount!,
        activeProjectCount: data.data.activeProjectCount!,
        pendingQuotesCount: data.data.pendingQuotesCount!,
        completeThisMonthProjectCount: data.data.completeThisMonthProjectCount!,
        vendorCount: data.data.vendorCount!
      })
    } else {
      set({ loading: true })
    }
  }
}))
