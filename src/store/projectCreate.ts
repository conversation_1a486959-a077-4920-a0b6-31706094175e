import { create } from 'zustand'
import { useRequest } from 'ahooks'
import { Toast } from 'toastify-react-native'
import { router } from 'expo-router'

import { useFormStorage } from '@/hooks/useFormStorage'
import { client } from '@/services/api'
import type { components } from '@/services/api/schema'
import { useSelectVendor } from '@/store/selectVendor'
import type { ProjectWorkType } from '@/types'
import type { PropertyType } from '@/types/property'
import type { AddedItem } from '@/components/property-manager/projects/create/types'

export type ProjectFormData = Omit<
  components['schemas']['ProjectInfo'],
  'projectType' | 'estimateStartDate' | 'estimateCompleteDate'
> & {
  projectType: ProjectWorkType
  selectedProperty?: PropertyType
  estimateStartDate: Date | null
  estimateCompleteDate: Date | null
}

export type SelectedVendor = components['schemas']['VendorSelectDTO']

const initialFormData: ProjectFormData = {
  projectId: undefined,
  propertyId: 0,
  managerId: undefined,
  ownerId: undefined,
  projectType: 'REHAB',
  priority: 'MEDIUM',
  affectedUnits: '',
  description: '',
  projectName: '',
  emptyRehabVendorId: undefined,
  estimateStartDate: null,
  estimateCompleteDate: null,
  estimateBudget: undefined,
  additionalNotes: '',
  items: [],
  selectedProperty: undefined
}

interface ProjectCreateState {
  // Form data
  formData: ProjectFormData
  editingItem: AddedItem | null
  properties: PropertyType[]
  selectedProperty: PropertyType | undefined
  selectedVendor: SelectedVendor | undefined
  currentStep: number
  isLoaded: boolean

  // Actions
  updateFormData: (data: Partial<ProjectFormData>) => void
  resetFormData: () => void
  clearFormData: () => void
  setEditingItem: (item: AddedItem | null) => void
  setProperties: (properties: PropertyType[]) => void
  selectVendor: (vendor: SelectedVendor | undefined) => void
  setCurrentStep: (step: number) => void
  handleBack: () => void
  handleContinue: () => void
  saveDraft: () => void

  // Reset store (call when entering create page)
  resetStore: () => void
}

export const useProjectCreateStore = create<ProjectCreateState>((set, get) => ({
  // Initial state
  formData: initialFormData,
  editingItem: null,
  properties: [],
  selectedProperty: undefined,
  selectedVendor: undefined,
  currentStep: 0,
  isLoaded: true,

  // Actions
  updateFormData: (data) => {
    set((state) => ({
      formData: { ...state.formData, ...data }
    }))
  },

  resetFormData: () => {
    set({ formData: initialFormData })
  },

  clearFormData: () => {
    set({ formData: initialFormData })
  },

  setEditingItem: (item) => {
    set({ editingItem: item })
  },

  setProperties: (properties) => {
    set({ properties })
    // Update selected property based on formData.propertyId
    const { formData } = get()
    const selectedProperty = properties.find(
      property => property.propertyId === formData.propertyId
    )
    set({ selectedProperty })
  },

  selectVendor: (vendor) => {
    set({ selectedVendor: vendor })
  },

  setCurrentStep: (step) => {
    set({ currentStep: step })
  },

  handleBack: () => {
    const { currentStep } = get()
    if (currentStep > 1) {
      set({ currentStep: currentStep - 1 })
    }
    // Note: router.back() should be handled in the component
  },

  handleContinue: () => {
    const { currentStep } = get()
    if (currentStep < 3) {
      set({ currentStep: currentStep + 1 })
    }
  },

  saveDraft: async () => {
    const result = await useProjectSubmit().runAsync('DRAFT')
    if (result?.success) {
      router.back()
    }
  },

  resetStore: () => {
    set({
      formData: initialFormData,
      editingItem: null,
      properties: [],
      selectedProperty: undefined,
      selectedVendor: undefined,
      currentStep: 0,
      isLoaded: true
    })
  }
})))

// Hook for submit functionality (separate because it uses useRequest)
export const useProjectSubmit = () => {
  const { clearSelected } = useSelectVendor()
  const { formData, selectedVendor, clearFormData } = useProjectCreateStore()

  return useRequest(
    async (status: 'DRAFT' | 'SUBMITTED' = 'SUBMITTED') => {
      const { estimateStartDate, estimateCompleteDate, ...rest } = {
        ...formData
      }
      const body: components['schemas']['ProjectInfo'] = rest
      
      // Remove temp item id
      if (body.items?.length) {
        for (const item of body.items) {
          if (item.itemId && item.itemId < 0) {
            item.itemId = undefined
          }
        }
      }
      
      // Selected vendor
      if (selectedVendor) {
        body.assignedVendorIds = `${selectedVendor.vendorId}`
      }
      
      // Date format
      body.estimateStartDate = estimateStartDate?.toISOString()
      body.estimateCompleteDate = estimateCompleteDate?.toISOString()
      body.status = status
      
      const { data, error } = await client.POST('/api/v1/pm/project/add', {
        body
      })
      
      if (!error) {
        clearFormData()
        Toast.success(
          status === 'DRAFT' ? 'Project draft saved' : 'Project submitted'
        )
        return { success: true, projectId: data!.data! }
      }
      return { success: false, projectId: undefined }
    },
    {
      manual: true,
      onSuccess(result) {
        if (result?.success && !result.projectId) {
          clearSelected()
        }
      }
    }
  )
}
