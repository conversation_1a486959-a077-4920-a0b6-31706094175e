import { writeFileSync } from 'fs'
import fetch from 'node-fetch'

const API_BASE_URL =
  'http://ae5c04cd9431c415bb543e735afcf4ae-858245834.us-east-1.elb.amazonaws.com/api/v1/dictionaries'
const TYPES_URL = `${API_BASE_URL}/types`
const ALL_URL = `${API_BASE_URL}/all`

async function main() {
  try {
    // Fetch dictionary types from API
    console.log('Fetching dictionary types...')
    const typesResponse = await fetch(TYPES_URL)
    const typesResult = await typesResponse.json()

    if (!typesResult.data || !Array.isArray(typesResult.data)) {
      throw new Error('Invalid types API response format')
    }

    // Extract dictionary types
    const types = typesResult.data
    console.log(`Found ${types.length} dictionary types:`, types)

    // Fetch all dictionary items
    console.log('Fetching all dictionary items...')
    const allResponse = await fetch(ALL_URL)
    const allResult = await allResponse.json()

    if (!allResult.data || !Array.isArray(allResult.data)) {
      throw new Error('Invalid all items API response format')
    }

    // Group dictionary items by type
    const itemsByType = {}
    allResult.data.forEach(item => {
      if (!item.type || !item.code) {
        console.warn('Skipping item with missing type or code:', item)
        return
      }

      if (!itemsByType[item.type]) {
        itemsByType[item.type] = []
      }
      itemsByType[item.type].push(item.code)
    })

    console.log(
      'Grouped items by type:',
      Object.keys(itemsByType).map(
        type => `${type}: ${itemsByType[type].length} items`
      )
    )

    // Generate union types for each dictionary type
    const unionTypes = Object.keys(itemsByType)
      .map(type => {
        const codes = [...new Set(itemsByType[type])].sort() // Remove duplicates and sort
        // Replace hyphens with underscores for valid TypeScript type names
        const typeName = `DICT_ITEM_${type.toUpperCase().replace(/-/g, '_')}`
        const unionType = codes.map(code => `'${code}'`).join(' | ')
        return `export type ${typeName} = ${unionType}`
      })
      .join('\n\n')

    // Generate TypeScript type definition
    const typeContent = `import type { components } from '@/services/api/schema'

export type DictItemType = components['schemas']['DictionaryItem']

export type DictType = ${types.map(t => `'${t}'`).join(' | ')}

// Dictionary item union types by type
${unionTypes}
`

    // Write to file
    writeFileSync('src/types/dict.ts', typeContent, 'utf-8')
    console.log('Successfully generated dictionary types with union types')
    console.log(
      `Generated union types for: ${Object.keys(itemsByType).join(', ')}`
    )
  } catch (error) {
    console.error('Error generating dictionary types:', error)
    process.exit(1)
  }
}

main()
