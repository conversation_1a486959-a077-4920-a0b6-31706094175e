/** @type {import("prettier").Config} */
const config = {
  arrowParens: 'avoid',
  // endOfLine: 'auto',
  endOfLine: 'lf',
  semi: false,
  singleQuote: true,
  quoteProps: 'consistent',
  trailingComma: 'none',
  jsxSingleQuote: false,
  plugins: [
    'prettier-plugin-tailwindcss'
    // '@ianvs/prettier-plugin-sort-imports'
    // '@trivago/prettier-plugin-sort-imports'
  ]
  // importOrder: [
  //   '^path$',
  //   '^vite$',
  //   '^@vitejs/(.*)$',
  //   '^react$',
  //   '^react-dom/client$',
  //   '^react/(.*)$',
  //   '^globals$',
  //   '^zod$',
  //   '^axios$',
  //   '^date-fns$',
  //   '^js-cookie$',
  //   '^react-hook-form$',
  //   '^use-intl$',
  //   '^@radix-ui/(.*)$',
  //   '^@hookform/resolvers/zod$',
  //   '^@tanstack/react-query$',
  //   '^@tanstack/react-router$',
  //   '^@tanstack/react-table$',
  //   '^@tabler/icons-react$',
  //   '<THIRD_PARTY_MODULES>',
  //   '^@/assets/(.*)',
  //   '^@/api/(.*)$',
  //   '^@/stores/(.*)$',
  //   '^@/lib/(.*)$',
  //   '^@/utils/(.*)$',
  //   '^@/constants/(.*)$',
  //   '^@/context/(.*)$',
  //   '^@/hooks/(.*)$',
  //   '^@/components/layouts/(.*)$',
  //   '^@/components/ui/(.*)$',
  //   '^@/components/errors/(.*)$',
  //   '^@/components/(.*)$',
  //   '^@/features/(.*)$',
  //   '^[./]'
  // ]
}

export default config
