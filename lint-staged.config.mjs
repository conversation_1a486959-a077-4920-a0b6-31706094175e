/**
 * @filename lint-staged.config.js
 * @type {import('lint-staged').Configuration}
 */
export default {
  '*.{js,jsx,ts,tsx,json,md}': [
    'eslint --fix --ignore-pattern .gitignore --ignore-pattern ui/paibox-ui'
  ],
  // '*.{json,yaml}': ['prettier --write'],
  '**/*.ts?(x)': 'bash -c "npm run types:check"',
  '*.{css,less,sass,scss,styl}': [
    // FIXME
    // 'stylelint --config ./stylelint.config.js --allow-empty-input -i ./.stylelintignore --fix'
  ]
}
