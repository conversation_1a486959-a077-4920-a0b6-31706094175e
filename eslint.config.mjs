// @ts-check
import eslint from '@eslint/js'
// import prettierPlugin from 'eslint-plugin-prettier/recommended'
import prettierPlugin from 'eslint-plugin-prettier'
import reactPlugin from 'eslint-plugin-react'
import reactHooksPlugin from 'eslint-plugin-react-hooks'
import simpleImportSortPlugin from 'eslint-plugin-simple-import-sort'
import unusedImportsPlugin from 'eslint-plugin-unused-imports'
import globals from 'globals'
import tseslint from 'typescript-eslint'

import prettierConfig from './prettier.config.mjs'

export default tseslint.config(
  {
    ignores: [
      '.history',
      'android',
      'dist',
      'ios',
      'expo-env.d.ts',
      'node_modules',
      'package-lock.json',
      'pnpm-lock.yaml'
    ]
  },
  eslint.configs.recommended,
  tseslint.configs.recommended,
  {
    settings: {
      react: {
        version: 'detect'
      }
    },
    languageOptions: {
      ...reactPlugin.configs.flat.recommended.languageOptions,
      globals: {
        ...globals.browser,
        ...globals.es2025,
        ...globals.jest,
        ...globals.node,
        ...globals.vitest,
        ...globals.devtools,
        ...globals.serviceworker
      }
    },
    plugins: {
      'react': reactPlugin,
      'react-hooks': reactHooksPlugin,
      'unused-imports': unusedImportsPlugin,
      'prettier': prettierPlugin,
      'simple-import-sort': simpleImportSortPlugin
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/consistent-type-imports': 'warn',
      '@typescript-eslint/no-empty-object-type': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'simple-import-sort/imports': [
        'error',
        {
          groups: [
            // Node.js built-in modules
            ['^node:.*'],
            // React and other external packages (no empty line between them)
            ['^react$', '^react-.*', '^@react-.*', '^@?\\w'],
            // Internal imports
            ['^@/.*'],
            // Relative imports
            ['^\\.\\./', '^\\./'],
            // Style imports
            ['^.+\\.?(css)$']
          ]
        }
      ],
      'simple-import-sort/exports': 'error',
      'prettier/prettier': ['error', prettierConfig],
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_'
        }
      ]
    }
  }
  // NOTE: Conflict with simple-import-sort
  // prettierPluginRecommended
)
