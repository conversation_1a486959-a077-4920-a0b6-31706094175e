# Ignite Project Rules and Guidelines

## Project Structure
- Use English for all code, comments, and documentation
- Follow Ignite's folder structure conventions
- Keep components modular and reusable
- Use TypeScript for type safety
- The main code are located in `/src` folder

## App structure
- `/src` - Main application code directory
  - `/app` - Pages and layouts
  - `/components` - Reusable UI components
  - `/store` - Zustand state store
  - `/services` - API services and external integrations
  - `/utils` - Utility functions and helper methods
  - `/theme` - Theme configuration (colors, fonts, spacing, etc.)
  - `/i18n` - Internationalization files
  - `/hooks` - Custom React Hooks
  - `/constants` - Constant definitions
  - `/types` - TypeScript type definitions
- `/assets` - Static assets (images, fonts, etc.)
  - `icons` 
  - `images`

## Component Guidelines
- Use Ignite's built-in components when possible:
  - AutoImage
  - Button
  - Card
  - Checkbox
  - EmptyState
  - Header
  - Icon
  - ListItem
  - ListView
  - Radio
  - Screen
  - Switch
  - Text
  - TextField

## State Management
- Use Zustand for global state management
- Keep state logic separate from UI components
- Use stores for complex state management
- Implement proper state persistence with MMKV

## Component development
- Use nativewind to control UI style but not StyleSheet
- Nativewind means tailwind in react-native
- Use `import classNames from '@/utils/classNames' to merge nativewind classNames, don't use string template
- When creating/updating a component, creating/updating a related test file under `src/components/__tests__`
- You must always use English comment in components and their test files

## Code Generation
- Use `npx ignite-cli generate {templateName} {targetComponentName}` to generate files from templates in `ignite/templates` directory which are coding in `eta` template format
- When using `ignite/templates` files you must generate from `ignite-cli` command cli
- Generate both component and test files
- Include proper TypeScript types
- Use `@/` to alias `/src` root path

## Testing
- Write unit tests using Jest
- Implement E2E tests with Maestro
- Maintain good test coverage
- Follow testing best practices

## Internationalization
- Use Expo Localization for i18n
- Keep all text in translation files
- Support RTL layouts
- Default to English for new content

## UI/UX Guidelines
- Maintain clean and consistent UI
- Use proper spacing and typography
- Implement smooth animations with RN Reanimated
- Follow platform-specific design guidelines

## Performance
- Use FlashList instead of FlatList
- Implement proper keyboard handling
- Optimize images and assets
- Use Hermes engine features

## Documentation
- Reference: https://docs.infinite.red/ignite-cli/
- Document complex logic
- Keep README up to date
- Include component documentation

## Git Workflow
- Write clear commit messages in English
- Follow conventional commits
- Keep commits focused and atomic
- Review code before committing

## Dependencies
- React Native v0.76
- React v18
- TypeScript v5
- React Navigation v7
- Zustand v5
- Expo v52
- Expo Font v13
- Expo Localization v16
- Expo Status Bar v2
- RN Reanimated v3
- MMKV v2
- apisauce v2
- Reactotron RN v3
- Jest v29
- date-fns v4
- react-native-keyboard-controller v1
- FlashList v1
- Nativewind v4

## Code Style
- Follow ESLint rules
- Use Prettier for formatting
- Maintain consistent naming conventions
- Write clean, readable code 