import type { ImageStyle, TextStyle, ViewStyle } from 'react-native'
import { Image, View } from 'react-native'

import { Screen, Text } from '@/components'
import { isRTL } from '@/i18n'
import type { ThemedStyle } from '@/theme'
import { useAppTheme } from '@/utils/useAppTheme'
import { useSafeAreaInsetsStyle } from '@/utils/useSafeAreaInsetsStyle'

const welcomeLogo = require('assets/images/logo.png')
const welcomeFace = require('assets/images/welcome-face.png')

export default function WelcomeScreen() {
  const $bottomContainerInsets = useSafeAreaInsetsStyle(['bottom'])
  const { theme, themed } = useAppTheme()

  return (
    <Screen safeAreaEdges={['top']} contentContainerStyle={themed($container)}>
      <View style={themed($topContainer)}>
        <Image
          style={themed($welcomeLogo)}
          source={welcomeLogo}
          resizeMode="contain"
        />
        <Text
          testID="welcome-heading"
          style={themed($welcomeHeading)}
          tx="welcomeScreen:readyForLaunch"
          preset="heading"
        />
        <Text tx="welcomeScreen:exciting" preset="subheading" />
        <Image
          style={$welcomeFace}
          source={welcomeFace}
          resizeMode="contain"
          tintColor={theme.isDark ? theme.colors.palette.neutral900 : undefined}
        />
      </View>

      <View style={[themed($bottomContainer), $bottomContainerInsets]}>
        <Text tx="welcomeScreen:postscript" size="md" />
      </View>
    </Screen>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background
})

const $topContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexShrink: 1,
  flexGrow: 1,
  flexBasis: '57%',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg
})

const $bottomContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexShrink: 1,
  flexGrow: 0,
  flexBasis: '43%',
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  paddingHorizontal: spacing.lg,
  justifyContent: 'space-around'
})

const $welcomeLogo: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 88,
  width: '100%',
  marginBottom: spacing.xxl
})

const $welcomeFace: ImageStyle = {
  height: 169,
  width: 269,
  position: 'absolute',
  bottom: -47,
  right: -80,
  transform: [{ scaleX: isRTL ? -1 : 1 }]
}

const $welcomeHeading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.md
})
