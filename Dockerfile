# Build stage
# FROM docker.1ms.run/library/node:lts-alpine3.20 AS builder
FROM node:lts-alpine3.20 AS builder

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@10

# Copy package.json and pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# Install dependencies
# --registry https://registry.npmmirror.com
RUN pnpm install

# Copy the rest of the code
COPY . .

# Build web bundle
RUN pnpm bundle:web

# Deploy stage
# FROM docker.1ms.run/library/nginx:alpine

# Copy nginx configuration
# COPY nginx.conf /etc/nginx/conf.d/default.conf
# COPY nginx.conf /etc/nginx/conf.d/configfile.template

# Copy build artifacts from builder stage
# COPY --from=builder /app/dist /usr/share/nginx/html

ENV \
  PORT=3000 \
  HOST=0.0.0.0

# Expose port
EXPOSE 3000

# Start web server
CMD ["pnpm", "serve:web"]
# Start nginx
# CMD ["nginx", "-g", "daemon off;"]
# CMD sh -c "envsubst '\$PORT' < /etc/nginx/conf.d/configfile.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

# build #
# docker build --progress=plain --no-cache -t paibox-app:0.1.0 -f Dockerfile .
# docker build --no-cache -t paibox-app:0.1.0 .
# docker build --no-cache . -t paibox-app:0.1.0

# run #
# docker run -d -p 3000:3000 paibox-app:0.1.0
# docker logs <CONTAINER ID> -f
